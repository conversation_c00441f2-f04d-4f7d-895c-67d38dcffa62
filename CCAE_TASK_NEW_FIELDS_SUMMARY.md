# CCAE任务表新增字段功能说明

## 概述
为WOC_CCAE_TASK表新增了两个字段，用于存储计算节点列表和网络节点列表信息。

## 新增字段

### 1. COMPUTE_LIST (计算节点列表)
- **数据类型**: CLOB
- **存储格式**: JSON数组
- **用途**: 存储计算节点的SN序列号列表
- **示例**: `["SN001", "SN002", "SN003"]`

### 2. NETWORK_IP_LIST (网络节点列表)
- **数据类型**: CLOB
- **存储格式**: JSON数组
- **用途**: 存储网络节点的IP地址列表
- **示例**: `["*************", "*************", "*************"]`

### 3. STATUS (逻辑删除状态)
- **数据类型**: NUMBER(1,0)
- **默认值**: 1
- **用途**: 逻辑删除标识，1表示未删除，0表示已删除
- **MyBatis-Plus支持**: 使用@TableLogic注解自动处理

## 代码层面的修改

### 1. 数据库表结构 (1.0.sql)
```sql
-- 新增字段
COMPUTE_LIST CLOB NULL,
NETWORK_IP_LIST CLOB NULL,
STATUS NUMBER(1,0) DEFAULT 1 NOT NULL,

-- 字段注释
COMMENT ON COLUMN SLGZT.WOC_CCAE_TASK.COMPUTE_LIST IS '计算节点列表(SN序列号)，JSON格式存储';
COMMENT ON COLUMN SLGZT.WOC_CCAE_TASK.NETWORK_IP_LIST IS '网络节点列表(IP列表)，JSON格式存储';
COMMENT ON COLUMN SLGZT.WOC_CCAE_TASK.STATUS IS '状态：1-未删除，0-已删除';
```

### 2. DO对象 (CcaeTaskDO.java)
```java
/**
 * 计算节点列表(SN序列号)
 */
@TableField(value = "COMPUTE_LIST", typeHandler = JacksonTypeHandler.class)
private List<String> computeList;

/**
 * 网络节点列表(IP列表)
 */
@TableField(value = "NETWORK_IP_LIST", typeHandler = JacksonTypeHandler.class)
private List<String> networkIpList;
```

### 3. DTO对象 (CcaeTaskDTO.java)
```java
/**
 * 计算节点列表(SN序列号)
 */
private List<String> computeList;

/**
 * 网络节点列表(IP列表)
 */
private List<String> networkIpList;

/**
 * 状态：1-未删除，0-已删除
 */
@TableLogic(value = "1", delval = "0")
@TableField("STATUS")
private Integer status;
```

### 4. Query对象 (CcaeTaskQuery.java)
```java
/**
 * 计算节点SN序列号（用于查询包含指定SN的任务）
 */
private String computeSn;

/**
 * 网络IP地址（用于查询包含指定IP的任务）
 */
private String networkIp;

/**
 * 状态：1-未删除，0-已删除
 */
private Integer status;

/**
 * 状态列表
 */
private List<Integer> statusList;

/**
 * 是否包含已删除数据（默认false，只查询未删除数据）
 */
private Boolean includeDeleted;
```

### 5. DAO查询支持 (CcaeTaskDAO.java)
```java
// 查询包含指定计算节点SN的任务
.apply(StringUtils.isNotBlank(query.getComputeSn()), 
       "JSON_CONTAINS(COMPUTE_LIST, JSON_QUOTE({0}))", query.getComputeSn())
// 查询包含指定网络IP的任务
.apply(StringUtils.isNotBlank(query.getNetworkIp()), 
       "JSON_CONTAINS(NETWORK_IP_LIST, JSON_QUOTE({0}))", query.getNetworkIp())
```

## 技术特性

### JSON存储和查询
- 使用MyBatis-Plus的`JacksonTypeHandler`自动处理Java List与JSON的转换
- 支持达梦数据库的JSON查询函数`JSON_CONTAINS`
- 自动序列化/反序列化，无需手动处理JSON格式
- 使用`List<String>`比数组更加灵活，支持动态添加/删除元素

### 查询功能
- **精确匹配查询**: 可以查询包含特定SN或IP的任务
- **数组元素查询**: 支持在JSON数组中查找特定元素
- **组合查询**: 可以与其他查询条件组合使用
- **逻辑删除查询**: 自动过滤已删除数据，支持状态查询

### 逻辑删除功能
- **自动过滤**: MyBatis-Plus自动在查询中添加status=1条件
- **软删除**: 删除操作只更新status字段，不物理删除数据
- **数据恢复**: 支持恢复已删除的数据
- **状态查询**: 可以根据状态查询特定状态的数据

## 使用示例

### 1. 创建任务时设置节点信息
```java
CcaeTaskDTO task = new CcaeTaskDTO();
task.setCcaeId("task-001");
task.setComputeList(Arrays.asList("SN001", "SN002", "SN003"));
task.setNetworkIpList(Arrays.asList("*************", "*************"));
ccaeTaskService.createTask(task);
```

### 2. 查询包含特定节点的任务
```java
// 查询包含特定计算节点的任务
CcaeTaskQuery query = new CcaeTaskQuery();
query.setComputeSn("SN002");
List<CcaeTaskDTO> tasks = ccaeTaskService.listTasks(query);

// 查询包含特定网络IP的任务
CcaeTaskQuery query2 = new CcaeTaskQuery();
query2.setNetworkIp("*************");
List<CcaeTaskDTO> tasks2 = ccaeTaskService.listTasks(query2);
```

### 3. 逻辑删除操作
```java
// 逻辑删除任务
ccaeTaskService.logicDeleteTask(taskId);

// 根据CCAE任务ID逻辑删除
ccaeTaskService.logicDeleteTaskByCcaeId("task-001");

// 恢复已删除的任务
ccaeTaskService.restoreTask(taskId);

// 查询特定状态的任务
CcaeTaskQuery statusQuery = new CcaeTaskQuery();
statusQuery.setStatus(1); // 查询未删除的任务
List<CcaeTaskDTO> activeTasks = ccaeTaskService.listTasks(statusQuery);
```

## 数据库迁移

### 迁移脚本
提供了迁移脚本 `add_ccae_task_node_fields.sql` 用于在现有数据库中添加新字段和逻辑删除字段。

### 执行步骤
1. 备份现有数据
2. 执行迁移脚本
3. 验证字段添加成功
4. 为现有数据设置默认状态
5. 部署新版本代码

## 测试覆盖

### 单元测试
- **CcaeTaskDAOTest**: 测试DAO层的CRUD操作和JSON查询功能
- **CcaeTaskServiceTest**: 测试Service层的业务逻辑和完整流程

### 测试场景
- 新字段的插入和查询
- JSON List的序列化/反序列化
- 基于节点信息的查询功能
- 字段更新操作
- 分页查询功能
- List的动态操作（添加、删除元素）
- 逻辑删除和恢复功能
- 状态查询功能

## 注意事项

1. **数据格式**: 确保存储的是有效的JSON数组格式
2. **查询性能**: JSON查询可能比普通字段查询慢，建议根据实际使用情况考虑索引优化
3. **数据一致性**: 更新节点信息时要确保数据的完整性
4. **兼容性**: 新字段允许为NULL，保证向后兼容性
5. **逻辑删除**: MyBatis-Plus会自动在所有查询中添加status=1条件，确保不查询到已删除数据
6. **数据恢复**: 已删除的数据可以通过恢复功能重新激活
7. **状态管理**: 新增数据时会自动设置status=1（未删除状态）

## 版本信息
- **添加时间**: 2025-12-30
- **影响范围**: WOC_CCAE_TASK表及相关代码层
- **兼容性**: 向后兼容，不影响现有功能
