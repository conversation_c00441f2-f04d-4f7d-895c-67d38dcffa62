# CCAE任务表逻辑删除功能说明

## 概述
为WOC_CCAE_TASK表添加了逻辑删除功能，通过status字段实现软删除，避免物理删除数据带来的风险。

## 技术实现

### 数据库字段
```sql
-- 状态字段
STATUS NUMBER(1,0) DEFAULT 1 NOT NULL

-- 字段注释
COMMENT ON COLUMN SLGZT.WOC_CCAE_TASK.STATUS IS '状态：1-未删除，0-已删除';
```

### MyBatis-Plus配置
```java
/**
 * 状态：1-未删除，0-已删除
 */
@TableLogic(value = "1", delval = "0")
@TableField("STATUS")
private Integer status;
```

## 功能特性

### 1. 自动过滤已删除数据
MyBatis-Plus会自动在所有查询SQL中添加`WHERE status = 1`条件：

```sql
-- 原始查询
SELECT * FROM WOC_CCAE_TASK WHERE ccae_id = 'task-001'

-- 自动添加逻辑删除条件后
SELECT * FROM WOC_CCAE_TASK WHERE ccae_id = 'task-001' AND status = 1
```

### 2. 软删除操作
删除操作会自动转换为更新操作：

```sql
-- 逻辑删除
UPDATE WOC_CCAE_TASK SET status = 0 WHERE id = 123
```

### 3. 数据恢复
支持恢复已删除的数据：

```sql
-- 恢复数据
UPDATE WOC_CCAE_TASK SET status = 1 WHERE id = 123
```

## API接口

### Service层接口
```java
// 逻辑删除任务
void logicDeleteTask(Long id);

// 根据CCAE任务ID逻辑删除任务
void logicDeleteTaskByCcaeId(String ccaeId);

// 恢复逻辑删除的任务
void restoreTask(Long id);
```

### Manager层接口
```java
// 逻辑删除
void logicDeleteById(Long id);

// 恢复逻辑删除的数据
void restoreById(Long id);

// 根据CCAE任务ID逻辑删除
void logicDeleteByCcaeId(String ccaeId);
```

### DAO层方法
```java
// 逻辑删除
public int logicDeleteById(Long id);

// 恢复逻辑删除的数据
public int restoreById(Long id);
```

## 使用示例

### 1. 创建任务（自动设置未删除状态）
```java
CcaeTaskDTO task = new CcaeTaskDTO();
task.setCcaeId("task-001");
// status会自动设置为1（未删除）
ccaeTaskService.createTask(task);
```

### 2. 逻辑删除任务
```java
// 方式1：根据ID删除
ccaeTaskService.logicDeleteTask(taskId);

// 方式2：根据CCAE任务ID删除
ccaeTaskService.logicDeleteTaskByCcaeId("task-001");
```

### 3. 查询任务（自动过滤已删除）
```java
// 正常查询，自动过滤已删除数据
CcaeTaskDTO task = ccaeTaskService.getByCcaeId("task-001");
// 如果任务已被逻辑删除，返回null

List<CcaeTaskDTO> tasks = ccaeTaskService.listTasks(new CcaeTaskQuery());
// 结果中不包含已删除的任务
```

### 4. 恢复已删除任务
```java
ccaeTaskService.restoreTask(taskId);
```

### 5. 状态查询
```java
// 查询特定状态的任务
CcaeTaskQuery query = new CcaeTaskQuery();
query.setStatus(1); // 查询未删除的任务
List<CcaeTaskDTO> activeTasks = ccaeTaskService.listTasks(query);

query.setStatus(0); // 查询已删除的任务（需要特殊处理）
List<CcaeTaskDTO> deletedTasks = ccaeTaskService.listTasks(query);
```

## 优势

### 1. 数据安全
- 避免误删除导致的数据丢失
- 保留完整的数据历史记录
- 支持数据恢复操作

### 2. 业务连续性
- 删除操作可逆，降低业务风险
- 支持数据审计和追溯
- 便于问题排查和数据分析

### 3. 性能优化
- 避免物理删除的级联操作
- 减少数据库碎片
- 提高删除操作的性能

### 4. 开发便利
- MyBatis-Plus自动处理逻辑删除逻辑
- 无需修改现有查询代码
- 透明的删除过滤机制

## 注意事项

### 1. 查询性能
- 所有查询都会添加status条件，确保索引包含status字段
- 建议为常用查询字段创建复合索引

### 2. 数据一致性
- 确保相关联的数据也正确处理逻辑删除
- 注意外键关联的数据处理

### 3. 存储空间
- 逻辑删除的数据仍占用存储空间
- 定期清理长期不需要的已删除数据

### 4. 业务逻辑
- 某些业务场景可能需要查询已删除数据
- 需要提供特殊的查询接口处理这类需求

## 最佳实践

1. **默认状态**: 新增数据时自动设置status=1
2. **日志记录**: 删除和恢复操作要记录详细日志
3. **权限控制**: 恢复操作需要特殊权限
4. **定期清理**: 建立定期清理机制处理长期删除的数据
5. **监控告警**: 监控删除操作的频率和数量
