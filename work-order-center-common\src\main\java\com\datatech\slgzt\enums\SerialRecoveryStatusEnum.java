package com.datatech.slgzt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 串行回收状态枚举
 *
 * <AUTHOR>
 * @description 回收工单产品串行回收状态枚举，参考SerialChangeStatusEnum设计
 * @date 2025年 07月23日
 */
@Getter
@AllArgsConstructor
public enum SerialRecoveryStatusEnum {
    /**
     * 多次失败，不再尝试
     */
    RECOVERY_FAILED(-1, "多次失败，不再尝试"),
    /**
     * 不需要串行回收
     */
    NO_SERIAL_RECOVERY(0, "不需要串行回收"),

    /**
     * 需要串行回收(但未回收)
     */
    WAIT_RECOVERY(1, "需要串行回收"),

    /**
     * 串行回收待回收
     */
    RECOVERY_READY(2, "串行回收待回收"),

    /**
     * 串行回收过
     */
    RECOVERED(3, "串行回收过");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static SerialRecoveryStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SerialRecoveryStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     */
    public static String getDescByCode(Integer code) {
        SerialRecoveryStatusEnum statusEnum = getByCode(code);
        return statusEnum != null ? statusEnum.getDesc() : null;
    }
}
