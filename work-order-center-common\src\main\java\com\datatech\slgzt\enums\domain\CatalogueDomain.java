package com.datatech.slgzt.enums.domain;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public enum CatalogueDomain {
    // 父节点 (type=1)
    MOBILE_CLOUD("移动云", "cloudst_group_moc", 1, null),
    //智算中心
    ZHISUAN_CLOUD("智算中心", "cloudst_group_zhisuan", 1, null),
    NETWORK_CLOUD("网络云", "cloudst_group_nwc", 1, null),
    PLATFORM_CLOUD("平台云", "cloudst_group_plf", 1, null),
    CONTAINER_CLOUD("容器云", "cloudst_group_ctn", 1, null),
    EDGE_CLOUD("网络边缘云", "cloudst_group_edge", 1, null),
    IT_CLOUD("IT云", "cloudst_group_it", 1, null),

    // 子节点 (type=2)
    VMWARE("融合边缘云-VMware", "plf_prov_moc_zj_vmware", 2, MOBILE_CLOUD),
    //智算中心
    ZHISUAN("智算中心", "plf_prov_moc_zj_zhisuan", 2, ZHISUAN_CLOUD),
    //曙光
    SHUGUANG("曙光NAS", "plf_prov_moc_zj_sugon", 2, MOBILE_CLOUD), //曙光
    H3C("融合边缘云-华三", "plf_prov_moc_zj_h3c", 2, MOBILE_CLOUD),
    H3C_OBS("融合边缘云-华三对象存储", "plf_prov_moc_zj_h3c_obs", 2, MOBILE_CLOUD),
    HUAWEI("融合边缘云-华为", "plf_prov_moc_zj_huawei", 2, MOBILE_CLOUD),
    //浪潮
    INSPUR("融合边缘云-浪潮", "plf_prov_moc_zj_inspur", 2, MOBILE_CLOUD),
    INNOVATION("网络云-创新资源池", "plf_prov_nwc_zj_nfvo", 2, NETWORK_CLOUD), //网络
    PLATFORM_CHILD("平台云", "plf_prov_nwc_zj_plf", 2, PLATFORM_CLOUD), //网络
    //网络云省内
    NETWORK_CLOUD_PROVINCE("网络云-省内", "plf_prov_nwc_zj_province", 2, NETWORK_CLOUD), //网络
//    //网络边缘云-华为MEO
    EDGE_CLOUD_HUAWEI("网络边缘云-华为MEO", "plf_meo_nwc_zj_hw", 2, EDGE_CLOUD), //网络边缘云
//    //网络边缘云-中兴MEO
    EDGE_CLOUD_ZTE("网络边缘云-中兴MEO", "plf_meo_nwc_zj_zx", 2, EDGE_CLOUD), //网络边缘云
    IT_CHILD("IT云", "plf_prov_it_zj_plf", 2, IT_CLOUD), //网络


    //政务云 这个业务只有报表用的到其他都不行，就单独在用的地方处理
    //GOV_CLOUD("政务云", "plf_prov_gov_zj_gov", 2, PLATFORM_CLOUD),

    //容器云
    CONTAINER("容器云", "ctn_ctn", 2, CONTAINER_CLOUD),
    //华为hcs
    HW_HCS("华为HCS", "plf_prov_moc_zj_hcs", 2, MOBILE_CLOUD),;
    // Getters
    @Getter
    private final String name;
    @Getter
    private final String code;
    private final int type;
    @Getter
    private final CatalogueDomain parent;

    CatalogueDomain(String name, String code, int type, CatalogueDomain parent) {
        this.name = name;
        this.code = code;
        this.type = type;
        this.parent = parent;
    }

    // 判断是否为父节点
    public boolean isParent() {
        return type == 1;
    }

    // 判断是否为子节点
    public boolean isChild() {
        return type == 2;
    }

    // 获取子节点列表
    public List<CatalogueDomain> getChildren() {
        if (!isParent()) return Collections.emptyList();
        return Arrays.stream(values())
                .filter(child -> child.parent == this)
                .collect(Collectors.toList());
    }
    // 获取所有子节点
    public static List<CatalogueDomain> getAllChildren() {
        return Arrays.stream(values())
               .filter(CatalogueDomain::isChild)
               .collect(Collectors.toList());
    }


    // 获取所有父类节点
    public static List<CatalogueDomain> getParents() {
        return Arrays.stream(values())
                .filter(CatalogueDomain::isParent)
                .collect(Collectors.toList());
    }

    // 获取所有子类节点
    //geByCode
    public static CatalogueDomain getByCode(String code) {
        for (CatalogueDomain domain : values()) {
            if (domain.getCode().equals(code)) {
                return domain;
            }
        }
        return null;
    }

}