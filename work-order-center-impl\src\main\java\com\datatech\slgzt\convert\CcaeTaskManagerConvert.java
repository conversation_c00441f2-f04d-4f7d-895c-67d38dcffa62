package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.CcaeTaskDO;
import com.datatech.slgzt.model.dto.CcaeTaskDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * CCAE任务Manager转换器
 * 
 * <AUTHOR>
 * @date 2025-12-30
 */
@Mapper(componentModel = "spring")
public interface CcaeTaskManagerConvert {

    /**
     * DO转DTO
     */
    CcaeTaskDTO do2dto(CcaeTaskDO ccaeTaskDO);

    /**
     * DTO转DO
     */
    CcaeTaskDO dto2do(CcaeTaskDTO ccaeTaskDTO);

    /**
     * DO列表转DTO列表
     */
    List<CcaeTaskDTO> dos2DTOs(List<CcaeTaskDO> ccaeTaskDOList);

    /**
     * DTO列表转DO列表
     */
    List<CcaeTaskDO> dtoList2DOs(List<CcaeTaskDTO> ccaeTaskDTOList);
}
