package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datatech.slgzt.dao.mapper.CcaeTaskMapper;
import com.datatech.slgzt.dao.model.CcaeTaskDO;
import com.datatech.slgzt.model.query.CcaeTaskQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * CCAE任务DAO
 *
 * <AUTHOR>
 * @date 2025-12-30
 */
@Repository
public class CcaeTaskDAO {

    @Resource
    private CcaeTaskMapper ccaeTaskMapper;

    /**
     * 根据ID查询
     */
    public CcaeTaskDO getById(Long id) {
        return ccaeTaskMapper.selectById(id);
    }

    /**
     * 根据CCAE任务ID查询
     */
    public CcaeTaskDO getByCcaeId(String ccaeId) {
        return ccaeTaskMapper.selectOne(Wrappers.<CcaeTaskDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(ccaeId), CcaeTaskDO::getCcaeId, ccaeId)
                .last("LIMIT 1"));
    }

    /**
     * 新增
     */
    public int insert(CcaeTaskDO ccaeTaskDO) {
        return ccaeTaskMapper.insert(ccaeTaskDO);
    }

    /**
     * 更新
     */
    public int updateById(CcaeTaskDO ccaeTaskDO) {
        return ccaeTaskMapper.updateById(ccaeTaskDO);
    }

    /**
     * 删除
     */
    public int deleteById(Long id) {
        return ccaeTaskMapper.deleteById(id);
    }

    /**
     * 列表查询
     */
    public List<CcaeTaskDO> list(CcaeTaskQuery query) {
        return ccaeTaskMapper.selectList(buildQueryWrapper(query));
    }

    /**
     * 分页查询
     */
    public Page<CcaeTaskDO> page(CcaeTaskQuery query) {
        Page<CcaeTaskDO> page = new Page<>(
                ObjNullUtils.isNotNull(query.getPageNum()) ? query.getPageNum() : 1,
                ObjNullUtils.isNotNull(query.getPageSize()) ? query.getPageSize() : 10
        );
        return ccaeTaskMapper.selectPage(page, buildQueryWrapper(query));
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<CcaeTaskDO> buildQueryWrapper(CcaeTaskQuery query) {
        return Wrappers.<CcaeTaskDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getId()), CcaeTaskDO::getId, query.getId())
                .in(ObjNullUtils.isNotNull(query.getIds()), CcaeTaskDO::getId, query.getIds())
                .eq(StringUtils.isNotBlank(query.getCcaeId()), CcaeTaskDO::getCcaeId, query.getCcaeId())
                .in(ObjNullUtils.isNotNull(query.getCcaeIds()), CcaeTaskDO::getCcaeId, query.getCcaeIds())
                .eq(StringUtils.isNotBlank(query.getDeviceScope()), CcaeTaskDO::getDeviceScope, query.getDeviceScope())
                .in(ObjNullUtils.isNotNull(query.getDeviceScopes()), CcaeTaskDO::getDeviceScope, query.getDeviceScopes())
                .eq(StringUtils.isNotBlank(query.getTemplateCategory()), CcaeTaskDO::getTemplateCategory, query.getTemplateCategory())
                .in(ObjNullUtils.isNotNull(query.getTemplateCategories()), CcaeTaskDO::getTemplateCategory, query.getTemplateCategories())
                .eq(ObjNullUtils.isNotNull(query.getExecuteMethod()), CcaeTaskDO::getExecuteMethod, query.getExecuteMethod())
                .in(ObjNullUtils.isNotNull(query.getExecuteMethods()), CcaeTaskDO::getExecuteMethod, query.getExecuteMethods())
                .eq(ObjNullUtils.isNotNull(query.getProgress()), CcaeTaskDO::getProgress, query.getProgress())
                .in(ObjNullUtils.isNotNull(query.getProgressList()), CcaeTaskDO::getProgress, query.getProgressList())
                .eq(ObjNullUtils.isNotNull(query.getCreatorId()), CcaeTaskDO::getCreatorId, query.getCreatorId())
                .in(ObjNullUtils.isNotNull(query.getCreatorIds()), CcaeTaskDO::getCreatorId, query.getCreatorIds())
                .like(StringUtils.isNotBlank(query.getCreatorName()), CcaeTaskDO::getCreatorName, query.getCreatorName())
                .ge(ObjNullUtils.isNotNull(query.getCreateTimeStart()), CcaeTaskDO::getCreateTime, query.getCreateTimeStart())
                .le(ObjNullUtils.isNotNull(query.getCreateTimeEnd()), CcaeTaskDO::getCreateTime, query.getCreateTimeEnd())
                .ge(ObjNullUtils.isNotNull(query.getCcaeCreateTimeStart()), CcaeTaskDO::getCcaeCreateTime, query.getCcaeCreateTimeStart())
                .le(ObjNullUtils.isNotNull(query.getCcaeCreateTimeEnd()), CcaeTaskDO::getCcaeCreateTime, query.getCcaeCreateTimeEnd())
                .ge(ObjNullUtils.isNotNull(query.getCcaeEndTimeStart()), CcaeTaskDO::getCcaeEndTime, query.getCcaeEndTimeStart())
                .le(ObjNullUtils.isNotNull(query.getCcaeEndTimeEnd()), CcaeTaskDO::getCcaeEndTime, query.getCcaeEndTimeEnd())
                // 查询包含指定计算节点SN的任务
                .apply(StringUtils.isNotBlank(query.getComputeSn()),
                       "JSON_CONTAINS(COMPUTE_LIST, JSON_QUOTE({0}))", query.getComputeSn())
                // 查询包含指定网络IP的任务
                .apply(StringUtils.isNotBlank(query.getNetworkIp()),
                       "JSON_CONTAINS(NETWORK_IP_LIST, JSON_QUOTE({0}))", query.getNetworkIp())
                .orderByDesc(CcaeTaskDO::getCreateTime);
    }
}
