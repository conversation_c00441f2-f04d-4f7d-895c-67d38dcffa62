package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.datatech.slgzt.dao.mapper.HashrateResInfoMapper;
import com.datatech.slgzt.dao.model.HashrateResInfoDO;
import com.datatech.slgzt.model.query.HashrateResInfoQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 12月19日 16:11:47
 */
@Repository
public class HashrateResInfoDAO {


    @Resource
    private HashrateResInfoMapper mapper;


    /**
     * 插入智算资源
     *
     * @param hashrateResInfoDO 智算资源信息
     * @return 插入的记录数
     */
    public int insert(HashrateResInfoDO hashrateResInfoDO) {
        return mapper.insert(hashrateResInfoDO);
    }

    public void delByDeviceId(String deviceId) {
        LambdaQueryWrapper<HashrateResInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HashrateResInfoDO::getDeviceId, deviceId);
        mapper.delete(queryWrapper);
    }


    /**
     * 更新智算资源
     *
     * @param hashrateResInfoDO 智算资源信息
     * @return 更新的记录数
     */
    public int update(HashrateResInfoDO hashrateResInfoDO) {
        return mapper.updateById(hashrateResInfoDO);
    }

    /**
     * delById
     */
    public void delById(Long id) {
        mapper.deleteById(id);
    }


    /**
     * 根据查询条件获取智算资源列表
     *
     * @param query 查询条件
     * @return 智算资源列表
     */
    public List<HashrateResInfoDO> list(HashrateResInfoQuery query) {
        // 构建查询条件
        LambdaQueryWrapper<HashrateResInfoDO> queryWrapper = new LambdaQueryWrapper<>();

        if (query != null) {
            // 所属云代码精确匹配
            if (ObjNullUtils.isNotNull(query.getDomainCode())) {
                queryWrapper.eq(HashrateResInfoDO::getDomainCode, query.getDomainCode());
            }
            // 所属云代码精确匹配
            if (ObjNullUtils.isNotNull(query.getDomainName())) {
                queryWrapper.like(HashrateResInfoDO::getDomainName, query.getDomainName());
            }
            // 所属云代码精确匹配
            if (ObjNullUtils.isNotNull(query.getIndexT())) {
                queryWrapper.eq(HashrateResInfoDO::getIndexT, query.getIndexT());
            }
            // 资源池名称模糊查询
            if (ObjNullUtils.isNotNull(query.getRegionName())) {
                queryWrapper.like(HashrateResInfoDO::getRegionName, query.getRegionName());
            }
            // 资源池名称模糊查询
            if (ObjNullUtils.isNotNull(query.getRegionNames())) {
                queryWrapper.in(HashrateResInfoDO::getRegionName, query.getRegionNames());
            }

            // 使用方精确匹配
            if (ObjNullUtils.isNotNull(query.getDeptNameList())) {
                queryWrapper.in(HashrateResInfoDO::getDeptName, query.getDeptNameList());
            }



            // 区域代码精确匹配
            if (ObjNullUtils.isNotNull(query.getAreaCodes())) {
                queryWrapper.in(HashrateResInfoDO::getAreaCode, query.getAreaCodes());
            }

            // 模型名称精确匹配
            if (ObjNullUtils.isNotNull(query.getModelNames())) {
                queryWrapper.in(HashrateResInfoDO::getModel, query.getModelNames());
            }
            // 模型名称精确匹配
            if (ObjNullUtils.isNotNull(query.getModelName())) {
                queryWrapper.eq(HashrateResInfoDO::getModel, query.getModelName());
            }
            // 区域代码精确匹配
            if (ObjNullUtils.isNotNull(query.getAreaCode())) {
                queryWrapper.eq(HashrateResInfoDO::getAreaCode, query.getAreaCode());
            }

            // 区域代码列表精确匹配
            if (ObjNullUtils.isNotNull(query.getAreaCodeList())) {
                queryWrapper.in(HashrateResInfoDO::getAreaCode, query.getAreaCodeList());
            }



            // 业务系统名称模糊查询
            if (ObjNullUtils.isNotNull(query.getBusinessName())) {
                queryWrapper.like(HashrateResInfoDO::getBusinessName, query.getBusinessName());
            }

            // 资源ID模糊查询
            if (ObjNullUtils.isNotNull(query.getDeviceId())) {
                queryWrapper.like(HashrateResInfoDO::getDeviceId, query.getDeviceId());
            }

            // 设备名称模糊查询
            if (ObjNullUtils.isNotNull(query.getDeviceName())) {
                queryWrapper.like(HashrateResInfoDO::getDeviceName, query.getDeviceName());
            }

            // 设备状态精确匹配
            if (ObjNullUtils.isNotNull(query.getDeviceStatus())) {
                queryWrapper.eq(HashrateResInfoDO::getDeviceStatus, query.getDeviceStatus());
            }



            // 设备状态列表精确匹配
            if (ObjNullUtils.isNotNull(query.getDeviceStatusList())) {
                queryWrapper.in(HashrateResInfoDO::getDeviceStatus, query.getDeviceStatusList());
            }

            // 分配状态精确匹配
            if (ObjNullUtils.isNotNull(query.getAllocationStatus())) {
                queryWrapper.eq(HashrateResInfoDO::getAllocationStatus, query.getAllocationStatus());
            }
            // 分配状态列表精确匹配
            if (ObjNullUtils.isNotNull(query.getAllocationStatusList())) {
                queryWrapper.in(HashrateResInfoDO::getAllocationStatus, query.getAllocationStatusList());
            }



            // 申请人模糊查询
            if (ObjNullUtils.isNotNull(query.getApplicant())) {
                queryWrapper.like(HashrateResInfoDO::getApplicant, query.getApplicant());
            }

            // 申请时间范围查询
            if (query.getStartTime() != null) {
                queryWrapper.ge(HashrateResInfoDO::getApplicationTime, query.getStartTime());
            }

            if (query.getEndTime() != null) {
                queryWrapper.le(HashrateResInfoDO::getApplicationTime, query.getEndTime());
            }
        }

        // 按申请时间降序排序
        queryWrapper.orderByDesc(HashrateResInfoDO::getId);

        // 执行查询
        return mapper.selectList(queryWrapper);
    }


     /**
     * 根据ID获取智算资源
     *
     * @param id 智算资源ID
     * @return 智算资源信息
     */
    public HashrateResInfoDO getById(Long id) {
        return mapper.selectById(id);
    }

}
