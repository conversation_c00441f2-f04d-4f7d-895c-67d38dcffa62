package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.StandardRecoveryWorkOrderProductMapper;
import com.datatech.slgzt.dao.model.order.RecoveryWorkOrderProductDO;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class RecoveryWorkOrderProductDAO {

    @Resource
    private StandardRecoveryWorkOrderProductMapper mapper;


    public void insert(RecoveryWorkOrderProductDO productDO) {
        mapper.insert(productDO);
    }

    public RecoveryWorkOrderProductDO getById(Long id) {
        return mapper.selectById(id);
    }

    public void update(RecoveryWorkOrderProductDO productDO) {
        mapper.updateById(productDO);
    }


    public List<RecoveryWorkOrderProductDO> getByIds(List<Long> ids) {
        return mapper.selectBatchIds(ids);
    }

    /**
     * listByWorkOrderId
     */
    public List<RecoveryWorkOrderProductDO> listByWorkOrderId(String workOrderId) {
        return mapper.selectList(Wrappers.<RecoveryWorkOrderProductDO>lambdaQuery()
                .eq(RecoveryWorkOrderProductDO::getWorkOrderId, workOrderId));
    }

    public void updateByIds(List<Long> ids, String status) {
        mapper.updateByIds(ids, status);
    }

    public void updateByResourceDetailId(String resourceDetailId, String status,String message) {
        mapper.updateByResourceDetailId(resourceDetailId, status,message);
    }

    public void updateByParentId(RecoveryWorkOrderProductDO productDO) {
        mapper.update(productDO, Wrappers.<RecoveryWorkOrderProductDO>lambdaUpdate()
                .eq(RecoveryWorkOrderProductDO::getParentProductId
                        , productDO.getParentProductId()));
    }

    public RecoveryWorkOrderProductDO getBySubOrderId(Long subOrderId) {
        return mapper.selectOne(Wrappers.<RecoveryWorkOrderProductDO>lambdaQuery().eq(RecoveryWorkOrderProductDO::getSubOrderId, subOrderId));
    }

    public void updateHcmByCmdbIds(List<String> configIds, String status) {
        RecoveryWorkOrderProductDO productDO = new RecoveryWorkOrderProductDO();
        productDO.setHcmStatus(status);
        mapper.update(productDO, Wrappers.<RecoveryWorkOrderProductDO>lambdaUpdate()
                .in(RecoveryWorkOrderProductDO::getCmdbId
                        , configIds));
    }

    public void updateHcmByIds(List<Long> ids, String status) {
        RecoveryWorkOrderProductDO productDO = new RecoveryWorkOrderProductDO();
        productDO.setHcmStatus(status);
        mapper.update(productDO, Wrappers.<RecoveryWorkOrderProductDO>lambdaUpdate()
                .in(RecoveryWorkOrderProductDO::getId
                        , ids));
    }

    public void updateTenantConfirmByIds(List<Long> ids, Boolean tenantConfirm) {
        RecoveryWorkOrderProductDO productDO = new RecoveryWorkOrderProductDO();
        productDO.setTenantConfirm(tenantConfirm);
        mapper.update(productDO, Wrappers.<RecoveryWorkOrderProductDO>lambdaUpdate()
               .in(RecoveryWorkOrderProductDO::getId
                       , ids));
    }

    public void deleteByWorkOrderId(String workOrderId) {
        mapper.delete(Wrappers.<RecoveryWorkOrderProductDO>lambdaQuery()
                .eq(RecoveryWorkOrderProductDO::getWorkOrderId, workOrderId));
    }

    public RecoveryWorkOrderProductDO getByCmdbId(String cmdbId) {
        return mapper.selectOne(Wrappers.<RecoveryWorkOrderProductDO>lambdaQuery()
                .eq(RecoveryWorkOrderProductDO::getCmdbId, cmdbId));
    }

    /**
     * 查询正在串行回收的主产品（状态为3，且为主产品）
     */
    public RecoveryWorkOrderProductDO listSerialRecovering() {
        return mapper.selectOne(Wrappers.<RecoveryWorkOrderProductDO>lambdaQuery()
                .eq(RecoveryWorkOrderProductDO::getSerialRecoveryStatus, 3)
                .eq(RecoveryWorkOrderProductDO::getParentProductId, 0L)
                .eq(RecoveryWorkOrderProductDO::getRecoveryStatus, RecoveryStatusEnum.RECOVERING.getType())
                .last("LIMIT 1"));
    }

    /**
     * 查询下一个需要串行回收的主产品（状态为2，且为主产品）
     */
    public RecoveryWorkOrderProductDO listNextSerialRecovery() {
        return mapper.selectOne(Wrappers.<RecoveryWorkOrderProductDO>lambdaQuery()
                .eq(RecoveryWorkOrderProductDO::getSerialRecoveryStatus, 2)
                .eq(RecoveryWorkOrderProductDO::getParentProductId, 0L)
                .eq(RecoveryWorkOrderProductDO::getRecoveryStatus, RecoveryStatusEnum.RECOVERING.getType())
                .orderByAsc(RecoveryWorkOrderProductDO::getCreateTime)
                .last("LIMIT 1"));
    }

    /**
     * 统计正在串行回收的主产品数量
     */
    public int countSerialRecovering() {
        return mapper.selectCount(Wrappers.<RecoveryWorkOrderProductDO>lambdaQuery()
                .eq(RecoveryWorkOrderProductDO::getSerialRecoveryStatus, 3)
                .eq(RecoveryWorkOrderProductDO::getParentProductId, 0L)
                .eq(RecoveryWorkOrderProductDO::getRecoveryStatus, RecoveryStatusEnum.RECOVERING.getType()));
    }

    /**
     * 根据父产品ID更新串行回收状态
     */
    public void updateSerialRecoveryStatusByParentId(Long parentProductId, Integer serialRecoveryStatus) {
        RecoveryWorkOrderProductDO updateDO = new RecoveryWorkOrderProductDO();
        updateDO.setSerialRecoveryStatus(serialRecoveryStatus);
        mapper.update(updateDO, Wrappers.<RecoveryWorkOrderProductDO>lambdaUpdate()
                .eq(RecoveryWorkOrderProductDO::getParentProductId, parentProductId));
    }

    /**
     * 按REGION_CODE分组查询每个资源池中最早的待回收产品
     */
    public List<RecoveryWorkOrderProductDO> listNextSerialRecoveryByRegionCode() {
        return mapper.listNextSerialRecoveryByRegionCode();
    }

    public void updateSerialRecoveryStatusByOrderId(String workOrderId, String regionCode, Integer oldSerialRecoveryStatus, Integer newSerialRecoveryStatus) {
        mapper.update(null, Wrappers.<RecoveryWorkOrderProductDO>lambdaUpdate()
                .set(RecoveryWorkOrderProductDO::getSerialRecoveryStatus, newSerialRecoveryStatus)
                .eq(RecoveryWorkOrderProductDO::getWorkOrderId, workOrderId)
                .eq(RecoveryWorkOrderProductDO::getRegionCode, regionCode)
                .eq(RecoveryWorkOrderProductDO::getSerialRecoveryStatus, oldSerialRecoveryStatus));
    }
}
