package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.convert.StandardWorkOrderProductManagerConvert;
import com.datatech.slgzt.dao.mapper.StandardWorkOrderProductMapper;
import com.datatech.slgzt.dao.model.order.StandardWorkOrderProductDO;
import com.datatech.slgzt.enums.StatusEnum;
import com.datatech.slgzt.model.query.StandardWorkOrderProductQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 16:57:42
 */
@Repository
public class StandardWorkOrderProductDAO {

    @Resource
    private StandardWorkOrderProductMapper mapper;

    @Resource
    private StandardWorkOrderProductManagerConvert convert;

    /**
     * list
     */
    public List<StandardWorkOrderProductDO> list(StandardWorkOrderProductQuery query) {
        return mapper.selectList(Wrappers.<StandardWorkOrderProductDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getOrderId()), StandardWorkOrderProductDO::getWorkOrderId, query.getOrderId())
                .in(ObjNullUtils.isNotNull(query.getIds()), StandardWorkOrderProductDO::getId, query.getIds())
                .in(ObjNullUtils.isNotNull(query.getGids()), StandardWorkOrderProductDO::getGid, query.getGids())
                .in(ObjNullUtils.isNotNull(query.getSubOrderIds()), StandardWorkOrderProductDO::getSubOrderId, query.getSubOrderIds())
                .eq(ObjNullUtils.isNotNull(query.getProductType()), StandardWorkOrderProductDO::getProductType, query.getProductType())
                .eq(ObjNullUtils.isNotNull(query.getParentId()), StandardWorkOrderProductDO::getParentProductId, query.getParentId())
        );

    }

    public StandardWorkOrderProductDO listOne(String openStatus, int cloudPlatformStatus) {
        return mapper.selectOne(Wrappers.<StandardWorkOrderProductDO>lambdaQuery()
                .eq(StandardWorkOrderProductDO::getOpenStatus, openStatus)
                .eq(StandardWorkOrderProductDO::getSerialOpenStatus, cloudPlatformStatus)
                // 主产品
                .eq(StandardWorkOrderProductDO::getParentProductId, 0)
                .last("LIMIT 1"));
    }

    /**
     * 按REGION_CODE分组查询每个资源池中最早的待开通产品
     * 使用窗口函数ROW_NUMBER()按REGION_CODE分组，按CREATE_TIME排序，取每组第一条记录
     */
    public List<StandardWorkOrderProductDO> listNextOpenByRegionCode() {
        return mapper.listNextOpenByRegionCode();
    }


    public void delByWorkOrderId(String workOrderId) {
        mapper.delete(Wrappers.<StandardWorkOrderProductDO>lambdaQuery().eq(StandardWorkOrderProductDO::getWorkOrderId, workOrderId));
    }

    /**
     * updateById
     */
    public int updateById(StandardWorkOrderProductDO productDO) {
        return mapper.updateById(productDO);
    }

    public void updateByWorkOrderId(StandardWorkOrderProductDO productDO) {
        mapper.update(productDO, Wrappers.<StandardWorkOrderProductDO>lambdaUpdate()
                .eq(StandardWorkOrderProductDO::getWorkOrderId, productDO.getWorkOrderId()));
    }


    public StandardWorkOrderProductDO getById(Long id) {
        return mapper.selectById(id);
    }

    /**
     * getByGid
     */
    public StandardWorkOrderProductDO getByGid(String gid) {
        return mapper.selectOne(Wrappers.<StandardWorkOrderProductDO>lambdaQuery().eq(StandardWorkOrderProductDO::getGid, gid));
    }

    public StandardWorkOrderProductDO getBySubOrderId(Long subOrderId) {
        return mapper.selectOne(Wrappers.<StandardWorkOrderProductDO>lambdaQuery().eq(StandardWorkOrderProductDO::getSubOrderId, subOrderId));
    }

    /**
     * insert
     */
    public void insert(StandardWorkOrderProductDO productDO) {
        mapper.insert(productDO);
    }


    public void updateByParentId(StandardWorkOrderProductDO product) {
        mapper.update(product, Wrappers.<StandardWorkOrderProductDO>lambdaUpdate()
                .eq(StandardWorkOrderProductDO::getParentProductId
                        , product.getParentProductId()));
    }

    /**
     * 根据子订单ID更新Job执行ID
     */
    public void updateJobExecutionIdBySubOrderId(StandardWorkOrderProductDO product) {
        mapper.update(new StandardWorkOrderProductDO(), Wrappers.<StandardWorkOrderProductDO>lambdaUpdate()
                .eq(StandardWorkOrderProductDO::getSubOrderId, product.getSubOrderId())
                .set(StandardWorkOrderProductDO::getJobExecutionId, product.getJobExecutionId()));
    }

    /**
     * selectById
     */
    public StandardWorkOrderProductDO selectById(Long id) {
        return mapper.selectById(id);
    }

    public List<StandardWorkOrderProductDO> getByWorkOrderId(String orderId) {
        QueryWrapper<StandardWorkOrderProductDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ENABLED", StatusEnum.NORMAL.code());
        queryWrapper.eq("WORK_ORDER_ID", orderId);
        return mapper.selectList(queryWrapper);
    }

    public void updateSerialOpenStatusByOrderId(String workOrderId, String regionCode, int oldSerialOpenStatus, int newSerialOpenStatus) {
        mapper.update(null, Wrappers.<StandardWorkOrderProductDO>lambdaUpdate()
                .set(StandardWorkOrderProductDO::getSerialOpenStatus, newSerialOpenStatus)
                .eq(StandardWorkOrderProductDO::getWorkOrderId, workOrderId)
                .eq(StandardWorkOrderProductDO::getRegionCode, regionCode)
                .eq(StandardWorkOrderProductDO::getSerialOpenStatus, oldSerialOpenStatus));
    }
}

