package com.datatech.slgzt.dao.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.datatech.slgzt.dao.model.VMResourcePerformanceDO;
import com.datatech.slgzt.model.dto.VMResourcePerformanceStringDTO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@DS("clickhouse")
public interface CKVmPerformanceMapper {

    /**
     * 聚合虚拟机性能数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param regionCodes 区域编码列表
     * @return 虚拟机性能数据
     */
    List<VMResourcePerformanceDO> aggregateVmPerformance(@Param("startTime") LocalDateTime startTime,
                                                         @Param("endTime") LocalDateTime endTime,
                                                         @Param("regionCodes") List<String> regionCodes);

    /**
     * 查询虚拟机性能列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param regionCodes 区域编码列表
     * @return 虚拟机性能数据列表
     */
    List<VMResourcePerformanceDO> listVmPerformance(@Param("startTime") LocalDateTime startTime,
                                                    @Param("endTime") LocalDateTime endTime,
                                                    @Param("regionCodes") List<String> regionCodes);

    /**
     * 聚合虚拟机性能数据总计（不分组）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param regionCodes 区域编码列表
     * @return 虚拟机性能数据总计
     */
    VMResourcePerformanceStringDTO aggregateVmPerformanceTotal(@Param("startTime") LocalDateTime startTime,
                                                               @Param("endTime") LocalDateTime endTime,
                                                               @Param("regionCodes") List<String> regionCodes);

    /**
     * 手动分页查询虚拟机性能列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param regionCodes 区域编码列表
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 虚拟机性能数据列表
     */
    List<VMResourcePerformanceDO> listVmPerformanceWithPage(@Param("startTime") LocalDateTime startTime,
                                                            @Param("endTime") LocalDateTime endTime,
                                                            @Param("regionCodes") List<String> regionCodes,
                                                            @Param("offset") Long offset,
                                                            @Param("limit") Long limit);

    /**
     * 查询虚拟机性能列表总数
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param regionCodes 区域编码列表
     * @return 总记录数
     */
    Long countVmPerformance(@Param("startTime") LocalDateTime startTime,
                           @Param("endTime") LocalDateTime endTime,
                           @Param("regionCodes") List<String> regionCodes);

}
