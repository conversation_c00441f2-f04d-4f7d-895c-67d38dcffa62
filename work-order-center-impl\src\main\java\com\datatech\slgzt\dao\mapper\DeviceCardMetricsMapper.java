package com.datatech.slgzt.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datatech.slgzt.dao.model.DeviceCardMetricsDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;

import java.time.LocalDateTime;
import java.util.List;


public interface DeviceCardMetricsMapper extends BaseMapper<DeviceCardMetricsDO> {


    @SelectProvider(type = DeviceCardMetricsMapper.class, method = "avgBuildQuery")
    List<DeviceCardMetricsDO> queryAvgDeviceMetrics(
            @Param("areaCode") String areaCode,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("deviceIds") List<String> deviceIds
    );

    /**
     * 按时间聚合查询GPU指标数据
     */
    @SelectProvider(type = DeviceCardMetricsMapper.class, method = "buildGpuAggregateQuery")
    List<DeviceCardMetricsDO> queryGpuMetricsAggregated(
            @Param("deviceIds") List<String> deviceIds,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("statType") String statType
    );

    /**
     * 按区域和卡类型查询利用率聚合数据
     */
    @SelectProvider(type = DeviceCardMetricsMapper.class, method = "buildCardUtilizationQuery")
    List<DeviceCardMetricsDO> queryCardUtilizationMetrics(
            @Param("areaCode") String areaCode,
            @Param("modelName") String modelName,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("aggregationType") String aggregationType,
            @Param("deviceIds") List<String> deviceIds
    );


    /**
     * 按区域和卡类型查询利用率聚合数据
     */
    @SelectProvider(type = DeviceCardMetricsMapper.class, method = "buildCardUtilizationQuery2")
    List<DeviceCardMetricsDO> queryCardUtilizationMetrics2(
            @Param("areaCode") String areaCode,
            @Param("modelName") String modelName,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("aggregationType") String aggregationType
    );

    /**
     * 查询业务系统历史利用率数据（按时间范围聚合）
     */
    @SelectProvider(type = DeviceCardMetricsMapper.class, method = "buildBusinessSystemHistoryQuery")
    List<DeviceCardMetricsDO> queryBusinessSystemHistoryMetrics(
            @Param("areaCode") String areaCode,
            @Param("modelName") String modelName,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    // 动态 SQL 构建方法
    static String avgBuildQuery(@Param("areaCode") String areaCode,
                             @Param("startTime") LocalDateTime startTime,
                             @Param("endTime") LocalDateTime endTime,
                                @Param("deviceIds") List<String> deviceIds) {
        return new org.apache.ibatis.jdbc.SQL() {{
            SELECT("AREA_CODE AS areaCode");
            SELECT("GPU_TIME AS gpuTime");
            SELECT("AVG(GPU_UTIL_PERCENT) AS GPU_UTIL_PERCENT");
            SELECT("AVG(MEM_UTIL_PERCENT) AS MEM_UTIL_PERCENT");

            FROM("woc_gpu_device_metrics_distributed");

            if (areaCode != null && !areaCode.isEmpty()) {
                WHERE("AREA_CODE = #{areaCode}");
            }
            if (startTime != null && endTime != null) {
                WHERE("CREATED_AT BETWEEN #{startTime} AND #{endTime}");
            }
            if (deviceIds != null && !deviceIds.isEmpty()) {
                WHERE("DEVICE_ID IN (" +
                    String.join(",", deviceIds.stream().map(id -> "'" + id + "'").toArray(String[]::new)) + ")");
            }

            GROUP_BY("AREA_CODE, GPU_TIME");
            ORDER_BY("AREA_CODE, GPU_TIME");
        }}.toString();
    }

    /**
     * 构建GPU聚合查询SQL
     */
    static String buildGpuAggregateQuery(@Param("deviceIds") List<String> deviceIds,
                                       @Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime,
                                       @Param("statType") String statType) {
        return new org.apache.ibatis.jdbc.SQL() {{
            SELECT("DEVICE_ID AS deviceId");
            SELECT("MODEL_NAME AS modelName");
            SELECT("DEVICE_TYPE AS deviceType");
            SELECT("AREA_CODE AS areaCode");

            // 根据聚合类型选择时间分组字段
            if ("HOUR".equalsIgnoreCase(statType)) {
                SELECT("GPU_TIME AS gpuTime");
            } else if ("DAY".equalsIgnoreCase(statType)) {
                SELECT("SUBSTRING(GPU_TIME, 1, 8) AS gpuTime");
            } else if ("MONTH".equalsIgnoreCase(statType)) {
                SELECT("SUBSTRING(GPU_TIME, 1, 6) AS gpuTime");
            } else {
                SELECT("GPU_TIME AS gpuTime");
            }
            
            // 聚合指标数据
            SELECT("AVG(GPU_UTIL_PERCENT) AS gpuUtilPercent");
            SELECT("AVG(MEM_UTIL_PERCENT) AS memUtilpercent");
            SELECT("AVG(MEMORY_USAGE) AS memoryUsage");
            SELECT("AVG(DEV_POWER_USAGE) AS devPowerUsage");
            SELECT("AVG(DEV_GPU_TEMP) AS devGpuTemp");
            SELECT("MAX(ALLOCATION_COUNT) AS allocationCount");

            FROM("woc_gpu_device_metrics_distributed");

            // 设备ID过滤
            if (deviceIds != null && !deviceIds.isEmpty()) {
                WHERE("DEVICE_ID IN (" + 
                    String.join(",", deviceIds.stream().map(id -> "'" + id + "'").toArray(String[]::new)) + ")");
            }
            
            // 时间范围过滤
            if (startTime != null && endTime != null) {
                WHERE("CREATED_AT BETWEEN #{startTime} AND #{endTime}");
            }

            // 根据聚合类型分组
            if ("HOUR".equalsIgnoreCase(statType)) {
                GROUP_BY("DEVICE_ID, MODEL_NAME, DEVICE_TYPE, AREA_CODE, GPU_TIME");
            } else if ("DAY".equalsIgnoreCase(statType)) {
                GROUP_BY("DEVICE_ID, MODEL_NAME, DEVICE_TYPE, AREA_CODE, SUBSTRING(GPU_TIME, 1, 8)");
            } else if ("MONTH".equalsIgnoreCase(statType)) {
                GROUP_BY("DEVICE_ID, MODEL_NAME, DEVICE_TYPE, AREA_CODE, SUBSTRING(GPU_TIME, 1, 6)");
            } else {
                GROUP_BY("DEVICE_ID, MODEL_NAME, DEVICE_TYPE, AREA_CODE, GPU_TIME");
            }

            ORDER_BY("gpuTime DESC, DEVICE_ID");
        }}.toString();
    }

    /**
     * 构建卡类型利用率查询SQL
     */
    static String buildCardUtilizationQuery(@Param("areaCode") String areaCode,
                                          @Param("modelName") String modelName,
                                          @Param("startTime") LocalDateTime startTime,
                                          @Param("endTime") LocalDateTime endTime,
                                          @Param("aggregationType") String aggregationType,
                                            @Param("deviceIds") List<String> deviceIds) {
        return new org.apache.ibatis.jdbc.SQL() {{
            SELECT("MODEL_NAME AS modelName");
            
            // 根据聚合类型选择时间分组字段
            if ("day".equalsIgnoreCase(aggregationType)) {
                SELECT("SUBSTRING(GPU_TIME, 1, 10) AS gpuTime"); // 精确到小时 YYYYMMDDHH
                SELECT("SUBSTRING(GPU_TIME, 1, 10) AS timePoint");
            } else if ("month".equalsIgnoreCase(aggregationType)) {
                SELECT("SUBSTRING(GPU_TIME, 1, 8) AS gpuTime");  // 精确到天 YYYYMMDD
                SELECT("SUBSTRING(GPU_TIME, 1, 8) AS timePoint");
            } else {
                SELECT("GPU_TIME AS gpuTime");
                SELECT("GPU_TIME AS timePoint");
            }
            
            // 聚合算力和显存利用率
            SELECT("AVG(GPU_UTIL_PERCENT) AS gpuUtilPercent");
            SELECT("AVG(MEM_UTIL_PERCENT) AS memUtilpercent");

            FROM("woc_gpu_device_metrics_distributed");

            // areaCode过滤（可以为空）
            if (areaCode != null && !areaCode.trim().isEmpty()) {
                WHERE("AREA_CODE = #{areaCode}");
            }
            
            // modelName过滤（必须）
            if (modelName != null && !modelName.trim().isEmpty()) {
                WHERE("MODEL_NAME = #{modelName}");
            }
            if (deviceIds != null && !deviceIds.isEmpty()) {
                WHERE("DEVICE_ID IN (" +
                    String.join(",", deviceIds.stream().map(id -> "'" + id + "'").toArray(String[]::new)) + ")");
            }
            // 时间范围过滤
            if (startTime != null && endTime != null) {
                WHERE("CREATED_AT BETWEEN #{startTime} AND #{endTime}");
            }
            WHERE("METRIC_SOURCE like '%physical%'");
            // 根据聚合类型分组
            if ("day".equalsIgnoreCase(aggregationType)) {
                GROUP_BY("MODEL_NAME, SUBSTRING(GPU_TIME, 1, 10)");
                ORDER_BY("SUBSTRING(GPU_TIME, 1, 10) ASC");
            } else if ("month".equalsIgnoreCase(aggregationType)) {
                GROUP_BY("MODEL_NAME, SUBSTRING(GPU_TIME, 1, 8)");
                ORDER_BY("SUBSTRING(GPU_TIME, 1, 8) ASC");
            } else {
                GROUP_BY("MODEL_NAME, GPU_TIME");
                ORDER_BY("GPU_TIME ASC");
            }
        }}.toString();
    }

    /**
     * 构建卡类型利用率查询SQL
     */
    static String buildCardUtilizationQuery2(@Param("areaCode") String areaCode,
                                            @Param("modelName") String modelName,
                                            @Param("startTime") LocalDateTime startTime,
                                            @Param("endTime") LocalDateTime endTime,
                                            @Param("aggregationType") String aggregationType) {
        return new org.apache.ibatis.jdbc.SQL() {{
            SELECT("DEVICE_ID");
            // 根据聚合类型选择时间分组字段
            if ("day".equalsIgnoreCase(aggregationType)) {
                SELECT("SUBSTRING(GPU_TIME, 1, 10) AS gpuTime"); // 精确到小时 YYYYMMDDHH
                SELECT("SUBSTRING(GPU_TIME, 1, 10) AS timePoint");
            } else if ("month".equalsIgnoreCase(aggregationType)) {
                SELECT("SUBSTRING(GPU_TIME, 1, 8) AS gpuTime");  // 精确到天 YYYYMMDD
                SELECT("SUBSTRING(GPU_TIME, 1, 8) AS timePoint");
            } else {
                SELECT("GPU_TIME AS gpuTime");
                SELECT("GPU_TIME AS timePoint");
            }

            // 聚合算力和显存利用率
            SELECT("AVG(GPU_UTIL_PERCENT) AS gpuUtilPercent");
            SELECT("AVG(MEM_UTIL_PERCENT) AS memUtilpercent");

            FROM("woc_gpu_device_metrics_distributed");

            // areaCode过滤（可以为空）
            if (areaCode != null && !areaCode.trim().isEmpty()) {
                WHERE("AREA_CODE = #{areaCode}");
            }

            WHERE("MODEL_NAME = '910B'");

            // 时间范围过滤
            if (startTime != null && endTime != null) {
                WHERE("CREATED_AT BETWEEN #{startTime} AND #{endTime}");
            }

            // 根据聚合类型分组
            if ("day".equalsIgnoreCase(aggregationType)) {
                GROUP_BY("DEVICE_ID, SUBSTRING(GPU_TIME, 1, 10)");
                ORDER_BY("SUBSTRING(GPU_TIME, 1, 10) ASC");
            } else if ("month".equalsIgnoreCase(aggregationType)) {
                GROUP_BY("DEVICE_ID, SUBSTRING(GPU_TIME, 1, 8)");
                ORDER_BY("SUBSTRING(GPU_TIME, 1, 8) ASC");
            } else {
                GROUP_BY("DEVICE_ID, GPU_TIME");
                ORDER_BY("GPU_TIME ASC");
            }
        }}.toString();
    }

    /**
     * 构建设备历史指标聚合查询SQL（按deviceId分组）
     */
    static String buildBusinessSystemHistoryQuery(@Param("areaCode") String areaCode,
                                                @Param("modelName") String modelName,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime) {
        return new org.apache.ibatis.jdbc.SQL() {{
            // 只查询分布式表的字段
            SELECT("DEVICE_ID AS deviceId");
            // 聚合指标数据 - 按设备和时间范围计算平均值
            SELECT("AVG(GPU_UTIL_PERCENT) AS gpuUtilPercent");
            SELECT("AVG(MEM_UTIL_PERCENT) AS memUtilpercent");
            SELECT("AVG(MEMORY_USAGE) AS memoryUsage");
            SELECT("AVG(DEV_POWER_USAGE) AS devPowerUsage");
            SELECT("AVG(DEV_GPU_TEMP) AS devGpuTemp");

            FROM("woc_gpu_device_metrics_distributed");

            // areaCode过滤（可选）
            if (areaCode != null && !areaCode.trim().isEmpty()) {
                WHERE("AREA_CODE = #{areaCode}");
            }
            
            // modelName过滤（可选）
            if (modelName != null && !modelName.trim().isEmpty()) {
                WHERE("MODEL_NAME = #{modelName}");
            }
            
            // 时间范围过滤
            if (startTime != null && endTime != null) {
                WHERE("CREATED_AT BETWEEN #{startTime} AND #{endTime}");
            }

            // 按设备分组
            GROUP_BY("DEVICE_ID");
        }}.toString();
    }
}
