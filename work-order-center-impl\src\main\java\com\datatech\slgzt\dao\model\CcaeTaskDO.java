package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * CCAE任务表DO
 * 
 * <AUTHOR>
 * @date 2025-12-30
 */
@Data
@TableName("WOC_CCAE_TASK")
public class CcaeTaskDO {

    /**
     * 主键ID
     */
    @TableId(value = "ID", type = IdType.ID_WORKER)
    @TableField("ID")
    private Long id;

    /**
     * CCAE任务ID
     */
    @TableField("CCAE_ID")
    private String ccaeId;

    /**
     * 通过检查项数量
     */
    @TableField("PASS_ITEM_COUNT")
    private Integer passItemCount;

    /**
     * 检查项总数量
     */
    @TableField("CHECK_ITEM_COUNT")
    private Integer checkItemCount;

    /**
     * 检查范围(专业组)：主机；网络；存储
     */
    @TableField("DEVICE_SCOPE")
    private String deviceScope;

    /**
     * 模板分类：Deep-深度健康评估；Basic-快速健康评估；Job-作业运行前检查
     */
    @TableField("TEMPLATE_CATEGORY")
    private String templateCategory;

    /**
     * 任务执行方式：0-立即执行；1-每日；7-每周；30-每月
     */
    @TableField("EXECUTE_METHOD")
    private Integer executeMethod;

    /**
     * 耗时（秒）
     */
    @TableField("ELAPSED_TIME_SEC")
    private Integer elapsedTimeSec;

    /**
     * 进度，-1表示停止，0-99表示进度，100表示完成
     */
    @TableField("PROGRESS")
    private Integer progress;

    /**
     * CCAE任务创建时间
     */
    @TableField("CCAE_CREATE_TIME")
    private LocalDateTime ccaeCreateTime;

    /**
     * CCAE任务结束时间
     */
    @TableField("CCAE_END_TIME")
    private LocalDateTime ccaeEndTime;

    /**
     * 任务停止时间
     */
    @TableField("STOP_TIME")
    private LocalDateTime stopTime;

    /**
     * 创建人ID
     */
    @TableField("CREATOR_ID")
    private Long creatorId;

    /**
     * 创建人姓名
     */
    @TableField("CREATOR_NAME")
    private String creatorName;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("MODIFY_TIME")
    private LocalDateTime modifyTime;

    /**
     * 计算节点列表(SN序列号)
     */
    @TableField(value = "COMPUTE_LIST", typeHandler = JacksonTypeHandler.class)
    private List<String> computeList;

    /**
     * 网络节点列表(IP列表)
     */
    @TableField(value = "NETWORK_IP_LIST", typeHandler = JacksonTypeHandler.class)
    private List<String> networkIpList;

    /**
     * 状态：1-未删除，0-已删除
     */
    @TableLogic(value = "1", delval = "0")
    @TableField("STATUS")
    private Integer status;
}
