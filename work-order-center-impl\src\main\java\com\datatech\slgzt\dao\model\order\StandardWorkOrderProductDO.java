package com.datatech.slgzt.dao.model.order;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 工单和产品json关联类
 *
 * @Author: liu<PERSON><PERSON>an
 * @Date: 2025/3/13
 */
@Data
@TableName("WOC_STANDARD_WORK_ORDER_PRODUCT")
public class StandardWorkOrderProductDO {


    //产品id
    @TableField("ID")
    private Long id;

    //工单Id
    @TableField("WORK_ORDER_ID")
    private String workOrderId;

    /**
     * 产品类型
     * esc
     * gsc
     * eip 等
     */
    @TableField("PRODUCT_TYPE")
    private String productType;


    //属性快照
    @TableField("PROPERTY_SNAPSHOT")
    private String propertySnapshot;

    //父类产品id 可以为空
    @TableField("PARENT_PRODUCT_ID")
    private Long parentProductId;

    /**
     * gid
     */
    @TableField("GID")
    private String gid;

    /**
     * gid
     */
    @TableField("SUB_ORDER_ID")
    private Long subOrderId;

    //开通状态
    @TableField("OPEN_STATUS")
    private String openStatus;

    //消息
    @TableField("MESSAGE")
    private String message;

    //Job执行ID
    @TableField("JOB_EXECUTION_ID")
    private Long jobExecutionId;

    @TableLogic(value = "1", delval = "0")
    @TableField("ENABLED")
    private Boolean enabled;

    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @TableField("MODIFY_TIME")
    private LocalDateTime modifyTime;

    /**
     * 串行开通状态，-1-多次失败，不再尝试，0-不需要串行开通，1-需要串行开通(但未开通)，2-串行开通待开通，3-串行开通开通中
     */
    @TableField("SERIAL_OPEN_STATUS")
    private Integer serialOpenStatus;

    /**
     * 区域代码
     */
    @TableField("REGION_CODE")
    private String regionCode;
}

