package com.datatech.slgzt.impl;

import com.datatech.slgzt.convert.HashrateResInfoManagerConvert;
import com.datatech.slgzt.dao.HashrateResInfoDAO;
import com.datatech.slgzt.dao.model.HashrateResInfoDO;
import com.datatech.slgzt.manager.HashrateResInfoManager;
import com.datatech.slgzt.model.dto.HashrateResInfoDTO;
import com.datatech.slgzt.model.query.HashrateResInfoQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 12月20日 11:12:04
 */
@Service
public class HashrateResInfoManagerImpl implements HashrateResInfoManager {


    @Resource
    private HashrateResInfoDAO hashrateResInfoDAO;

    @Resource
    private HashrateResInfoManagerConvert convert;


    /**
     * 智算资源查询
     *
     * @param query 智算资源查询参数
     * @return 智算资源列表
     */
    @Override
    public List<HashrateResInfoDTO> list(HashrateResInfoQuery query) {
        List<HashrateResInfoDO> list = hashrateResInfoDAO.list(query);
        return StreamUtils.mapArray(list, convert::do2Dto);
    }

    /**
     * page
     *
     * @param query
     */
    @Override
    public PageResult<HashrateResInfoDTO> page(HashrateResInfoQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<HashrateResInfoDO> list = hashrateResInfoDAO.list(query);
        return PageWarppers.box(new PageInfo<>(list), convert::do2Dto);
    }

    /**
     * insert
     *
     * @param dto
     */
    @Override
    public void insert(HashrateResInfoDTO dto) {
        HashrateResInfoDO hashrateResInfoDO = convert.dto2Do(dto);
        hashrateResInfoDAO.insert(hashrateResInfoDO);

    }

    @Override
    public HashrateResInfoDTO getById(Long id) {
        HashrateResInfoDO hashrateResInfoDO = hashrateResInfoDAO.getById(id);
        return convert.do2Dto(hashrateResInfoDO);
    }


    /**
     * delByDeviceId
     *
     * @param deviceId
     */
    @Override
    public void delByDeviceId(String deviceId) {
        hashrateResInfoDAO.delByDeviceId(deviceId);
    }

     /**
      * update
      */
    @Override
    public void update(HashrateResInfoDTO dto) {
        HashrateResInfoDO hashrateResInfoDO = convert.dto2Do(dto);
        hashrateResInfoDAO.update(hashrateResInfoDO);
    }

     /**
      * delById
      */
    @Override
    public void delById(Long id) {
        hashrateResInfoDAO.delById(id);
    }
}
