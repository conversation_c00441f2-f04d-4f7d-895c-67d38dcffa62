package com.datatech.slgzt.impl.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.datatech.slgzt.config.CcaeProperties;
import com.datatech.slgzt.helps.CcaeLoginHelper;
import com.datatech.slgzt.helps.CcaeUnpackHelper;
import com.datatech.slgzt.manager.CcaeManager;
import com.datatech.slgzt.model.to.CcaeComputeDeviceTO;
import com.datatech.slgzt.model.to.CcaeNeDeviceTO;
import com.datatech.slgzt.model.to.CcaeNetworkDeviceTO;
import com.datatech.slgzt.model.to.CcaeTemplateTO;
import com.datatech.slgzt.utils.OkHttpsUtils;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CcaeManagerImpl implements CcaeManager {
    @Resource
    private CcaeProperties ccaeProperties;

    @Override
    public JSONArray listCheckItem() throws NoSuchAlgorithmException, KeyManagementException {
        Mapper mapper = OkHttpsUtils
                .httpIgnoreHttpsCertificate()
                // 7.3.1 查询全量检查项
                .sync(ccaeProperties.getUrl() + "/api/v1/healthassessment/check-items")
                .addHeader("X-Auth-Token", CcaeLoginHelper.INSTANCE.getToken())
                .get()
                .getBody()
                .toMapper();
        String dataStr = CcaeUnpackHelper.unpackData(mapper, "调用底层登录接口失败");
        return JSON.parseArray(dataStr);
    }

    @Override
    public List<CcaeTemplateTO> listTemplate() throws NoSuchAlgorithmException, KeyManagementException {
        Mapper mapper = OkHttpsUtils
                .httpIgnoreHttpsCertificate()
                .sync(ccaeProperties.getUrl() + "/api/v1/healthassessment/templates")
                .addHeader("X-Auth-Token", CcaeLoginHelper.INSTANCE.getToken())
                .get()
                .getBody()
                .toMapper();
        String dataStr = CcaeUnpackHelper.unpackData(mapper, "调用底层登录接口失败");
        return JSON.parseArray(dataStr, CcaeTemplateTO.class);
    }

    @Override
    public CcaeTemplateTO getTemplateByResId(String resId) throws NoSuchAlgorithmException, KeyManagementException {
        Mapper mapper = OkHttpsUtils
                .httpIgnoreHttpsCertificate()
                .sync(ccaeProperties.getUrl() + "/api/v1/healthassessment/templates/" + resId)
                .addHeader("X-Auth-Token", CcaeLoginHelper.INSTANCE.getToken())
                .get()
                .getBody()
                .toMapper();
        String dataStr = CcaeUnpackHelper.unpackData(mapper, "调用底层登录接口失败");
        return JSON.parseObject(dataStr, CcaeTemplateTO.class);
    }

    @Override
    public List<CcaeComputeDeviceTO> listComputeDevice() throws NoSuchAlgorithmException, KeyManagementException {
        Mapper mapper = OkHttpsUtils
                .httpIgnoreHttpsCertificate()
                // 7.7.1.1.1.8 集群资源
                .sync(ccaeProperties.getUrl() + "/api/v1/inventory/compute/nodes")
                .addHeader("X-Auth-Token", CcaeLoginHelper.INSTANCE.getToken())
                .get()
                .getBody()
                .toMapper();
        String dataStr = CcaeUnpackHelper.unpackData(mapper, "调用底层接口失败");
        return JSON.parseArray(dataStr, CcaeComputeDeviceTO.class);
    }

    @Override
    public List<CcaeNeDeviceTO> listComputeNeDevices() throws Exception {
        Mapper mapper = OkHttpsUtils
                .httpIgnoreHttpsCertificate()
                // 7.7.1.1.1.1 计算域服务器资源
                .sync(ccaeProperties.getUrl() + "/api/v1/inventory/compute/nedevices")
                .addHeader("X-Auth-Token", CcaeLoginHelper.INSTANCE.getToken())
                .get()
                .getBody()
                .toMapper();
        String dataStr = CcaeUnpackHelper.unpackData(mapper, "调用底层登录接口失败");
        return JSON.parseArray(dataStr, CcaeNeDeviceTO.class);
    }

    @Override
    public List<CcaeNetworkDeviceTO> listNetworkDevice() throws NoSuchAlgorithmException, KeyManagementException {
        Mapper mapper = OkHttpsUtils
                .httpIgnoreHttpsCertificate()
                // 7.7.1.1.2.1 网络域交换机资源
                .sync(ccaeProperties.getUrl() + "/api/v1/inventory/network/nedevices")
                .addHeader("X-Auth-Token", CcaeLoginHelper.INSTANCE.getToken())
                .get()
                .getBody()
                .toMapper();
        String dataStr = CcaeUnpackHelper.unpackData(mapper, "调用底层接口失败");
        return JSON.parseArray(dataStr, CcaeNetworkDeviceTO.class);
    }

    @Override
    public String createNetworkTask(String taskName, JSONArray checkItems, List<String> networkIpList) throws NoSuchAlgorithmException, KeyManagementException {
        Map<String, Object> req = new HashMap<>();
        req.put("taskName", taskName);
        req.put("checkItems", checkItems);
        req.put("neIdType", "ipAddress");
        req.put("neIdLists", networkIpList);

        Mapper mapper = OkHttpsUtils
                .httpIgnoreHttpsCertificate()
                .sync(ccaeProperties.getUrl() + "/api/v1/healthassessment/cluster/network")
                .addHeader("X-Auth-Token", CcaeLoginHelper.INSTANCE.getToken())
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(req))
                .post()
                .getBody()
                .toMapper();
        String dataStr = CcaeUnpackHelper.unpackData(mapper, "调用底层登录接口失败");
        return JSON.parseObject(dataStr).getString("taskId");
    }

    @Override
    public String createComputeTask(String taskName, String templateName, String serverIdType, List<String> computeList) throws NoSuchAlgorithmException, KeyManagementException {
        Map<String, Object> req = new HashMap<>();
        req.put("taskName", taskName);
        req.put("checkTemplate", templateName);
        req.put("serverIdType", serverIdType);
        req.put("serverList", computeList);

        Mapper mapper = OkHttpsUtils
                .httpIgnoreHttpsCertificate()
                .sync(ccaeProperties.getUrl() + "/api/v1/healthassessment/cluster")
                .addHeader("X-Auth-Token", CcaeLoginHelper.INSTANCE.getToken())
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(req))
                .post()
                .getBody()
                .toMapper();
        String dataStr = CcaeUnpackHelper.unpackData(mapper, "调用底层登录接口失败");
        return JSON.parseObject(dataStr).getString("taskId");
    }
}

