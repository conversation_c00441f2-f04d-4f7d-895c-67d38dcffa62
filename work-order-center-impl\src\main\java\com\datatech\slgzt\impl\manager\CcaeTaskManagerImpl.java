package com.datatech.slgzt.impl.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datatech.slgzt.convert.CcaeTaskManagerConvert;
import com.datatech.slgzt.dao.CcaeTaskDAO;
import com.datatech.slgzt.dao.model.CcaeTaskDO;
import com.datatech.slgzt.manager.CcaeTaskManager;
import com.datatech.slgzt.model.dto.CcaeTaskDTO;
import com.datatech.slgzt.model.query.CcaeTaskQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.StreamUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * CCAE任务Manager实现类
 * 
 * <AUTHOR>
 * @date 2025-12-30
 */
@Service
public class CcaeTaskManagerImpl implements CcaeTaskManager {

    @Resource
    private CcaeTaskDAO ccaeTaskDAO;

    @Resource
    private CcaeTaskManagerConvert convert;

    @Override
    public CcaeTaskDTO getById(Long id) {
        CcaeTaskDO ccaeTaskDO = ccaeTaskDAO.getById(id);
        return convert.do2dto(ccaeTaskDO);
    }

    @Override
    public CcaeTaskDTO getByCcaeId(String ccaeId) {
        CcaeTaskDO ccaeTaskDO = ccaeTaskDAO.getByCcaeId(ccaeId);
        return convert.do2dto(ccaeTaskDO);
    }

    @Override
    public void save(CcaeTaskDTO ccaeTaskDTO) {
        CcaeTaskDO ccaeTaskDO = convert.dto2do(ccaeTaskDTO);
        if (ccaeTaskDO.getId() == null) {
            ccaeTaskDO.setCreateTime(LocalDateTime.now());
            ccaeTaskDO.setModifyTime(LocalDateTime.now());
            // 新增时默认设置为未删除状态
            if (ccaeTaskDO.getStatus() == null) {
                ccaeTaskDO.setStatus(1);
            }
            ccaeTaskDAO.insert(ccaeTaskDO);
        } else {
            ccaeTaskDO.setModifyTime(LocalDateTime.now());
            ccaeTaskDAO.updateById(ccaeTaskDO);
        }
    }

    @Override
    public void updateById(CcaeTaskDTO ccaeTaskDTO) {
        CcaeTaskDO ccaeTaskDO = convert.dto2do(ccaeTaskDTO);
        ccaeTaskDO.setModifyTime(LocalDateTime.now());
        ccaeTaskDAO.updateById(ccaeTaskDO);
    }

    @Override
    public void deleteById(Long id) {
        ccaeTaskDAO.deleteById(id);
    }

    @Override
    public List<CcaeTaskDTO> list(CcaeTaskQuery query) {
        List<CcaeTaskDO> ccaeTaskDOList = ccaeTaskDAO.list(query);
        return StreamUtils.mapArray(ccaeTaskDOList, convert::do2dto);
    }

    @Override
    public PageResult<CcaeTaskDTO> page(CcaeTaskQuery query) {
        Page<CcaeTaskDO> page = ccaeTaskDAO.page(query);
        List<CcaeTaskDTO> records = StreamUtils.mapArray(page.getRecords(), convert::do2dto);
        return new PageResult<>(records, page.getTotal(), page.getCurrent(), page.getSize());
    }

    @Override
    public void updateProgressByCcaeId(String ccaeId, Integer progress) {
        CcaeTaskDO existingTask = ccaeTaskDAO.getByCcaeId(ccaeId);
        if (existingTask != null) {
            existingTask.setProgress(progress);
            existingTask.setModifyTime(LocalDateTime.now());
            ccaeTaskDAO.updateById(existingTask);
        }
    }

    @Override
    public void updateTaskStatusByCcaeId(String ccaeId, Integer progress, Integer passItemCount, Integer checkItemCount, Integer elapsedTimeSec) {
        CcaeTaskDO existingTask = ccaeTaskDAO.getByCcaeId(ccaeId);
        if (existingTask != null) {
            existingTask.setProgress(progress);
            existingTask.setPassItemCount(passItemCount);
            existingTask.setCheckItemCount(checkItemCount);
            existingTask.setElapsedTimeSec(elapsedTimeSec);
            existingTask.setModifyTime(LocalDateTime.now());
            
            // 如果任务完成，设置结束时间
            if (progress != null && progress == 100) {
                existingTask.setCcaeEndTime(LocalDateTime.now());
            }
            
            ccaeTaskDAO.updateById(existingTask);
        }
    }
}
