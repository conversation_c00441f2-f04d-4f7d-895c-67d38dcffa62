package com.datatech.slgzt.impl.manager;

import com.datatech.slgzt.convert.PerformanceAggregateManagerConvert;
import com.datatech.slgzt.dao.mapper.CKBandwidthPerformanceMapper;
import com.datatech.slgzt.dao.mapper.CKVmPerformanceMapper;
import com.datatech.slgzt.dao.model.CKBandwidthPerformanceDO;
import com.datatech.slgzt.dao.model.VMResourcePerformanceDO;
import com.datatech.slgzt.manager.PerformanceAggregateManager;
import com.datatech.slgzt.manager.RegionCodeManager;
import com.datatech.slgzt.model.dto.CKBandwidthPerformanceDTO;
import com.datatech.slgzt.model.dto.RegionCodeDTO;
import com.datatech.slgzt.model.dto.VMResourcePerformanceDTO;
import com.datatech.slgzt.model.dto.VMResourcePerformanceStringDTO;
import com.datatech.slgzt.utils.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 性能数据聚合Manager实现类
 *
 * <AUTHOR>
 * @date 2025-12-21
 */
@Service
public class PerformanceAggregateManagerImpl implements PerformanceAggregateManager {

    @Resource
    private CKVmPerformanceMapper ckVmPerformanceMapper;

    @Resource
    private CKBandwidthPerformanceMapper ckBandwidthPerformanceMapper;

    @Resource
    private PerformanceAggregateManagerConvert performanceAggregateManagerConvert;

    @Resource
    private RegionCodeManager regionCodeManager;

    @Override
    public List<VMResourcePerformanceDTO> aggregateVmPerformance(LocalDateTime startTime, LocalDateTime endTime, List<String> regionCodes) {
        List<VMResourcePerformanceDO> vmResourcePerformanceDOList = ckVmPerformanceMapper.aggregateVmPerformance(startTime, endTime, regionCodes);
        return performanceAggregateManagerConvert.vmDos2dtos(vmResourcePerformanceDOList);
    }

    @Override
    public List<CKBandwidthPerformanceDTO> aggregateBandwidthPerformance(LocalDateTime startTime, LocalDateTime endTime, List<String> regionCodes) {
        List<CKBandwidthPerformanceDO> ckBandwidthPerformanceDOList = ckBandwidthPerformanceMapper.aggregateBandwidthPerformance(startTime, endTime, regionCodes);
        return performanceAggregateManagerConvert.bandwidthDos2dtos(ckBandwidthPerformanceDOList);
    }

    @Override
    public PageResult<VMResourcePerformanceDTO> listVmPerformancePage(Integer pageNum, Integer pageSize, LocalDateTime startTime, LocalDateTime endTime, List<String> regionCodes) {
        // 手动分页计算
        pageNum = pageNum == null ? 1 : pageNum;
        pageSize = pageSize == null ? 10 : pageSize;

        // 计算偏移量
        long offset = (long) (pageNum - 1) * pageSize;
        long limit = pageSize;

        // 查询总数
        Long total = ckVmPerformanceMapper.countVmPerformance(startTime, endTime, regionCodes);

        // 查询分页数据
        List<VMResourcePerformanceDO> vmResourcePerformanceDOList = ckVmPerformanceMapper.listVmPerformanceWithPage(
                startTime, endTime, regionCodes, offset, limit);
        if (vmResourcePerformanceDOList == null || vmResourcePerformanceDOList.isEmpty()) {
            return new PageResult<>();
        }
        // 获取VMResourcePerformanceDO的resourcePoolCode的集合
        Set<String> resourcePoolCodes = vmResourcePerformanceDOList.stream()
                .map(VMResourcePerformanceDO::getResourcePoolCode)
                .collect(Collectors.toSet());
        List<RegionCodeDTO> regionCodeDTOList = regionCodeManager.listByCodes(resourcePoolCodes);

        // 转换为DTO并设置区域信息
        List<VMResourcePerformanceDTO> dtoList = vmResourcePerformanceDOList.stream()
                .map(vmResourcePerformanceDO -> {
                    VMResourcePerformanceDTO dto = performanceAggregateManagerConvert.vmDo2dto(vmResourcePerformanceDO);
                    RegionCodeDTO regionCodeDTO = regionCodeDTOList.stream()
                            .filter(i -> i.getRegionCode().equals(vmResourcePerformanceDO.getResourcePoolCode()))
                            .findFirst()
                            .orElse(null);
                    if (regionCodeDTO != null) {
                        dto.setResourcePoolName(regionCodeDTO.getRegionName());
                        dto.setDomainName(regionCodeDTO.getCloudName());
                    }
                    return dto;
                }).collect(Collectors.toList());

        // 手动构建分页结果
        PageResult<VMResourcePerformanceDTO> pageResult = new PageResult<>();
        pageResult.setRecords(dtoList);
        pageResult.setTotal(total);
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);

        return pageResult;
    }

    @Override
    public VMResourcePerformanceStringDTO aggregateVmPerformanceTotal(LocalDateTime startTime, LocalDateTime endTime, List<String> regionCodes) {
        return ckVmPerformanceMapper.aggregateVmPerformanceTotal(startTime, endTime, regionCodes);
    }

    @Override
    public CKBandwidthPerformanceDTO aggregateBandwidthPerformanceTotal(LocalDateTime startTime, LocalDateTime endTime, List<String> regionCodes) {
        CKBandwidthPerformanceDO ckBandwidthPerformanceDO = ckBandwidthPerformanceMapper.aggregateBandwidthPerformanceTotal(startTime, endTime, regionCodes);
        return performanceAggregateManagerConvert.bandwidthDo2dto(ckBandwidthPerformanceDO);
    }
}
