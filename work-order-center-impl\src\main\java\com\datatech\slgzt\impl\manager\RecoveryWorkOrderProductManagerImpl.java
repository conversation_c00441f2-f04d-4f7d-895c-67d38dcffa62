package com.datatech.slgzt.impl.manager;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.convert.RecoveryWorkOrderProductManagerConvert;
import com.datatech.slgzt.dao.RecoveryWorkOrderProductDAO;
import com.datatech.slgzt.dao.model.order.RecoveryWorkOrderProductDO;
import com.datatech.slgzt.manager.RecoveryWorkOrderProductManager;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderProductDTO;
import com.datatech.slgzt.utils.StreamUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RecoveryWorkOrderProductManagerImpl implements RecoveryWorkOrderProductManager {

    @Resource
    private RecoveryWorkOrderProductDAO dao;

    @Resource
    private RecoveryWorkOrderProductManagerConvert convert;

    @Override
    public void insert(RecoveryWorkOrderProductDTO productDTO) {
        dao.insert(convert.dto2do(productDTO));
    }

    @Override
    public RecoveryWorkOrderProductDTO getById(Long id) {
        return convert.do2dto(dao.getById(id));
    }

    @Override
    public void update(RecoveryWorkOrderProductDTO productDTO) {
        dao.update(convert.dto2do(productDTO));
    }


    @Override
    public List<RecoveryWorkOrderProductDTO> listByWorkOrderId(String workOrderId) {
        return StreamUtils.mapArray(dao.listByWorkOrderId(workOrderId), convert::do2dto);
    }

    @Override
    public void updateStatusByIds(List<Long> ids, String status) {
        dao.updateByIds(ids, status);
    }

    @Override
    public void updateByResourceDetailId(String resourceDetailId, String status, String message) {
        dao.updateByResourceDetailId(resourceDetailId, status, message);
    }

    @Override
    public void updateStatusByParentId(Long id, String status) {
        RecoveryWorkOrderProductDO recoveryWorkOrderProductDO = new RecoveryWorkOrderProductDO();
        recoveryWorkOrderProductDO.setParentProductId(id);
        recoveryWorkOrderProductDO.setRecoveryStatus(status);
        dao.updateByParentId(recoveryWorkOrderProductDO);
    }

    @Override
    public RecoveryWorkOrderProductDTO getBySubOrderId(Long subOrderId) {
        return convert.do2dto(dao.getBySubOrderId(subOrderId));
    }

    @Override
    public List<RecoveryWorkOrderProductDTO> getByIds(List<Long> ids) {
        return StreamUtils.mapArray(dao.getByIds(ids), convert::do2dto);
    }

    @Override
    public void updateHcmByCmdbIds(List<String> configIds, String status) {
        dao.updateHcmByCmdbIds(configIds, status);
    }
    @Override
    public void updateHcmByIds(List<Long> ids, String status) {
        dao.updateHcmByIds(ids, status);
    }


    @Override
    public void updateTenantConfirmByIds(List<Long> ids, Boolean tenantConfirm) {
        dao.updateTenantConfirmByIds(ids, tenantConfirm);
    }

    @Override
    public void deleteByWorkOrderId(String workOrderId) {
        dao.deleteByWorkOrderId(workOrderId);
    }
    public RecoveryWorkOrderProductDTO getByCmdbId(String cmdbId) {
        return convert.do2dto(dao.getByCmdbId(cmdbId));
    }

    @Override
    public RecoveryWorkOrderProductDTO listSerialRecovering() {
        return convert.do2dto(dao.listSerialRecovering());
    }

    @Override
    public RecoveryWorkOrderProductDTO listNextSerialRecovery() {
        return convert.do2dto(dao.listNextSerialRecovery());
    }

    @Override
    public int countSerialRecovering() {
        return dao.countSerialRecovering();
    }

    @Override
    public void updateSerialRecoveryStatusByParentId(Long parentProductId, Integer serialRecoveryStatus) {
        dao.updateSerialRecoveryStatusByParentId(parentProductId, serialRecoveryStatus);
    }

    @Override
    public void updateSerialRecoveryStatusByOrderId(String workOrderId, String regionCode, Integer oldSerialRecoveryStatus, Integer newSerialRecoveryStatus) {
        dao.updateSerialRecoveryStatusByOrderId(workOrderId, regionCode, oldSerialRecoveryStatus, newSerialRecoveryStatus);
    }

    /**
     * 按REGION_CODE分组查询每个资源池中最早的待回收产品
     * @return Map<String, RecoveryWorkOrderProductDTO> key为regionCode，value为该资源池中最早的待回收产品
     */
    @Override
    public Map<String, RecoveryWorkOrderProductDTO> listNextSerialRecoveryByRegionCode() {
        List<RecoveryWorkOrderProductDO> productDOList = dao.listNextSerialRecoveryByRegionCode();
        return productDOList.stream()
                .collect(Collectors.toMap(
                        product -> product.getRegionCode() != null ? product.getRegionCode() : "null",
                        convert::do2dto,
                        (existing, replacement) -> existing // 如果有重复key，保留第一个
                ));
    }
}

