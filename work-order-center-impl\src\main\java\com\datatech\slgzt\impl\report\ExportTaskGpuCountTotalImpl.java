package com.datatech.slgzt.impl.report;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.component.FtpConfig;
import com.datatech.slgzt.config.DeptMapperProperties;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.ExportTaskManager;
import com.datatech.slgzt.manager.HashrateResInfoManager;
import com.datatech.slgzt.model.dto.ExportTaskDTO;
import com.datatech.slgzt.model.dto.HashrateResInfoDTO;
import com.datatech.slgzt.model.opm.ExportTaskOpm;
import com.datatech.slgzt.model.query.HashrateResInfoQuery;
import com.datatech.slgzt.model.report.VmCountReportExcelDTO;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.service.ExportTaskService;
import com.datatech.slgzt.utils.FTPUtil;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.collect.ArrayListMultimap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * GPU报表导出服务实现
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Service
public class ExportTaskGpuCountTotalImpl implements ExportTaskService {

    private final ExecutorService executor = Executors.newFixedThreadPool(2);

    @Resource
    private ExportTaskManager exportTaskManager;

    @Resource
    private HashrateResInfoManager hashrateResInfoManager;

    @Resource
    private DeptMapperProperties deptMapperProperties;

    @Resource
    private FtpConfig ftpConfig;

    private static final String EXPORT_PATH = "export/gpu/";
    private static final String BUSINESS_FILE_NAME = "GPU_COUNT_TOTAL";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final String TEMPLATE_PATH = "templates/gpu_count_total.xlsx";

    @Override
    public void export(ExportTaskOpm opm) {
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();

        // 创建导出任务记录
        ExportTaskDTO taskDTO = new ExportTaskDTO();
        taskDTO.setReportName(opm.getReportName());
        taskDTO.setBusinessType(getReportType());
        taskDTO.setStatType(opm.getStatType());
        taskDTO.setStartTime(opm.getStartTime());
        taskDTO.setEndTime(opm.getEndTime());
        taskDTO.setCreator(currentUser.getUserName());
        taskDTO.setQueryCondition(opm.getQueryCondition());
        taskDTO.setStatus(0);
        taskDTO.setCreateTime(LocalDateTime.now());
        taskDTO.setExportFields(JSON.toJSONString(opm.getExportFields()));
        String taskId = exportTaskManager.createTask(taskDTO);
        taskDTO.setId(taskId);

        executor.execute(() -> {
            String fileName = generateExcelFileName();
            String filePath = EXPORT_PATH + fileName;
            taskDTO.setFileName(fileName);

            File exportDir = new File(EXPORT_PATH);
            if (!exportDir.exists()) {
                exportDir.mkdirs();
            }

            try {
                // 解析部门映射配置
                String mapperStr = deptMapperProperties.getMapperStr();
                JSONArray detpJsonArray = JSON.parseArray(mapperStr);
                Map<String, List<String>> deptMap = new LinkedHashMap<>(); // 保持顺序
                detpJsonArray.forEach(item -> {
                    JSONObject jsonObject = (JSONObject) item;
                    String deptName = jsonObject.getString("label");
                    String childDeptNames = jsonObject.getString("value");
                    deptMap.put(deptName, Arrays.asList(childDeptNames.split(",")));
                });

                // 1. 获取全部资源信息
                List<HashrateResInfoDTO> allResInfoList = hashrateResInfoManager.list(new HashrateResInfoQuery());

                // 2. 全部VM ID集合（去重）
                Set<String> allVmIds = allResInfoList.stream()
                                                     .filter(Objects::nonNull)
                                                     .filter(res -> StringUtils.isNotBlank(res.getDeviceId()))
                                                     .map(HashrateResInfoDTO::getDeviceId)
                                                     .collect(Collectors.toSet());

                // 3. 全部数据按型号分组，并按VM ID去重统计
                Map<String, Set<String>> allModelVmIds = new HashMap<>();
                allResInfoList.stream()
                              .filter(Objects::nonNull)
                              .filter(res -> StringUtils.isNotBlank(res.getModel()))
                              .filter(res -> StringUtils.isNotBlank(res.getDeviceId()))
                              .forEach(res -> {
                                  allModelVmIds.computeIfAbsent(res.getModel(), k -> new HashSet<>())
                                               .add(res.getDeviceId());
                              });

                // 4. 用于收集所有已分配的VM ID
                Set<String> allocatedVmIds = new HashSet<>();
                // 用于收集所有已分配的资源（按型号）
                Map<String, Set<String>> allocatedModelVmIds = new HashMap<>();
                allocatedModelVmIds.put("910B2", new HashSet<>());
                allocatedModelVmIds.put("910B4", new HashSet<>());
                allocatedModelVmIds.put("910C", new HashSet<>());

                ExcelWriter excelWriter = EasyExcel.write(filePath, VmCountReportExcelDTO.class)
                                                   .build();
                WriteSheet writeSheet = EasyExcel.writerSheet("云主机汇总").build();

                List<VmCountReportExcelDTO> allExportData = new ArrayList<>();
                AtomicInteger serialNumber = new AtomicInteger(1);

                // 5. 遍历各部门统计
                deptMap.forEach((deptName, childDeptNames) -> {
                    // 查询该部门的资源
                    List<HashrateResInfoDTO> resInfoList = hashrateResInfoManager.list(
                            new HashrateResInfoQuery().setDeptNameList(childDeptNames)
                    );

                    // 该部门的VM ID集合
                    Set<String> deptVmIds = resInfoList.stream()
                                                       .filter(Objects::nonNull)
                                                       .filter(res -> StringUtils.isNotBlank(res.getDeviceId()))
                                                       .map(HashrateResInfoDTO::getDeviceId)
                                                       .collect(Collectors.toSet());

                    // 按型号收集VM ID（去重）
                    Map<String, Set<String>> deptModelVmIds = new HashMap<>();
                    resInfoList.stream()
                               .filter(Objects::nonNull)
                               .filter(res -> StringUtils.isNotBlank(res.getModel()))
                               .filter(res -> StringUtils.isNotBlank(res.getDeviceId()))
                               .forEach(res -> {
                                   deptModelVmIds.computeIfAbsent(res.getModel(), k -> new HashSet<>())
                                                 .add(res.getDeviceId());
                               });

                    // 收集已分配的VM（用于后续计算剩余）
                    allocatedVmIds.addAll(deptVmIds);

                    // 按型号收集已分配的VM ID
                    deptModelVmIds.forEach((model, vmIds) -> {
                        allocatedModelVmIds.computeIfAbsent(model, k -> new HashSet<>()).addAll(vmIds);
                    });

                    // 构建导出数据
                    VmCountReportExcelDTO excelDTO = new VmCountReportExcelDTO();
                    excelDTO.setSerialNumber(String.valueOf(serialNumber.getAndIncrement()));
                    excelDTO.setDeptName(deptName);
                    excelDTO.setVmCount(deptVmIds.size());
                    // 按VM ID去重后的数量
                    excelDTO.setModel910B2Count(String.valueOf(
                            deptModelVmIds.getOrDefault("910B2", Collections.emptySet()).size()
                    ));
                    excelDTO.setModel910B4Count(String.valueOf(
                            deptModelVmIds.getOrDefault("910B4", Collections.emptySet()).size()
                    ));
                    excelDTO.setModel910CCount(String.valueOf(
                            deptModelVmIds.getOrDefault("910C", Collections.emptySet()).size()
                    ));

                    allExportData.add(excelDTO);
                });

                // 6. 计算剩余数量（总数 - 已分配）
                Set<String> remainingVmIds = new HashSet<>(allVmIds);
                remainingVmIds.removeAll(allocatedVmIds);

                // 计算各型号剩余数量（按VM ID）
                Map<String, Integer> remainingModelCount = new HashMap<>();
                for (String model : Arrays.asList("910B2", "910B4", "910C")) {
                    Set<String> totalModelVmIds = allModelVmIds.getOrDefault(model, new HashSet<>());
                    Set<String> allocatedModelVms = allocatedModelVmIds.getOrDefault(model, new HashSet<>());

                    // 剩余 = 该型号全部VM - 该型号已分配VM
                    Set<String> remainingModelVmIds = new HashSet<>(totalModelVmIds);
                    remainingModelVmIds.removeAll(allocatedModelVms);
                    remainingModelCount.put(model, remainingModelVmIds.size());
                }

                // 添加剩余行
                VmCountReportExcelDTO remainingDTO = new VmCountReportExcelDTO();
                remainingDTO.setSerialNumber(String.valueOf(serialNumber.getAndIncrement()));
                remainingDTO.setDeptName("剩余");
                remainingDTO.setVmCount(remainingVmIds.size());
                remainingDTO.setModel910B2Count(String.valueOf(remainingModelCount.getOrDefault("910B2", 0)));
                remainingDTO.setModel910B4Count(String.valueOf(remainingModelCount.getOrDefault("910B4", 0)));
                remainingDTO.setModel910CCount(String.valueOf(remainingModelCount.getOrDefault("910C", 0)));
                allExportData.add(remainingDTO);

                // 7. 添加汇总行
                VmCountReportExcelDTO totalDTO = new VmCountReportExcelDTO();
                totalDTO.setSerialNumber("汇总");
                totalDTO.setDeptName("");
                totalDTO.setVmCount(allVmIds.size());
                // 汇总也要按VM ID去重
                totalDTO.setModel910B2Count(String.valueOf(
                        allModelVmIds.getOrDefault("910B2", Collections.emptySet()).size()
                ));
                totalDTO.setModel910B4Count(String.valueOf(
                        allModelVmIds.getOrDefault("910B4", Collections.emptySet()).size()
                ));
                totalDTO.setModel910CCount(String.valueOf(
                        allModelVmIds.getOrDefault("910C", Collections.emptySet()).size()
                ));
                allExportData.add(totalDTO);

                // 写入Excel
                excelWriter.write(allExportData, writeSheet);
                excelWriter.finish();

                // 上传到FTP
                uploadToFtp(filePath, taskDTO);

                taskDTO.setStatus(1);
                exportTaskManager.updateTask(taskDTO);
                log.info("VM count report export completed successfully. File path: {}", filePath);

            } catch (Exception e) {
                log.error("VM count report export failed", e);
                taskDTO.setStatus(2);
                exportTaskManager.updateTask(taskDTO);
            }
        });
    }

    /**
     * 复制模板文件到指定路径
     */
    private void copyTemplateToFile(String targetPath) throws IOException {
        ClassPathResource templateResource = new ClassPathResource(TEMPLATE_PATH);
        try (InputStream inputStream = templateResource.getInputStream()) {
            File targetFile = new File(targetPath);
            Files.copy(inputStream, targetFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            log.info("Template file copied to: {}", targetPath);
        }
    }

    /**
     * 上传文件到FTP
     */
    private void uploadToFtp(String filePath, ExportTaskDTO taskDTO) throws IOException {
        FTPUtil ftpUtil = new FTPUtil(ftpConfig.getIp(), ftpConfig.getPort(),
                ftpConfig.getUser(), ftpConfig.getPass());
        String remotePath = ftpConfig.getBasePath() + new SimpleDateFormat("yyyy-MM-dd/").format(new Date());
        File localFile = new File(filePath);
        boolean uploadResult = ftpUtil.uploadFile(remotePath, localFile);

        if (uploadResult) {
            String ftpFilePath = remotePath + localFile.getName();
            taskDTO.setFilePath(ftpFilePath);
            taskDTO.setFileName(localFile.getName());
            exportTaskManager.updateTask(taskDTO);
            log.info("GPU monthly total report file uploaded to FTP successfully: {}", ftpFilePath);
        } else {
            log.error("Failed to upload GPU monthly total report file to FTP server");
            throw new RuntimeException("Failed to upload GPU monthly total report file to FTP server");
        }
    }

    private String generateExcelFileName() {
        String timestamp = LocalDateTime.now().format(DATE_FORMATTER);
        return String.format("%s_%s.xlsx", BUSINESS_FILE_NAME, timestamp);
    }

    @Override
    public String getReportType() {
        return "GPU_COUNT_TOTAL";
    }
} 