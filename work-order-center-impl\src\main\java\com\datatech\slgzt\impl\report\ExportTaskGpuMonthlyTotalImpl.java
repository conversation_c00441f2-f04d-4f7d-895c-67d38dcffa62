package com.datatech.slgzt.impl.report;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.component.FtpConfig;
import com.datatech.slgzt.enums.DeviceModelTFEnum;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.dto.ExportTaskDTO;
import com.datatech.slgzt.model.dto.HashrateResInfoDTO;
import com.datatech.slgzt.model.opm.ExportTaskOpm;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.model.query.DeviceMetricQuery;
import com.datatech.slgzt.model.query.HashrateResInfoQuery;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.service.ExportTaskService;
import com.datatech.slgzt.utils.FTPUtil;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * GPU报表导出服务实现
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Service
public class ExportTaskGpuMonthlyTotalImpl implements ExportTaskService {

    ExecutorService executor = Executors.newFixedThreadPool(2);

    @Resource
    private ExportTaskManager exportTaskManager;

    @Resource
    private DeviceCardMetricsManager deviceCardMetricsManager;

    @Resource
    private HashrateResInfoManager hashrateResInfoManager;

    @Resource
    private DeviceGpuInfoManager deviceGpuInfoManager;

    @Resource
    private FtpConfig ftpConfig;

    private static final String EXPORT_PATH = "export/gpu/";
    private static final String BUSINESS_FILE_NAME = "GPU_MONTHLY_TOTAL";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final String TEMPLATE_PATH = "templates/gpu_monthly_template.xlsx";

    /**
     * 区域配置类
     */
    private static class RegionConfig {
        String regionKey;
        String domainName;
        String subModelName;
        //状态
        List<String> status;

        public RegionConfig(String regionKey, String domainName, String subModelName, List<String> status) {
            this.regionKey = regionKey;
            this.domainName = domainName;
            this.subModelName = subModelName;
            this.status = status;
        }
    }

    /**
     * 获取所有区域配置
     */
    private List<RegionConfig> getRegionConfigs() {
        List<RegionConfig> configs = new ArrayList<>();

        // n1 区域：平台云-萧山02，910B2型号
        configs.add(new RegionConfig("n1", "智算中心-N节点",
                "910B2",null));

        // zj1 区域：平台云-萧山02
        configs.add(new RegionConfig("zj1",
                "智算中心-自建池",
                "910B4",null));

        // zj1 区域：平台云-萧山02
        configs.add(new RegionConfig("zj2",
                "智算云",
                "910C",null));

        // w1 区域：平台云-萧山03、04
        configs.add(new RegionConfig("w1",
                "网络云省内",
                "T4",null));

        // r1 区域：多个融合边缘云
        configs.add(new RegionConfig("r1",
                "融合边缘云",
                "T4",Lists.newArrayList("已分配","未分配")));

        // r3 区域：包含舟山的融合边缘云
        configs.add(new RegionConfig("r3",
                "融合边缘云",
                "300I",Lists.newArrayList("已分配","未分配")));

        return configs;
    }

    @Override
    public void export(ExportTaskOpm opm) {
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();

        // 创建导出任务记录
        ExportTaskDTO taskDTO = new ExportTaskDTO();
        taskDTO.setReportName(opm.getReportName());
        taskDTO.setBusinessType(getReportType());
        taskDTO.setStatType(opm.getStatType());
        taskDTO.setStartTime(opm.getStartTime());
        taskDTO.setEndTime(opm.getEndTime());
        taskDTO.setCreator(currentUser.getUserName());
        taskDTO.setQueryCondition(opm.getQueryCondition());
        taskDTO.setStatus(0);
        taskDTO.setCreateTime(LocalDateTime.now());
        taskDTO.setExportFields(JSON.toJSONString(opm.getExportFields()));
        String taskId = exportTaskManager.createTask(taskDTO);
        taskDTO.setId(taskId);

        executor.execute(() -> {
            String fileName = generateExcelFileName();
            String filePath = EXPORT_PATH + fileName;
            taskDTO.setFileName(fileName);

            File exportDir = new File(EXPORT_PATH);
            if (!exportDir.exists()) {
                exportDir.mkdirs();
            }

            try {
                Map<String, Object> dataMap = buildTemplateData(opm);

                InputStream templateInputStream = new ClassPathResource(TEMPLATE_PATH).getInputStream();
                ExcelWriter excelWriter = EasyExcel.write(filePath)
                                                   .withTemplate(templateInputStream)
                                                   .build();
                WriteSheet writeSheet = EasyExcel.writerSheet().build();

                excelWriter.fill(dataMap, writeSheet);
                excelWriter.finish();
                templateInputStream.close();

                uploadToFtp(filePath, taskDTO);

                taskDTO.setStatus(1);
                exportTaskManager.updateTask(taskDTO);
                log.info("GPU monthly total export completed successfully. File path: {}", filePath);

            } catch (Exception e) {
                log.error("GPU monthly total export failed", e);
                taskDTO.setStatus(2);
                exportTaskManager.updateTask(taskDTO);
            }
        });
    }

    /**
     * 构建模板填充数据
     */
    private Map<String, Object> buildTemplateData(ExportTaskOpm opm) {
        Map<String, Object> dataMap = new HashMap<>();

        // 计算时间范围
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime firstDayOfMonth = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime lastDayOfMonth = now.withDayOfMonth(now.toLocalDate().lengthOfMonth())
                                          .withHour(23).withMinute(59).withSecond(59).withNano(999999999);

        // 添加月份信息
        dataMap.put("month", String.valueOf(now.getMonthValue()));

        // 获取所有区域配置并处理
        List<RegionConfig> regionConfigs = getRegionConfigs();
        for (RegionConfig config : regionConfigs) {
            try {
                Map<String, Object> regionData = processRegionData(
                        config,
                        firstDayOfMonth,
                        lastDayOfMonth
                );
                dataMap.putAll(regionData);
            } catch (Exception e) {
                log.error("Failed to process region data for: {}", config.regionKey, e);
                // 设置默认值，避免模板填充失败
                dataMap.put(config.regionKey + "_gpuUtil", "0.00");
                dataMap.put(config.regionKey + "_memUtil", "0.00");
            }
        }

        return dataMap;
    }

    /**
     * 处理单个区域的数据（核心复用方法）
     */
    private Map<String, Object> processRegionData(RegionConfig config,
                                                  LocalDateTime startTime,
                                                  LocalDateTime endTime) {
        Map<String, Object> result = new HashMap<>();
        //增加数量部分
        List<HashrateResInfoDTO> hashrateResInfoList = hashrateResInfoManager.list(new HashrateResInfoQuery()
                .setModelName(config.subModelName)
                .setAllocationStatusList(config.status)
                .setDomainName(config.domainName));
        //设置总数
        result.put(config.regionKey + "_deviceCount", hashrateResInfoList.size());
        //设置内部使用数
        result.put(config.regionKey + "_internalUseCount", hashrateResInfoList.stream()
                .filter(dto -> dto.getProductCategory() != null && dto.getProductCategory().equals("内部使用"))
                .count());
        //设置对外售卖数
        result.put(config.regionKey + "_externalSaleCount", hashrateResInfoList.stream()
                .filter(dto -> dto.getProductCategory() != null && dto.getProductCategory().equals("对外售卖"))
                .count());
        //设置IDC客户现场数
        result.put(config.regionKey + "_idcCustomerCount", hashrateResInfoList.stream()
                .filter(dto -> dto.getProductCategory() != null && dto.getProductCategory().equals("IDC客户现场"))
                .count());
        //设置算力（TFLOPS）总数*系数DeviceModelTFEnum 里可以找到系数
        DeviceModelTFEnum tfEnum = DeviceModelTFEnum.getByModelName(config.subModelName);
        if (tfEnum!=null){
            double tfFactor = tfEnum.getTflops();
            double totalTf = hashrateResInfoList.size() * tfFactor;
            result.put(config.regionKey + "_totalTF", BigDecimal.valueOf(totalTf)
                    .setScale(2, RoundingMode.HALF_UP).toString());
        } else {
            result.put(config.regionKey + "_totalTF", "0.00");
        }
        //设置分配率 查询getAllocationStatus=已分配的数量/总数量
        long allocatedCount = hashrateResInfoList.stream()
                .filter(dto -> dto.getAllocationStatus() != null && dto.getAllocationStatus().equals("已分配"))
                .count();
        //分配率要乘100
        double allocationRate = hashrateResInfoList.isEmpty() ? 0.0 : (double) allocatedCount / hashrateResInfoList.size() * 100;
        result.put(config.regionKey + "_allocationRate", BigDecimal.valueOf(allocationRate)
                .setScale(2, RoundingMode.HALF_UP).toString()+"%");



        // 1. 构建查询条件
        DeviceInfoQuery deviceQuery = new DeviceInfoQuery().setCatalogueDomainName(config.domainName);
        if (config.subModelName != null) {
            deviceQuery.setModelNames(Collections.singletonList(config.subModelName));
        }

        // 2. 查询设备信息
        List<DeviceGpuInfoDTO> deviceList = deviceGpuInfoManager.selectDeviceGpuInfoList(deviceQuery);

        // 3. 过滤并提取设备ID
        List<String> deviceIdList = extractDeviceIds(deviceList);

        if (deviceIdList.isEmpty()) {
            log.warn("No valid device IDs found for region: {}", config.regionKey);
            result.put(config.regionKey + "_gpuUtil", "0.00");
            result.put(config.regionKey + "_memUtil", "0.00");
            return result;
        }

        // 4. 查询性能指标数据
        List<DeviceCardMetricsDTO> metricsData = deviceCardMetricsManager.queryGpuMetricsAggregated(
                deviceIdList,
                new DeviceMetricQuery()
                        .setStartTime(startTime)
                        .setEndTime(endTime)
                        .setStatType("MONTH")
        );

        // 5. 计算平均值
        Map<String, Double> averages = calculateAverages(metricsData);

        // 6. 格式化并填充结果（所有区域都包含 GPU 和内存利用率）
        result.put(config.regionKey + "_gpuUtil", formatPercentage(averages.get("gpuUtil")));
        result.put(config.regionKey + "_memUtil", formatPercentage(averages.get("memUtil")));

        log.info("Processed region {}: GPU={}, MEM={}",
                config.regionKey,
                result.get(config.regionKey + "_gpuUtil"),
                result.get(config.regionKey + "_memUtil"));

        return result;
    }

    /**
     * 提取设备ID列表
     */
    private List<String> extractDeviceIds(List<DeviceGpuInfoDTO> deviceList) {
        List<String> deviceIdList = new ArrayList<>();

        deviceList.stream()
                  .filter(device -> ObjNullUtils.isNotNull(device.getDeviceId()))
                  .forEach(device -> {
                      String deviceId = device.getDeviceId();
                      if (deviceId.contains(",")) {
                          deviceIdList.addAll(Arrays.asList(deviceId.split(",")));
                      } else {
                          deviceIdList.add(deviceId);
                      }
                  });

        return deviceIdList;
    }

    /**
     * 计算平均指标值
     */
    private Map<String, Double> calculateAverages(List<DeviceCardMetricsDTO> metricsData) {
        Map<String, Double> averages = new HashMap<>();

        double avgGpuUtil = metricsData.stream()
                                       .filter(data -> data.getGpuUtilPercent() != null)
                                       .mapToDouble(DeviceCardMetricsDTO::getGpuUtilPercent)
                                       .average()
                                       .orElse(0.0);

        double avgMemUtil = metricsData.stream()
                                       .filter(data -> data.getMemUtilpercent() != null)
                                       .mapToDouble(DeviceCardMetricsDTO::getMemUtilpercent)
                                       .average()
                                       .orElse(0.0);

        averages.put("gpuUtil", avgGpuUtil);
        averages.put("memUtil", avgMemUtil);

        return averages;
    }

    /**
     * 格式化百分比（保留2位小数）
     */
    private String formatPercentage(Double value) {
        if (value == null) {
            return "0.00";
        }
        return BigDecimal.valueOf(value)
                         .setScale(2, RoundingMode.HALF_UP)
                         .toString();
    }

    /**
     * 上传文件到FTP
     */
    private void uploadToFtp(String filePath, ExportTaskDTO taskDTO) {
        try {
            FTPUtil ftpUtil = new FTPUtil(ftpConfig.getIp(), ftpConfig.getPort(),
                    ftpConfig.getUser(), ftpConfig.getPass());
            String remotePath = ftpConfig.getBasePath() + new SimpleDateFormat("yyyy-MM-dd/").format(new Date());
            File localFile = new File(filePath);
            boolean uploadResult = ftpUtil.uploadFile(remotePath, localFile);

            if (uploadResult) {
                String ftpFilePath = remotePath + localFile.getName();
                taskDTO.setFilePath(ftpFilePath);
                taskDTO.setFileName(localFile.getName());
                exportTaskManager.updateTask(taskDTO);
                log.info("GPU monthly total report file uploaded to FTP successfully: {}", ftpFilePath);
            } else {
                log.error("Failed to upload GPU monthly total report file to FTP server");
                throw new RuntimeException("Failed to upload GPU monthly total report file to FTP server");
            }
        } catch (IOException e) {
            log.error("Error uploading GPU monthly total report file to FTP: ", e);
            throw new RuntimeException("Error uploading GPU monthly total report file to FTP: " + e.getMessage());
        }
    }

    private String generateExcelFileName() {
        String timestamp = LocalDateTime.now().format(DATE_FORMATTER);
        return String.format("%s_%s.xlsx", BUSINESS_FILE_NAME, timestamp);
    }

    @Override
    public String getReportType() {
        return "GPU_MONTHLY_TOTAL";
    }
} 