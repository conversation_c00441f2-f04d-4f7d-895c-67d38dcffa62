package com.datatech.slgzt.impl.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.config.OperationalViewProperties;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.AlarmInfoDTO;
import com.datatech.slgzt.model.AuditLogDTO;
import com.datatech.slgzt.model.EventDTO;
import com.datatech.slgzt.model.config.AlarmConfig;
import com.datatech.slgzt.model.config.EventConfig;
import com.datatech.slgzt.model.config.LogConfig;
import com.datatech.slgzt.model.config.PerformanceConfig;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.opm.OperationalReportLogCreateOpm;
import com.datatech.slgzt.model.query.OperationalReportQuery;
import com.datatech.slgzt.service.OperationalReportService;
import com.datatech.slgzt.service.OperationalViewService;
import com.datatech.slgzt.utils.FileUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Array;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.HttpResult;
import com.ejlchina.okhttps.OkHttps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 运维报表Service实现类
 *
 * <AUTHOR>
 * @date 2025-12-20
 */
@Slf4j
@Service
public class OperationalReportServiceImpl implements OperationalReportService {

    @Resource
    private OperationalReportManager operationalReportManager;

    @Resource
    private OperationalReportLogManager operationalReportLogManager;

    @Resource
    private OperationalViewManager operationalViewManager;

    @Resource
    private OperationalViewService operationalViewService;

    @Resource
    private PerformanceAggregateManager performanceAggregateManager;

    @Resource
    private RegionCodeManager regionCodeManager;

    @Resource
    private OperationalViewProperties properties;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createOperationalReport(OperationalReportDTO dto) {
        dto.setCreateTime(LocalDateTime.now());
        dto.setModifyTime(LocalDateTime.now());
        dto.setNextGenerationTime(calculateNextGenerationTime(dto.getGenerationCycle(), dto.getGenerationTime()));
        return operationalReportManager.create(dto);
    }

    private LocalDateTime calculateNextGenerationTime(String generationCycle, String generationTime) {
        if (generationTime == null || generationTime.trim().isEmpty()) {
            return null;
        }

        LocalTime execTime;
        try {
            execTime = LocalTime.parse(generationTime.trim(), DateTimeFormatter.ofPattern("H:mm"));
        } catch (Exception e) {
            throw new IllegalArgumentException("生成时间格式错误，应为 'HH:mm'");
        }

        LocalDateTime now = LocalDateTime.now();

        if ("每天".equals(generationCycle)) {
            LocalDateTime c = now.with(execTime);
            return c.isBefore(now) ? c.plusDays(1) : c;
        } else if ("每周".equals(generationCycle)) {
            LocalDateTime c = now.with(DayOfWeek.MONDAY).with(execTime);
            return c.isBefore(now) ? c.plusWeeks(1) : c;
        } else if ("每月".equals(generationCycle)) {
            LocalDateTime c = now.withDayOfMonth(1).with(execTime);
            return c.isBefore(now) ? c.plusMonths(1) : c;
        } else {
            LocalDateTime c = now.with(execTime);
            return c.isBefore(now) ? c.plusDays(1) : c;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOperationalReport(OperationalReportDTO dto) {
        dto.setModifyTime(LocalDateTime.now());
        operationalReportManager.update(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOperationalReportById(Long id) {
        operationalReportManager.deleteById(id);
    }

    @Override
    public OperationalReportDTO getOperationalReportById(Long id) {
        return operationalReportManager.getById(id);
    }

    @Override
    public PageResult<OperationalReportDTO> pageOperationalReports(OperationalReportQuery query) {
        return operationalReportManager.page(query);
    }

    @Override
    public List<OperationalReportDTO> listOperationalReports(OperationalReportQuery query) {
        return operationalReportManager.list(query);
    }

    @Override
    public List<OperationalReportDTO> listOperationalReportsByUserId(Long userId) {
        return operationalReportManager.listByUserId(userId);
    }

    @Override
    public List<OperationalReportDTO> listOperationalReportsByViewId(Long operationalViewId) {
        return operationalReportManager.listByOperationalViewId(operationalViewId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableOperationalReport(Long id) {
        OperationalReportDTO dto = operationalReportManager.getById(id);
        if (dto != null) {
            dto.setEnabled(1);
            dto.setModifyTime(LocalDateTime.now());
            operationalReportManager.update(dto);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableOperationalReport(Long id) {
        OperationalReportDTO dto = operationalReportManager.getById(id);
        if (dto != null) {
            dto.setEnabled(0);
            dto.setModifyTime(LocalDateTime.now());
            operationalReportManager.update(dto);
        }
    }

    /**
     * 手动生成报表
     */
    @Override
    public void generateOperationalReportManually(OperationalReportLogCreateOpm opm) {
        if (opm == null || opm.getOperationalViewId() == null || opm.getType() == null) {
            throw new IllegalArgumentException("operationalViewId 和 type 不能为空");
        }

        Long viewId = opm.getOperationalViewId();
        // "日报"/"周报"/"月报"
        String reportType = opm.getType();

        // 1. 获取运维视图配置
        OperationalViewDTO view = operationalViewManager.getById(viewId);
        Precondition.checkArgument(view, "未找到ID为 " + viewId + " 的运维视图");

        // 2. 获取各模块配置
        PerformanceConfig performanceConfig = view.getPerformanceConfig();
        AlarmConfig alarmConfig = view.getAlarmConfig();
        LogConfig logConfig = view.getLogConfig();
        EventConfig eventConfig = view.getEventConfig();

        // 3. 查询数据
        List<AlarmInfoDTO> alarms = new ArrayList<>();
        List<AuditLogDTO> logs = new ArrayList<>();
        List<EventDTO> events = new ArrayList<>();
        List<VMResourcePerformanceDTO> performanceData = new ArrayList<>();
        if (performanceConfig != null) {
            // 导出性能数据
            if (CollectionUtils.isEmpty(performanceConfig.getRegionCodes()) && StringUtils.isNotBlank(performanceConfig.getDomainCode())) {
                List<RegionCodeDTO> list2 = regionCodeManager.list(performanceConfig.getDomainCode(), null);
                performanceConfig.setRegionCodes(list2.stream().map(RegionCodeDTO::getRegionCode).collect(Collectors.toList()));
            }
            performanceData = performanceAggregateManager.listVmPerformancePage(1, 40000, performanceConfig.getStartTime(), performanceConfig.getEndTime(), performanceConfig.getRegionCodes()).getRecords();
        }
        if (alarmConfig != null) {
            PageResult<AlarmInfoDTO> alarmPage = operationalViewService.getAlarms(toMap(alarmConfig));
            alarms = alarmPage.getRecords();
        }
        if (logConfig != null) {
            logs = getLogs(logConfig);
        }
        if (eventConfig != null) {
            events = getEvents(eventConfig);
        }

        // 4. 生成文件名（逻辑与定时任务一致）
        LocalDateTime now = LocalDateTime.now();
        String fileName;
        if ("日报".equals(reportType)) {
            fileName = "运维日报-" + DateUtil.format(now, "yyyy-MM-dd");
        } else if ("周报".equals(reportType)) {
            LocalDateTime monday = now.with(DayOfWeek.MONDAY);
            LocalDateTime sunday = monday.plusDays(6);
            fileName = "运维周报-" +
                    DateUtil.format(monday, "yyyy-MM-dd") +
                    "至" +
                    DateUtil.format(sunday, "yyyy-MM-dd");
        } else if ("月报".equals(reportType)) {
            fileName = "运维月报-" + DateUtil.format(now, "yyyy-MM");
        } else {
            fileName = "运维" + reportType + "-" + DateUtil.format(now, "yyyyMMddHHmmss");
        }

        // 允许前端传自定义名称（可选）
        if (opm.getReportName() != null && !opm.getReportName().trim().isEmpty()) {
            fileName = opm.getReportName().replaceAll("[\\\\/:*?\"<>|]", "_"); // 清理非法字符
        }

        String filePath = System.getProperty("user.dir") + "/export/" + fileName + ".xlsx";

        // 5. 构造报表日志记录
        OperationalReportLogDTO logDTO = new OperationalReportLogDTO();
        logDTO.setUserId(view.getUserId());
        logDTO.setName(fileName);
        logDTO.setOperationalViewId(viewId);
        logDTO.setOperationalViewName(view.getName());
        logDTO.setOperationalReportType(reportType);
        logDTO.setCreateTime(LocalDateTime.now());
        logDTO.setModifyTime(LocalDateTime.now());
        logDTO.setResult("生成中");
        logDTO.setReportUrl(filePath);
        operationalReportLogManager.create(logDTO);

        try {
            // 6. 准备 Sheet 配置
            List<FileUtils.SheetConfig<?>> sheetConfigs = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(alarms)) {
                sheetConfigs.add(new FileUtils.SheetConfig<>("告警数据", alarms, AlarmInfoDTO.class));
            }
            if (CollectionUtil.isNotEmpty(logs)) {
                sheetConfigs.add(new FileUtils.SheetConfig<>("日志数据", logs, AuditLogDTO.class));
            }
            if (CollectionUtil.isNotEmpty(events)) {
                sheetConfigs.add(new FileUtils.SheetConfig<>("事件数据", events, EventDTO.class));
            }
            if (CollectionUtil.isNotEmpty(performanceData)) {
                sheetConfigs.add(new FileUtils.SheetConfig<>("性能数据", performanceData, VMResourcePerformanceDTO.class));
            }

            // 7. 执行导出
            if (!sheetConfigs.isEmpty()) {
                FileUtils.doMultiSheetExport(filePath, sheetConfigs);
                logDTO.setResult("已完成");
            } else {
                log.warn("无数据可导出，生成空报表: {}", filePath);
            }

        } catch (Exception e) {
            log.error("主动生成报表失败，viewId={}, type={}", viewId, reportType, e);
            logDTO.setResult("生成异常");
            logDTO.setMessage(e.getMessage() != null ? e.getMessage().substring(0, Math.min(500, e.getMessage().length())) : "未知错误");
        } finally {
            operationalReportLogManager.update(logDTO);
        }
    }

    // 定时任务查询所有启用的报表策略，判断下次执行是否在定时任务执行的区间之内，如果在的话则根据策略绑定的报表参数，查询相关报表生成文件，一种类型的报表为一个sheet，再在报表日志表中插入一条报表生成记录

    /**
     * 定时生成运维报表（每5分钟检查一次）
     */
    //@Transactional(rollbackFor = Exception.class)
    @Scheduled(cron = "0 */3 * * * ?")
    @Override
    public void autoGenerateOperationalReport() {
        log.info("定时生成运维报表开始");
        OperationalReportQuery operationalReportQuery = new OperationalReportQuery();
        operationalReportQuery.setEnabled(1);
        List<OperationalReportDTO> list = operationalReportManager.list(operationalReportQuery);
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        for (OperationalReportDTO operationalReportDTO : list) {
            LocalDateTime now = LocalDateTime.now();
            now = now.withSecond(0).withNano(0);
            log.info("视图策略id：{}，预计执行时间：{}，当前时间：{}", operationalReportDTO.getId(), operationalReportDTO.getNextGenerationTime(), now);
            // 跳过未到执行时间的任务
            // 3分钟一次，正负1分钟都应该执行
            // 比如00：03分执行，那么下次执行之间是00：02-00：04都执行；下一次执行时间是00：06，那么00：02-00：04肯定不在其中
            if (operationalReportDTO.getNextGenerationTime() == null
                    || operationalReportDTO.getNextGenerationTime().isAfter(now.plusMinutes(1))
                    || operationalReportDTO.getNextGenerationTime().isBefore(now.minusMinutes(1))) {
                continue;
            }
            OperationalViewDTO view = operationalViewManager.getById(operationalReportDTO.getOperationalViewId());
            if (view == null) {
                continue;
            }
            OperationalViewDTO operationalViewDTO = operationalViewManager.getById(operationalReportDTO.getOperationalViewId());
            PerformanceConfig performanceConfig = operationalViewDTO.getPerformanceConfig();
            AlarmConfig alarmConfig = operationalViewDTO.getAlarmConfig();
            LogConfig logConfig = operationalViewDTO.getLogConfig();
            EventConfig eventConfig = operationalViewDTO.getEventConfig();

            List<AlarmInfoDTO> alarms = new ArrayList<>();
            List<AuditLogDTO> logs = new ArrayList<>();
            List<EventDTO> events = new ArrayList<>();
            List<VMResourcePerformanceDTO> performanceData = new ArrayList<>();
            if (performanceConfig != null) {
                // 导出性能数据
                if (CollectionUtils.isEmpty(performanceConfig.getRegionCodes()) && StringUtils.isNotBlank(performanceConfig.getDomainCode())) {
                    List<RegionCodeDTO> list2 = regionCodeManager.list(performanceConfig.getDomainCode(), null);
                    performanceConfig.setRegionCodes(list2.stream().map(RegionCodeDTO::getRegionCode).collect(Collectors.toList()));
                }
                performanceData = performanceAggregateManager.listVmPerformancePage(1, 40000, performanceConfig.getStartTime(), performanceConfig.getEndTime(), performanceConfig.getRegionCodes()).getRecords();
            }
            if (alarmConfig != null) {
                // 导出报警数据
                PageResult<AlarmInfoDTO> pageResult = operationalViewService.getAlarms(toMap(alarmConfig));
                alarms = pageResult.getRecords();
            }
            if (logConfig != null) {
                // 导出日志数据
                logs = getLogs(logConfig);
            }
            if (eventConfig != null) {
                // 导出事件数据
                events = getEvents(eventConfig);
            }
            String fileName = getFileName(operationalReportDTO.getType());
            String filePath = System.getProperty("user.dir") + "/export/" + fileName + ".xlsx";
            // 构造一条报表生成记录
            OperationalReportLogDTO logDTO = new OperationalReportLogDTO();
            logDTO.setUserId(operationalViewDTO.getUserId());
            logDTO.setName(fileName);
            logDTO.setOperationalViewId(operationalReportDTO.getOperationalViewId());
            logDTO.setOperationalViewName(operationalReportDTO.getOperationalViewName());
            logDTO.setOperationalReportId(operationalReportDTO.getId());
            logDTO.setOperationalReportType(operationalReportDTO.getType());
            logDTO.setCreateTime(LocalDateTime.now());
            logDTO.setModifyTime(LocalDateTime.now());
            logDTO.setResult("生成中");
            logDTO.setReportUrl(filePath);
            operationalReportLogManager.create(logDTO);
            // 准备 Sheet 配置
            List<FileUtils.SheetConfig<?>> sheetConfigs = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(alarms)) {
                sheetConfigs.add(new FileUtils.SheetConfig<>("告警数据", alarms, AlarmInfoDTO.class));
            }
            if (CollectionUtil.isNotEmpty(logs)) {
                sheetConfigs.add(new FileUtils.SheetConfig<>("日志数据", logs, AuditLogDTO.class));
            }
            if (CollectionUtil.isNotEmpty(events)) {
                sheetConfigs.add(new FileUtils.SheetConfig<>("事件数据", events, EventDTO.class));
            }
            if (CollectionUtil.isNotEmpty(performanceData)) {
                sheetConfigs.add(new FileUtils.SheetConfig<>("性能数据", performanceData, VMResourcePerformanceDTO.class));
            }

            // 执行多 Sheet 导出
            try {
                if (!sheetConfigs.isEmpty()) {
                    FileUtils.doMultiSheetExport(filePath, sheetConfigs);
                } else {
                    // 可选：创建空文件或跳过
                    log.warn("无数据可导出，跳过文件生成: {}", filePath);
                }
            } catch (Exception e) {
                logDTO.setResult("生成异常");
                logDTO.setMessage(e.getMessage());
                operationalReportLogManager.update(logDTO);
            }
            logDTO.setResult("已完成");
            operationalReportLogManager.update(logDTO);
            // 更新下一次执行时间
            operationalReportDTO.setNextGenerationTime(calculateNextGenerationTime(operationalReportDTO.getGenerationCycle(), operationalReportDTO.getGenerationTime()));
            operationalReportManager.update(operationalReportDTO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteOperationalReports(List<Long> ids) {
        for (Long id : ids) {
            operationalReportManager.deleteById(id);
        }
    }

    @Override
    public String getTaTaiToken() {
        String curTimestamp = String.valueOf(System.currentTimeMillis());
        String sign = getMD5(curTimestamp + properties.getEventAppId() + properties.getEventAppSecret()).toUpperCase();
        Map<String, String> loginMap = new HashMap<>();
        loginMap.put("account", properties.getTaTaiAccount());
        loginMap.put("password", properties.getTaTaiPassword());

        Mapper dataMapper = OkHttps.sync(properties.getEventUrl() + "/cloud/open/windows/apis/system/user/loginInfo")
                .bodyType(OkHttps.JSON)
                .addHeader("timeStamp", curTimestamp)
                .addHeader("appId", properties.getEventAppId())
                .addHeader("appSecret", properties.getEventAppSecret())
                .addHeader("sign", sign)
                .setBodyPara(JSON.toJSONString(loginMap))
                .post()
                .getBody()
                .toMapper();
        String success = dataMapper.getString("success");
        Precondition.checkArgument("true".equals(success), "请求失败, " + dataMapper.getString("msg"));
        return dataMapper.getString("data");
    }

    @Override
    public String getTaTaiUrlWithToken() {
        //http://***************/#/?token=28f8e95e47f1db970e876ba82fd9edaa&local_login=false
        return properties.getTaTaiUrl() + "/#/?token=" + getTaTaiToken() + "&local_login=false";
    }

    @SneakyThrows
    private String convertToIso8601(String input) {
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = inputFormat.parse(input);
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        return outputFormat.format(date);
    }

    private List<AuditLogDTO> getLogs(LogConfig config) {
        // 结束时间默认为当前时间
        String endTime = config.getEndTime();
        if (endTime == null || endTime.trim().isEmpty()) {
            endTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        }

        String techStackZh = config.getResourceType();
        String startTime = config.getStartTime();
        String logContent = config.getLogContent();
        String deviceName = config.getDeviceName();
        // 技术栈映射
        String techStack = getTechStackEn(techStackZh);
        String vendorEn = getVendorEn(techStackZh);
        String indexPattern = "auditlog_" + techStack + "_" + vendorEn + "_*";

        // 时间格式转换
        String startIso = convertToIso8601(startTime);
        String endIso = convertToIso8601(endTime);

        // 构造 bool 查询
        List<String> mustClauses = new ArrayList<>();
        mustClauses.add("{ \"match\": { \"tech_stack\": \"" + techStackZh + "\" } }");
        mustClauses.add("{ \"range\": { \"operation_time\": { \"gte\": \"" + startIso + "\", \"lte\": \"" + endIso + "\" } } }");

        if (StringUtils.isNotBlank(deviceName)) {
            mustClauses.add("{ \"match_phrase\": { \"device_name\": \"" + deviceName + "\" } }");
        }

        if (StringUtils.isNotBlank(logContent)) {
            mustClauses.add("{ \"match_phrase\": { \"operation_log\": \"" + logContent + "\" } }");
        }

        String queryJson = "{\n" +
                "  \"track_total_hits\": true,\n" +
                "  \"query\": {\n" +
                "    \"bool\": {\n" +
                "      \"must\": [\n" +
                String.join(",\n", mustClauses) + "\n" +
                "      ]\n" +
                "    }\n" +
                "  },\n" +
                "  \"from\": 0,\n" +
                "  \"size\": 40000,\n" +
                "  \"sort\": [\n" +
                "    { \"operation_time\": { \"order\": \"desc\" } }\n" +
                "  ]\n" +
                "}";

        // 构造完整 URL
        String url = properties.getLogUrl() + indexPattern + "/_search";

        // 添加 Basic Auth（OkHttps 支持 header 设置）
        HttpResult result = OkHttps.sync(url)
                .addHeader("Authorization", "Basic " + Base64.getEncoder().encodeToString(
                        (properties.getLogUsername() + ":" + properties.getLogPassword()).getBytes(StandardCharsets.UTF_8)))
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.parseObject(queryJson))
                .post();


        // 检查状态码
        if (result.getStatus() >= 400) {
            String errorMsg = result.getBody().toString();
            throw new RuntimeException("日志中心返回错误 [HTTP " + result.getStatus() + "]: " + errorMsg);
        }

        // 解析字符串
        List<AuditLogDTO> logs = new ArrayList<>();

        JSONObject root = JSON.parseObject(result.getBody().toString());
        JSONObject hitsObj = root.getJSONObject("hits");

        if (hitsObj != null) {
            JSONArray hits = hitsObj.getJSONArray("hits");
            if (hits != null) {
                for (int i = 0; i < hits.size(); i++) {
                    JSONObject hit = hits.getJSONObject(i);
                    JSONObject source = hit.getJSONObject("_source");
                    AuditLogDTO log = new AuditLogDTO();
                    log.setOperationLog(source.getString("operation_log"));
                    log.setOperationTime(source.getString("operation_time"));
                    log.setDeviceType(source.getString("tech_stack"));
                    log.setDeviceName(source.getString("device_name"));
                    log.setCloudPlatform(source.getString("cloud"));
                    log.setResourcePool(source.getString("resource_pool"));
                    log.setLogVendor(source.getString("log_vendor"));
                    logs.add(log);
                }
            }
        }
        return logs;
    }

    List<EventDTO> getEvents(EventConfig config) {
        String curTimestamp = String.valueOf(System.currentTimeMillis());
        String sign = getMD5(curTimestamp + properties.getEventAppId() + properties.getEventAppSecret()).toUpperCase();

        Mapper dataMapper = OkHttps.sync(properties.getEventUrl() + "/cloud/open/event/api/getEventList")
                .bodyType(OkHttps.JSON)
                .addHeader("timeStamp", curTimestamp)
                .addHeader("appId", properties.getEventAppId())
                .addHeader("appSecret", properties.getEventAppSecret())
                .addHeader("sign", sign)
                .setBodyPara(JSON.toJSONString(config))
                .post()
                .getBody()
                .toMapper();
        String success = dataMapper.getString("success");
        Precondition.checkArgument("true".equals(success), "请求失败, " + dataMapper.getString("msg"));
        Array data = dataMapper.getArray("data");
        JSONArray dataArray = JSON.parseArray(data.toString());
        List<EventDTO> eventDTOS = JSONObject.parseArray(JSONObject.toJSONString(dataArray), EventDTO.class);
        for (EventDTO eventDTO : eventDTOS) {
            eventDTO.setEventFromSourceDesc(getEventFromSourceDesc(eventDTO.getEventFromSource()));
            eventDTO.setLastTakeDesc(formatDuration(eventDTO.getLastTake()));
        }
        return eventDTOS;
    }

    private String getTechStackEn(String zh) {
        switch (zh) {
            case "云主机":
                return "virtualmachine";
            case "云电脑":
                return "cloudcompute";
            case "存储":
                return "storage";
            case "网络":
                return "network";
            case "云OS":
                return "cloudos";
            case "容器":
                return "docker";
            case "数据库":
                return "database";
            case "中间件":
                return "paas";
            case "物理机":
                return "physicalmachine";
            case "云管平台":
                return "cloudmanager";
            default:
                throw new IllegalArgumentException("不支持的资源类型: " + zh);
        }
    }

    private String getVendorEn(String zh) {
        switch (zh) {
            case "云主机":
            case "物理机":
            case "云OS":
            case "存储":
                return "h3c";
            case "云电脑":
            case "云管平台":
            case "中间件":
            case "数据库":
            case "容器":
                return "snc";
            case "网络":
                return "datatech";
            default:
                return "unknown";
        }
    }

    private Map<String, Object> toMap(AlarmConfig config) {
        if (config == null) {
            return Collections.emptyMap();
        }
        Map<String, Object> map = new HashMap<>();
        if (config.getAppId() != null) {
            map.put("appId", config.getAppId());
        }
        if (config.getNetworkType() != null) {
            map.put("networkType", config.getNetworkType());
        }
        if (config.getAlarmSeverity() != null) {
            map.put("alarmSeverity", config.getAlarmSeverity());
        }
        if (config.getStartTime() != null) {
            map.put("startTime", config.getStartTime());
        }
        if (config.getEndTime() != null) {
            map.put("endTime", config.getEndTime());
        }
        map.put("pageNum", 1);
        map.put("pageSize", 40000);
        return map;
    }

    private String getMD5(String str) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
            return byte2Hex(messageDigest.digest());
        } catch (Exception e) {
            log.error("认证计算签名失败, {}", ExceptionUtils.getStackTrace(e));
            throw new RuntimeException("认证计算签名失败" + e.getMessage());
        }
    }

    /**
     * 将byte转为16进制
     */
    private static String byte2Hex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        String temp;
        for (byte aByte : bytes) {
            temp = Integer.toHexString(aByte & 0xFF);
            if (temp.length() == 1) {
                // 1得到一位的进行补0操作
                sb.append("0");
            }
            sb.append(temp);
        }
        return sb.toString();
    }

    private String getFileName(String type) {
        LocalDateTime nowForName = LocalDateTime.now();
        String fileName;

        if ("日报".equals(type)) {
            fileName = "运维日报-" + DateUtil.format(nowForName, DatePattern.NORM_DATE_PATTERN);
        } else if ("周报".equals(type)) {
            // 获取本周一 00:00:00
            LocalDateTime monday = nowForName.with(DayOfWeek.MONDAY).withHour(0).withMinute(0).withSecond(0).withNano(0);
            // 本周日 23:59:59（或直接取周日日期）
            LocalDateTime sunday = monday.plusDays(6);

            String mondayStr = DateUtil.format(monday, "yyyy-MM-dd");
            String sundayStr = DateUtil.format(sunday, "yyyy-MM-dd");
            fileName = "运维周报-" + mondayStr + "至" + sundayStr;
        } else if ("月报".equals(type)) {
            fileName = "运维月报-" + DateUtil.format(nowForName, "yyyy-MM");
        } else {
            fileName = "运维" + type + "-" + DateUtil.format(nowForName, DatePattern.PURE_DATETIME_PATTERN);
        }
        return fileName;
    }

    private String getEventFromSourceDesc(Integer eventFromSource) {
        switch (eventFromSource) {
            case 0:
                return "告警";
            case 2:
                return "业务报障";
            default:
                return "";
        }
    }

    public String formatDuration(long millis) {
        if (millis < 0) {
            return "-" + formatDuration(-millis);
        }
        if (millis == 0) {
            return "0毫秒";
        }

        Duration duration = Duration.ofMillis(millis);
        long days = duration.toDays();
        duration = duration.minusDays(days);
        long hours = duration.toHours();
        duration = duration.minusHours(hours);
        long minutes = duration.toMinutes();
        duration = duration.minusMinutes(minutes);
        long seconds = duration.getSeconds();
        long remainingMillis = duration.toMillis() % 1000;

        StringBuilder sb = new StringBuilder();
        if (days > 0) sb.append(days).append("天");
        if (hours > 0) sb.append(hours).append("小时");
        if (minutes > 0) sb.append(minutes).append("分钟");
        if (seconds > 0) sb.append(seconds).append("秒");
        if (remainingMillis > 0 || sb.length() == 0) { // 如果前面都是0，则至少显示毫秒
            sb.append(remainingMillis).append("毫秒");
        }

        return sb.toString();
    }
}
