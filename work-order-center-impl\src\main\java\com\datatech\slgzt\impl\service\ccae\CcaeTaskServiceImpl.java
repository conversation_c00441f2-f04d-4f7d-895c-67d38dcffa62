package com.datatech.slgzt.impl.service.ccae;

import com.datatech.slgzt.manager.CcaeTaskManager;
import com.datatech.slgzt.model.dto.CcaeTaskDTO;
import com.datatech.slgzt.model.query.CcaeTaskQuery;
import com.datatech.slgzt.service.ccae.CcaeTaskService;
import com.datatech.slgzt.utils.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * CCAE任务Service实现类
 * 
 * <AUTHOR>
 * @date 2025-12-30
 */
@Slf4j
@Service
public class CcaeTaskServiceImpl implements CcaeTaskService {

    @Resource
    private CcaeTaskManager ccaeTaskManager;

    @Override
    public CcaeTaskDTO getById(Long id) {
        return ccaeTaskManager.getById(id);
    }

    @Override
    public CcaeTaskDTO getByCcaeId(String ccaeId) {
        return ccaeTaskManager.getByCcaeId(ccaeId);
    }

    @Override
    public void createTask(CcaeTaskDTO ccaeTaskDTO) {
        log.info("创建CCAE任务，ccaeId: {}", ccaeTaskDTO.getCcaeId());
        
        // 设置默认值
        if (ccaeTaskDTO.getProgress() == null) {
            ccaeTaskDTO.setProgress(0);
        }
        if (ccaeTaskDTO.getPassItemCount() == null) {
            ccaeTaskDTO.setPassItemCount(0);
        }
        if (ccaeTaskDTO.getCheckItemCount() == null) {
            ccaeTaskDTO.setCheckItemCount(0);
        }
        if (ccaeTaskDTO.getElapsedTimeSec() == null) {
            ccaeTaskDTO.setElapsedTimeSec(0);
        }
        if (ccaeTaskDTO.getCcaeCreateTime() == null) {
            ccaeTaskDTO.setCcaeCreateTime(LocalDateTime.now());
        }
        // 设置默认状态为未删除
        if (ccaeTaskDTO.getStatus() == null) {
            ccaeTaskDTO.setStatus(1);
        }

        ccaeTaskManager.save(ccaeTaskDTO);
        log.info("CCAE任务创建成功，ccaeId: {}", ccaeTaskDTO.getCcaeId());
    }

    @Override
    public void updateTask(CcaeTaskDTO ccaeTaskDTO) {
        log.info("更新CCAE任务，ccaeId: {}", ccaeTaskDTO.getCcaeId());
        ccaeTaskManager.updateById(ccaeTaskDTO);
        log.info("CCAE任务更新成功，ccaeId: {}", ccaeTaskDTO.getCcaeId());
    }

    @Override
    public void deleteTask(Long id) {
        log.info("删除CCAE任务，id: {}", id);
        ccaeTaskManager.deleteById(id);
        log.info("CCAE任务删除成功，id: {}", id);
    }

    @Override
    public List<CcaeTaskDTO> listTasks(CcaeTaskQuery query) {
        return ccaeTaskManager.list(query);
    }

    @Override
    public PageResult<CcaeTaskDTO> pageTasks(CcaeTaskQuery query) {
        return ccaeTaskManager.page(query);
    }

    @Override
    public void startTask(String ccaeId) {
        log.info("启动CCAE任务，ccaeId: {}", ccaeId);
        ccaeTaskManager.updateProgressByCcaeId(ccaeId, 0);
        log.info("CCAE任务启动成功，ccaeId: {}", ccaeId);
    }

    @Override
    public void stopTask(String ccaeId) {
        log.info("停止CCAE任务，ccaeId: {}", ccaeId);
        CcaeTaskDTO existingTask = ccaeTaskManager.getByCcaeId(ccaeId);
        if (existingTask != null) {
            existingTask.setProgress(-1);
            existingTask.setStopTime(LocalDateTime.now());
            ccaeTaskManager.updateById(existingTask);
            log.info("CCAE任务停止成功，ccaeId: {}", ccaeId);
        } else {
            log.warn("CCAE任务不存在，无法停止，ccaeId: {}", ccaeId);
        }
    }

    @Override
    public void updateTaskProgress(String ccaeId, Integer progress) {
        log.debug("更新CCAE任务进度，ccaeId: {}, progress: {}", ccaeId, progress);
        ccaeTaskManager.updateProgressByCcaeId(ccaeId, progress);
    }

    @Override
    public void completeTask(String ccaeId, Integer passItemCount, Integer checkItemCount, Integer elapsedTimeSec) {
        log.info("完成CCAE任务，ccaeId: {}, passItemCount: {}, checkItemCount: {}, elapsedTimeSec: {}", 
                ccaeId, passItemCount, checkItemCount, elapsedTimeSec);
        ccaeTaskManager.updateTaskStatusByCcaeId(ccaeId, 100, passItemCount, checkItemCount, elapsedTimeSec);
        log.info("CCAE任务完成，ccaeId: {}", ccaeId);
    }

    @Override
    public CcaeTaskDTO getTaskStatistics(String ccaeId) {
        return ccaeTaskManager.getByCcaeId(ccaeId);
    }
}
