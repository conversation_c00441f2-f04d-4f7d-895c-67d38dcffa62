package com.datatech.slgzt.impl.service.nostander;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.*;
import com.datatech.slgzt.enums.bpmn.ActivityEnum;
import com.datatech.slgzt.enums.bpmn.RecoveryOrderNodeEnum;
import com.datatech.slgzt.exception.UniversalException;
import com.datatech.slgzt.handle.ResourceHandle;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.NonStanderWorkOrderManager;
import com.datatech.slgzt.manager.NonStanderWorkOrderProductManager;
import com.datatech.slgzt.manager.WorkOrderAuthLogManager;
import com.datatech.slgzt.model.BaseProductModel;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.baseconfig.OacConfig;
import com.datatech.slgzt.model.bpmn.ActivityTaskTreeVo;
import com.datatech.slgzt.model.bpmn.ActivityTaskVo;
import com.datatech.slgzt.model.bpmn.TaskNodeDTO;
import com.datatech.slgzt.model.bpmn.TaskTreeNodeDTO;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.dto.NonStanderWorkOrderDTO;
import com.datatech.slgzt.model.dto.NonStanderWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.OrderCommonDTO;
import com.datatech.slgzt.model.dto.WorkOrderAuthLogDTO;
import com.datatech.slgzt.model.dto.business.BusinessService;
import com.datatech.slgzt.model.dto.nonstandard.NonStanderAuditWorkOrderDTO;
import com.datatech.slgzt.model.dto.tenant.CmpTenantDTO;
import com.datatech.slgzt.model.home.AuditCountVo;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.opm.FlavorCreateOpm;
import com.datatech.slgzt.model.query.NonStanderWorkOrderProductQuery;
import com.datatech.slgzt.model.query.NonStanderWorkOrderQuery;
import com.datatech.slgzt.model.query.WorkOrderAuthLogQuery;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.service.UserService;
import com.datatech.slgzt.service.bpmn.BaseActivity;
import com.datatech.slgzt.service.cmdb.CmdbReportService;
import com.datatech.slgzt.service.config.ConfigService;
import com.datatech.slgzt.service.nostander.NonStanderWorkOrderService;
import com.datatech.slgzt.service.tenant.CmpTenantService;
import com.datatech.slgzt.service.yunshu.YunshuReportService;
import com.datatech.slgzt.utils.*;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 非标准工单服务实现类
 * @date 2025年 03月03日 18:54:53
 */
@Service
@Slf4j
public class NonStanderWorkOrderServiceImpl implements NonStanderWorkOrderService {


    @Resource
    private NonStanderWorkOrderManager nonStanderWorkOrderManager;

    @Resource
    private NonStanderWorkOrderProductManager productManager;

    @Resource
    private CmdbReportService cmdbReportService;
    @Resource
    private YunshuReportService yunshuReportService;

    @Resource
    private ConfigService configService;


    @Resource
    private BaseActivity baseActivity;

    @Resource
    private WorkOrderAuthLogManager workOrderAuthLogManager;

    @Resource
    private UserHelper userHelper;

    @Resource
    private CmpTenantService cmpTenantService;

    @Resource
    private BusinessService businessService;


    @Resource
    private UserService userService;

    @Resource
    private ResourceHandle resourceHandle;

    /**
     * 返回工单公共信息
     *
     * @param orderId
     * @return
     */
    @Override
    public OrderCommonDTO getOrderCommon(String orderId) {
        // 查询工单信息
        NonStanderWorkOrderDTO orderDTO = nonStanderWorkOrderManager.getById(orderId);
        OrderCommonDTO orderCommonDTO = new OrderCommonDTO();
        orderCommonDTO.setBillId(orderDTO.getBillId());
        orderCommonDTO.setTenantId(orderDTO.getTenantId());
        orderCommonDTO.setUserId(orderDTO.getCreatedBy());
        orderCommonDTO.setBusinessSysId(orderDTO.getBusinessSystemId());
        orderCommonDTO.setBusinessSysName(orderDTO.getBusinessSystemName());
        orderCommonDTO.setModuleId(orderDTO.getModuleId());
        orderCommonDTO.setModuleName(orderDTO.getModuleName());
        orderCommonDTO.setUserName(orderDTO.getCreatedUserName());
        orderCommonDTO.setOrderCode(orderDTO.getOrderCode());
        orderCommonDTO.setDomainCode(orderDTO.getDomainCode());
        orderCommonDTO.setDomainName(orderDTO.getDomainName());
        orderCommonDTO.setCatalogueDomainCode(orderDTO.getCatalogueDomainCode());
        orderCommonDTO.setCatalogueDomainName(orderDTO.getCatalogueDomainName());
        orderCommonDTO.setSourceType(SourceTypeEnum.NON_STANDARD.getPrefix());
        // 查询工单产品信息
        List<NonStanderWorkOrderProductDTO> list = productManager.list(new NonStanderWorkOrderProductQuery().setOrderId(orderId));
        List<String> regionCodeList = Lists.newArrayList();
        if (ObjNullUtils.isNotNull(list)) {
            regionCodeList = list.stream().map(NonStanderWorkOrderProductDTO::getPropertySnapshot)
                    .map(i -> JSON.parseObject(i, BaseProductModel.class))
                    .map(BaseProductModel::getRegionCode).filter(ObjNullUtils::isNotNull)
                    .distinct().collect(Collectors.toList());
        }
        orderCommonDTO.setRegionCodeList(StreamUtils.distinct(regionCodeList));
        return orderCommonDTO;
    }

    /**
     * 判断工单是否已经完成
     *
     * @param orderId
     */
    @Override
    public boolean isOrderComplete(String orderId) {
        //查询工单信息
        NonStanderWorkOrderDTO orderDTO = nonStanderWorkOrderManager.getById(orderId);
        Precondition.checkArgument(ObjNullUtils.isNotNull(orderDTO), "工单不存在");
        String orderStatus = orderDTO.getOrderStatus();
        return OrderStatusEnum.CLOSE.getCode().equals(orderStatus) || OrderStatusEnum.END.getCode().equals(orderStatus);
    }

    @Override
    public TaskTreeNodeDTO selectTreeNodeByKey(){
        TaskTreeNodeDTO taskTreeNodeDTO = baseActivity.taskNodesTreeByKey(ActivityEnum.ActivityProcessEnum.NON_STANDARD_PROCESS);
        setTaskDisplayName(taskTreeNodeDTO, null);
        return taskTreeNodeDTO;
    }

    /**
     * 注册
     */
    @Override
    public OrderTypeEnum register() {
        return OrderTypeEnum.NON_STANDARD;
    }

    /**
     * 创建非标订工单并且开始流程
     *
     * @param dto
     */
    @Override
    public void createNonStandardOrderAndStartProcess(NonStanderWorkOrderDTO dto, String requestPath, UserCenterUserDTO userDTO) {

        // 启动工作流
        dto.setCreatedBy(userDTO.getId());
        String activityId = "";
        String auditResult = "";
        if (MethodPathEnum.RESOURCE_RESUBMIT_OPEN.getPath().equalsIgnoreCase(requestPath)) {
            auditResult = OrderLogStatusEnum.RESUBMIT.getCode();
            NonStanderWorkOrderDTO orderDTO = nonStanderWorkOrderManager.getById(dto.getId());
            activityId = orderDTO.getActivitiId();
            // 填写工单编号，发短信时候需要
            dto.setOrderCode(orderDTO.getOrderCode());
            dto.setProductApplyFile(orderDTO.getProductApplyFile());
        } else {
            auditResult = OrderLogStatusEnum.CREATE.getCode();
            activityId = baseActivity.instanceRun(dto, ActivityEnum.ActivityProcessEnum.NON_STANDARD_PROCESS, AuthorityCodeEnum.USER_TASK.code(), null);
            if (StringUtils.isEmpty(activityId)) {
                throw UniversalException.build(GlobalExceptionEnum.ACTIVITY_DEFINITION_IS_NOT_FOUND);
            }
        }

        LocalDateTime createTime = LocalDateTime.now();
        workOrderAuthLogManager.createWorkOrderAuthLog(new WorkOrderAuthLogDTO()
                .setCreateTime(createTime)
                .setModifyTime(createTime)
                .setWorkOrderId(dto.getId())
                .setProcessInstanceId(activityId)
                .setAdvice(ActivitiStatusEnum.USER_TASK.getNodeRemark())
                .setAuditNodeCode(ActivitiStatusEnum.USER_TASK.getNode())
                .setAuditNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.USER_TASK.getNodeRemark())
                .setUserId(dto.getCreatedBy())
                .setUserName(userDTO.getUserName())
                .setUserPhone(userDTO.getPhone())
                .setUserEmail(userDTO.getUserEmail())
                .setAuditResult(auditResult));
        dto.setActivitiId(activityId);

        standardOrderFill(dto, requestPath, activityId, null, createTime);
        // 流转工作流
        if (!MethodPathEnum.DRAFTS_ORDER_OPEN.getPath().equals(requestPath)) {
            // 添加草稿箱的时候不需要将流程执行到下个节点
            baseActivity.complete(dto, ActivityEnum.ActivityStatusEnum.PASS, activityId, null, ActivityEnum.ActivityProcessEnum.NON_STANDARD_PROCESS, userDTO.getId(), null, null);
        }
        ActivityTaskTreeVo taskVo = baseActivity.taskNodesTree(activityId, ActivityEnum.ActivityProcessEnum.NON_STANDARD_PROCESS);
        dto.setCurrentNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(taskVo.getCurrentTaskBpmnName(), RecoveryOrderNodeEnum.EXAMINING.getCode()));
        dto.setCurrentNodeCode(taskVo.getCurrentTaskBpmnName());
        updateOrderStatus(dto, userDTO.getId(), OrderStatusEnum.EXAMINING.getCode());
    }

    @Override
    public ActivityTaskTreeVo getTaskNodesTree(String orderId) {
        NonStanderWorkOrderDTO orderDTO = nonStanderWorkOrderManager.getById(orderId);
        Precondition.checkArgument(orderDTO, "工单不存在");
        ActivityTaskTreeVo taskNode = baseActivity.taskNodesTree(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.NON_STANDARD_PROCESS);
        if (taskNode != null) {
            String remark = com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(taskNode.getCurrentTaskBpmnName(), null);
            taskNode.setCurrentTaskDisplayName(remark);
            setTaskDisplayName(taskNode.getRoot(), null);

            // 如果是end, 特殊处理，前端的key需要唯一才能确定点亮哪些节点
            if (taskNode.getCurrentTaskBpmnName() != null && taskNode.getCurrentTaskBpmnName().equals(ActivitiStatusEnum.AUTODIT_END.getNode())) {
                Object cost = baseActivity.getVariable(orderDTO.getActivitiId(), "cost");
                Object openWay = baseActivity.getVariable(orderDTO.getActivitiId(), "openWay");
                if (cost != null && openWay != null) {
                    if (cost.equals(2)) {
                        // cost hight
                        if (openWay.equals(0)) {
                            // 线下
                            taskNode.setCurrentTaskBpmnName(ActivitiStatusEnum.INFORMATION_ARCHIVE_H.getNode() + ":" + taskNode.getCurrentTaskBpmnName());
                        } else if (openWay.equals(1)) {
                            // 线上
                            taskNode.setCurrentTaskBpmnName(ActivitiStatusEnum.RESOURCE_CREATION_H.getNode() + ":" + taskNode.getCurrentTaskBpmnName());
                        }
                    } else if (cost.equals(1)) {
                        // cost low
                        if (openWay.equals(0)) {
                            // 线下
                            taskNode.setCurrentTaskBpmnName(ActivitiStatusEnum.INFORMATION_ARCHIVE_L.getNode() + ":" + taskNode.getCurrentTaskBpmnName());
                        } else if (openWay.equals(1)) {
                            // 线上
                            taskNode.setCurrentTaskBpmnName(ActivitiStatusEnum.RESOURCE_CREATION_L.getNode() + ":" + taskNode.getCurrentTaskBpmnName());
                        }
                    }
                }
            }
        }
        return taskNode;
    }

    private void setTaskDisplayName(TaskTreeNodeDTO current, TaskTreeNodeDTO parent) {
        current.setTaskDisplayName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(current.getTaskBpmnName(), null));
        if (current.getTaskBpmnName().equals(ActivitiStatusEnum.AUTODIT_END.getNode())) {
            current.setTaskBpmnName(parent.getTaskBpmnName() + ":" + current.getTaskBpmnName());
        }
        if (!CollectionUtils.isEmpty(current.getChildren())) {
            for (TaskTreeNodeDTO child : current.getChildren()) {
                setTaskDisplayName(child, current);
            }
        }
    }

    @Override
    public void getNextTaskNodes(String orderId, List<WorkOrderAuthLogDTO> authLogDTOS, String currentTaskName) {
        NonStanderWorkOrderDTO orderDTO = nonStanderWorkOrderManager.getById(orderId);
        Precondition.checkArgument(orderDTO, "工单不存在");
        TaskNodeDTO nextTaskNode = baseActivity.nextTaskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.NON_STANDARD_PROCESS);
        if (nextTaskNode == null) {
            return;
        }
        String assignee = nextTaskNode.getAssignee();
        if(assignee == null){
            return;
        }
        String name = "";
        String email = "";
        List<UserCenterUserDTO> approveUsers = new ArrayList<>();
        if (assignee.matches("[-+]?\\d+(\\.\\d+)?")) {
            UserCenterUserDTO userCenterUserDTO = userService.getUserById(Long.valueOf(assignee));
            if (userCenterUserDTO != null) {
                name = userCenterUserDTO.getUserName();
                email = userCenterUserDTO.getUserEmail();
                approveUsers.add(userCenterUserDTO);
            }
        } else {
            List<UserCenterUserDTO> list = userService.getUserListByRoleCode(assignee);
            if (list != null && !list.isEmpty()) {
                name = String.join(",", list.stream().map(UserCenterUserDTO::getUserName).collect(Collectors.toList()));
                email = String.join(",", list.stream().map(UserCenterUserDTO::getUserEmail).collect(Collectors.toList()));
                approveUsers = list;
            }
        }
        WorkOrderAuthLogDTO workOrderAuthLogDTO = new WorkOrderAuthLogDTO();
        workOrderAuthLogDTO.setWorkOrderId(orderId);
        workOrderAuthLogDTO.setUserName(name);
        workOrderAuthLogDTO.setUserEmail(email);
        workOrderAuthLogDTO.setAuditNodeName(currentTaskName);
        workOrderAuthLogDTO.setAuditNodeCode(nextTaskNode.getBpmnName());
        workOrderAuthLogDTO.setAuditResultDesc("待审核");
        workOrderAuthLogDTO.setApproveUsers(approveUsers);
        authLogDTOS.add(workOrderAuthLogDTO);
    }

    @Override
    public void checkFillEscResource(EcsModel model, String workOrderId) {
        //如果存在挂载数据盘 需要校验挂载数据盘的内容
        if (model.getMountDataDisk()) {
            List<EvsModel> mountDataDiskList = model.getMountDataDiskList();
            Precondition.checkArgument(mountDataDiskList, "mountDataDiskList不能为空");
            for (EvsModel evsModel : mountDataDiskList) {
                Precondition.checkArgument(evsModel.getSysDiskSize(), "挂载数据盘的系统盘大小不能为空");
                Precondition.checkArgument(evsModel.getSysDiskType(), "挂载数据盘的系统盘类型不能为空");
                evsModel.setAzCode(model.getAzCode());
                evsModel.setRegionCode(model.getRegionCode());
            }
        } else {
            model.setMountDataDiskList(null);
        }
        Precondition.checkArgument(model.getBindPublicIp(), "bindPublicIp不能为空");
        //如果绑定公网IP 需要校验公网IP的内容
        if (model.getBindPublicIp()) {
            List<EipModel> eipModelList = model.getEipModelList();
            Precondition.checkArgument(eipModelList, "eipModelList不能为空");
            for (EipModel eipModel : eipModelList) {
                Precondition.checkArgument(eipModel.getBandwidth(), "bandwidth不能为空");
                eipModel.setAzCode(model.getAzCode());
                eipModel.setRegionCode(model.getRegionCode());
            }
        } else {
            model.setEipModelList(null);
        }
        Precondition.checkArgument(model.getApplyTime(), "applyTime不能为空");
        Precondition.checkArgument(model.getOpenNum(), "openNum不能为空");
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0 && model.getOpenNum() < 201, "openNum必须大于0并且小于200");
        //数据都没问题了后创建数据
        //循环开通数量 ---下面部分需要抽离公用的方法
        // 循环创建指定数量的主产品
        //按照主产品的开通数量设置好Id数
        String originalVmName = model.getVmName(); // 保存原始名称
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品（ESC）
            String productType = model.getProductType();
            model.setMainIds(mainIds);
            model.setOpenNum(1);
            model.setId(mainId);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setOriginName(originalVmName);
            model.setVmName(originalVmName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            model.setPlane("私网地址");
            NonStanderWorkOrderProductDTO mainProduct = createMainProduct(model, mainId, productType, workOrderId);
            productManager.insert(mainProduct);
            // 创建关联子产品 如果不是ESC和GCS不用创建子产品
            createChildProducts(mainProduct.getId(), workOrderId, model);
        }
    }

    @Override
    public void checkFillEscResourceOffline(EcsModel model, String workOrderId) {
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0, "openNum必须大于0");
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getVmName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            model.setOriginName(originalName);
            model.setOpenNum(1);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setVmName(originalName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            String productType = model.getProductType();
            model.setId(mainId);
            NonStanderWorkOrderProductDTO product = new NonStanderWorkOrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setWorkOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.OFFLINE_OPEN.getCode());
            product.setGid(UuidUtil.getGid(productType));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }

    @Override
    public void checkFillMysqlResource(MysqlModel model, String workOrderId) {
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0, "openNum必须大于0");
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getMysqlName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            model.setOriginName(originalName);
            model.setOpenNum(1);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setMysqlName(originalName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            String productType = model.getProductType();
            model.setId(mainId);
            NonStanderWorkOrderProductDTO product = new NonStanderWorkOrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setWorkOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.OFFLINE_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.MYSQL.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }

    @Override
    public void checkFillRedisResource(RedisModel model, String workOrderId) {
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0, "openNum必须大于0");
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getRedisName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            model.setOriginName(originalName);
            model.setOpenNum(1);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setRedisName(originalName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            String productType = model.getProductType();
            model.setId(mainId);
            NonStanderWorkOrderProductDTO product = new NonStanderWorkOrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setWorkOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.OFFLINE_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.REDIS.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }

    @Override
    public void checkFillBmsResource(BareMetalModel model, String workOrderId) {
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0, "openNum必须大于0");
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getBmsName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            model.setOriginName(originalName);
            model.setOpenNum(1);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setBmsName(originalName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            String productType = model.getProductType();
            model.setId(mainId);
            NonStanderWorkOrderProductDTO product = new NonStanderWorkOrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setWorkOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.OFFLINE_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.BMS.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }

    @Override
    public void checkFillGmsResource(BareMetalModel model, String workOrderId) {
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0, "openNum必须大于0");
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getGmsName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            model.setOriginName(originalName);
            model.setOpenNum(1);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setGmsName(originalName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            String productType = model.getProductType();
            model.setId(mainId);
            NonStanderWorkOrderProductDTO product = new NonStanderWorkOrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setWorkOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.OFFLINE_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.GMS.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }

    @Override
    public void checkFillPostgreSqlResource(PostgreSqlModel model, String workOrderId) {
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0, "openNum必须大于0");
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getPostgreSqlName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            model.setOriginName(originalName);
            model.setOpenNum(1);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setPostgreSqlName(originalName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            String productType = model.getProductType();
            model.setId(mainId);
            NonStanderWorkOrderProductDTO product = new NonStanderWorkOrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setWorkOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.OFFLINE_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.POSTGRESQL.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }

    @Override
    public PageResult<NonStanderWorkOrderDTO> page(NonStanderWorkOrderQuery query, Long userId) {
        if (QueryParamCheckUtil.containsPercentage(query)) {
            return new PageResult<>();
        }

        // 判断查询的是待审批 还是已审批 还是驳回的工单
        UserCenterUserDTO user = userService.getUserById(userId);
        if (user == null) {
            log.warn("用户信息不存在，userId: {}", userId);
            return new PageResult<>();
        }

        List<String> ruleCodeList = UserServiceExt.processRoleData_(user.getOacRoles());
        String userIdStr = userId.toString();

        // 设置通用查询条件
        query.setUserId(userIdStr);
        query.setRoleCodeList(ruleCodeList);

        switch (query.getApprovalCode()) {
            case "pending":
                // 待审批工单
                query.setApprovalCode("pending");
                break;

            case "approved":
                // 已审批工单
                query.setApprovalCode("approved");
                break;

            case "rejected":
                // 驳回工单
                query.setApprovalCode("rejected");

                // 需要查询用户历史里有过审批拒绝相关的流程id
                List<String> processIds = workOrderAuthLogManager.groupByProcessInstanceId(
                        new WorkOrderAuthLogQuery()
                            .setUserId(userId)
                            .setAuditResult(OrderStatusEnum.REJECT.getCode())
                );

                // 如果processIds为空则直接返回空
                if (CollectionUtil.isEmpty(processIds)) {
                    log.info("用户没有被驳回的工单，userId: {}", userId);
                    return new PageResult<>();
                }

                query.setProcessIds(processIds);
                break;

            default:
                log.warn("未知的审批类型: {}", query.getApprovalCode());
                break;
        }

        return nonStanderWorkOrderManager.page(query);
    }

    @Override
    public void cancel(String workOrderId) {
        NonStanderWorkOrderDTO orderDTO = nonStanderWorkOrderManager.getById(workOrderId);
        Precondition.checkArgument(orderDTO, "当前工单不存在");
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        // 进行撤销工单权限校验
        preCheckCancelAuthority(orderDTO, currentUser);
        baseActivity.stop(orderDTO, currentUser.getId(), ActivityEnum.ActivityProcessEnum.NON_STANDARD_PROCESS);
        // 更新工单状态

        LocalDateTime now = LocalDateTime.now();
        WorkOrderAuthLogDTO authLogDTO = new WorkOrderAuthLogDTO()
                .setCreateTime(now)
                .setModifyTime(now)
                .setWorkOrderId(workOrderId)
                .setProcessInstanceId(orderDTO.getActivitiId())
                .setAdvice(ActivitiStatusEnum.ORDER_CANCEL.getNodeRemark())
                .setAuditNodeCode(ActivitiStatusEnum.ORDER_CANCEL.getNode())
                .setAuditNodeName(ActivitiStatusEnum.ORDER_CANCEL.getNodeRemark())
                .setUserId(orderDTO.getCreatedBy())
                .setUserName(currentUser.getUserName())
                .setUserPhone(currentUser.getPhone())
                .setUserEmail(currentUser.getUserEmail())
                .setAuditResult(ActivitiStatusEnum.ORDER_CANCEL.getNodeRemark());
        workOrderAuthLogManager.createWorkOrderAuthLog(authLogDTO);

        orderDTO.setCurrentNodeCode(ActivitiStatusEnum.ORDER_CANCEL.getNode());
        orderDTO.setCurrentNodeName(ActivitiStatusEnum.ORDER_CANCEL.getNodeRemark());
        orderDTO.setWorkOrderEndTime(now);
        orderDTO.setCurrentNodeStartTime(now);
        updateOrderStatus(orderDTO, currentUser.getId(), OrderStatusEnum.CLOSE.getCode());
    }

    @Override
    public AuditCountVo orderCount(NonStanderWorkOrderQuery query) {
        Long userId = Long.valueOf(query.getUserId());
        query.setApprovalCode(ApprovalTypeEnum.TODO_TYPE.getType());
        PageResult<NonStanderWorkOrderDTO> page = page(query, userId);
        Long todoSize = Optional.ofNullable(page.getTotal()).orElse(0L);
        query.setApprovalCode(ApprovalTypeEnum.DONE_TYPE.getType());
        page = page(query, userId);
        Long doneSize = Optional.ofNullable(page.getTotal()).orElse(0L);
        query.setApprovalCode(ApprovalTypeEnum.REJECT_TYPE.getType());
        page = page(query, userId);
        Long rejectSize = Optional.ofNullable(page.getTotal()).orElse(0L);
        return new AuditCountVo().setPendingCount(todoSize).setApprovedCount(doneSize).setRejectedCount(rejectSize);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(NonStanderAuditWorkOrderDTO auditDTO, Long userId) {
        Precondition.checkArgument(userId, "获取当前用户信息失败");
        UserCenterUserDTO auditUser = userService.getUserById(userId);
        NonStanderWorkOrderDTO orderDTO = nonStanderWorkOrderManager.getById(auditDTO.getOrderId());
        Precondition.checkArgument(orderDTO, "当前工单不存在");
        ActivityTaskTreeVo taskVo = baseActivity.taskNodesTree(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.NON_STANDARD_PROCESS);
        Precondition.checkArgument(taskVo, "已无审核流程节点！");


        if(auditDTO.getProductTotalCost()!=null){
            orderDTO.setProductTotalCost(auditDTO.getProductTotalCost());
            baseActivity.setVariable(orderDTO.getActivitiId(), "cost",
                    auditDTO.getProductTotalCost().compareTo(orderDTO.getContractCost()) < 0 ? 2 : 1);

            orderDTO.setRemark(auditDTO.getRemark());
            orderDTO.setProductApplyFile(auditDTO.getProductApplyFile());
        }

        // 处理归档IP
        if (auditDTO.getModelList() != null && !auditDTO.getModelList().isEmpty()) {
            auditDTO.getModelList().forEach(model -> {
                if (model.getId() != null && model.getArchivedIp() != null) {
                    // 更新对应产品的归档IP
                    productManager.update(new NonStanderWorkOrderProductDTO().setId(model.getId()).setArchivedIp(model.getArchivedIp()));
                }
            });
        }

        // 审核状态
        ActivityEnum.ActivityProcessEnum activityProcessEnum = ActivityEnum.ActivityProcessEnum.getByCode(orderDTO.getActiviteKey());
        ConfigTypeEnum configTypeEnum = ConfigTypeEnum.findByActivityEnum(activityProcessEnum);
        OacConfig oldTaskCode = configService.getByCode(taskVo.getCurrentTaskBpmnName(), configTypeEnum.getCode(), null, null);
        ActivityEnum.ActivityStatusEnum auditStatus = ActivityEnum.ActivityStatusEnum.getByCode(auditDTO.getActiviteStatus());

        String orderStatus = null;
        orderDTO.setCurrentNodeStartTime(LocalDateTime.now());
        switch (auditStatus) {
            case PASS:
                log.debug("taskVo.getCurrentTask()：{}", taskVo.getCurrentTaskBpmnName());
                if (!ActivitiStatusEnum.INFORMATION_ARCHIVE_H.getNode().equals(taskVo.getCurrentTaskBpmnName())
                        && !ActivitiStatusEnum.RESOURCE_CREATION_H.getNode().equals(taskVo.getCurrentTaskBpmnName())
                        && !ActivitiStatusEnum.INFORMATION_ARCHIVE_L.getNode().equals(taskVo.getCurrentTaskBpmnName())
                        && !ActivitiStatusEnum.RESOURCE_CREATION_L.getNode().equals(taskVo.getCurrentTaskBpmnName())) {
                    orderStatus = OrderStatusEnum.EXAMINING.getCode();
                    insertAuthLog(taskVo.getCurrentTaskBpmnName(), orderDTO, auditDTO.getAuditAdvice(), OrderLogStatusEnum.PASS, auditUser);
                } else {
                    //到这里已经是工单完成节点了
                    orderDTO.setWorkOrderEndTime(LocalDateTime.now());
                    insertAuthLog(taskVo.getCurrentTaskBpmnName(), orderDTO, auditDTO.getAuditAdvice(), OrderLogStatusEnum.PASS, auditUser);
                    orderStatus = OrderStatusEnum.END.getCode();
                    log.info("cmdb start");
                    cmdbReportService.createInstance(orderDTO.getId());
                    yunshuReportService.upper(orderDTO.getId());
                }
                break;
            case REJECT:
                insertAuthLog(taskVo.getCurrentTaskBpmnName(), orderDTO, auditDTO.getAuditAdvice(), OrderLogStatusEnum.REJECT, auditUser);
                orderStatus = OrderStatusEnum.findDescByAuditStatus(auditStatus).getCode();
                break;
            default:
        }
        baseActivity.complete(orderDTO, auditStatus, orderDTO.getActivitiId(), auditDTO.getAuditAdvice(), activityProcessEnum, userId, oldTaskCode,
                auditDTO.getNodeCode());

        taskVo = baseActivity.taskNodesTree(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.NON_STANDARD_PROCESS);
        orderDTO.setCurrentNodeCode(taskVo.getCurrentTaskBpmnName());
        orderDTO.setCurrentNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(taskVo.getCurrentTaskBpmnName(), null));
        updateOrderStatus(orderDTO, userId, orderStatus);
    }

    @Override
    public void processLogRoleNames(List<WorkOrderAuthLogDTO> authLogDTOS) {
        authLogDTOS.forEach(authLogDTO -> {
            String nodeCode = authLogDTO.getAuditNodeCode();

            // 默认设置为"--"
            String roleName = "--";

            if (StringUtils.isNotEmpty(nodeCode)) {
                if (ActivitiStatusEnum.USER_TASK.getNode().equals(nodeCode)) {
                    roleName = AuthorityCodeEnum.CUSTOMER_MANAGER.message();
                } else if (ActivitiStatusEnum.RESPONSE_SCHEME_MANAGER.getNode().equals(nodeCode)) {
                    roleName = AuthorityCodeEnum.RESPONSE_SCHEME_MANAGER.message();
                } else if (ActivitiStatusEnum.PROVINCE_GOV_ADMIN.getNode().equals(nodeCode)) {
                    roleName = AuthorityCodeEnum.PROVINCE_GOV_ADMIN.message();
                } else if (ActivitiStatusEnum.BRANCH_LEADER.getNode().equals(nodeCode)) {
                    roleName = AuthorityCodeEnum.BRANCH_LEADER.message();
                } else if (ActivitiStatusEnum.PROVINCE_GOV_LEADER.getNode().equals(nodeCode)) {
                    roleName = AuthorityCodeEnum.PROVINCE_GOV_LEADER.message();
                } else if (ActivitiStatusEnum.CLOUD_RESOURCE_LEADER.getNode().equals(nodeCode)) {
                    roleName = AuthorityCodeEnum.CLOUD_RESOURCE_LEADER.message();
                } else if (ActivitiStatusEnum.isXiaoYunNode(nodeCode)) {
                    roleName = AuthorityCodeEnum.XIAOYUN.message();
                }
            }

            authLogDTO.setAuditNodeRoleNames(roleName);
        });
    }

    @Override
    public CommonResult<String> createFlavor(FlavorCreateOpm opm) {
        log.info("开通自定义规格 createFlavor:{}", JSONObject.toJSONString(opm));
        String requestUrl = resourceHandle.resourceCenterUrl + resourceHandle.getCreateFlavor();
        Mapper dataMapper= OkHttps.sync(requestUrl)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(opm))
                .post()
                .getBody()
                .toMapper();
        String success = dataMapper.getString("success");
        Precondition.checkArgument("1".equals(success), "开通自定义规格失败");
        log.info("开通自定义规格成功，createFlavor ,response:{}", JSON.toJSON(dataMapper));
        return CommonResult.success(null);
    }

    private void updateOrderStatus(NonStanderWorkOrderDTO oacOrder, Long userId, String orderStatus) {
        //1. 修改操作订单
        oacOrder.setOrderStatus(orderStatus);
        oacOrder.setModifyTime(LocalDateTime.now());
        oacOrder.setUpdatedBy(userId);
        nonStanderWorkOrderManager.update(oacOrder);
    }

    private void insertAuthLog(String currentTask, NonStanderWorkOrderDTO orderDTO, String auditAdvice, OrderLogStatusEnum auditResult, UserCenterUserDTO auditUser) {
        if (StringUtils.isEmpty(currentTask) && auditResult != OrderLogStatusEnum.REJECT) {
            auditResult = OrderLogStatusEnum.END;
        }

        WorkOrderAuthLogDTO logDTO = new WorkOrderAuthLogDTO()
                .setCreateTime(orderDTO.getCurrentNodeStartTime())
                .setModifyTime(orderDTO.getCurrentNodeStartTime())
                .setWorkOrderId(orderDTO.getId())
                .setProcessInstanceId(orderDTO.getActivitiId())
                .setUserId(auditUser.getId())
                .setUserName(auditUser.getUserName())
                .setUserPhone(auditUser.getPhone())
                .setUserEmail(auditUser.getUserEmail())
                .setAdvice(auditAdvice)
                .setAuditNodeCode(currentTask)
                .setAuditResult(auditResult.getCode())
                .setAuditResultDesc(auditResult.getDesc())
                .setAuditNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode2(currentTask, auditResult).getNodeRemark());
        workOrderAuthLogManager.createWorkOrderAuthLog(logDTO);
    }

    private void preCheckCancelAuthority(NonStanderWorkOrderDTO orderDTO, UserCenterUserDTO currentUser) {
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        //判断当前工单是不是当前用户创建不然不能撤销
        Precondition.checkArgument(currentUser.getId().equals(orderDTO.getCreatedBy()), "当前用户不是工单创建人，不能撤销");
        // 获取工单当前审核节点
        ActivityTaskTreeVo taskVo = baseActivity.taskNodesTree(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.NON_STANDARD_PROCESS);
        Precondition.checkArgument(taskVo.getCurrentTaskBpmnName() != null, "已无审核流程节点！");
        List<String> codeList = Arrays.asList(
                com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.OFFLINE_OPEN_H.getNode(),
                com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.INFORMATION_ARCHIVE_H.getNode(),
                com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.NETWORK_PROVISIONING_H.getNode(),
                com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.RESOURCE_CREATION_H.getNode(),
                com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.OFFLINE_OPEN_L.getNode(),
                com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.INFORMATION_ARCHIVE_L.getNode(),
                com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.NETWORK_PROVISIONING_L.getNode(),
                com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.RESOURCE_CREATION_L.getNode());
        Precondition.checkArgument(!codeList.contains(taskVo.getCurrentTaskBpmnName()), "当前节点不允许撤销");
    }

    private void standardOrderFill(NonStanderWorkOrderDTO dto, String requestPath, String activityId,
                                   ActivityTaskVo taskVo, LocalDateTime workOrderStartTime) {
        UserCenterUserDTO currentUser = userHelper.getCurrentUser();
        dto.setCreatedBy(currentUser.getId());
        dto.setCreatedByName(currentUser.getUserName());
        dto.setOrderCode(CodeUtil.getOrderCode(CodePrefixEnum.OC.getCode()));
        dto.setActiviteKey(ActivityEnum.ActivityProcessEnum.NON_STANDARD_PROCESS.getCode());
        dto.setOrderType(OrderTypeEnum.SUBSCRIBE.getCode());
        dto.setOrderStatus(OrderStatusEnum.EXAMINING.getCode());
        dto.setCreateTime(workOrderStartTime);
        dto.setActivitiId(activityId);
        processBillIdAndCustomNo(dto.getBillId(), dto, requestPath);
        if (dto.getTenantId() != null) {
            CmpTenantDTO tenantDTO = cmpTenantService.selectById(dto.getTenantId());
            dto.setTenantName(tenantDTO.getName());
        }

        dto.setCreatedUserName(currentUser.getUserName());
        dto.setWorkOrderStartTime(workOrderStartTime);
        dto.setCurrentNodeStartTime(LocalDateTime.now());

        if (taskVo != null) {
            dto.setCurrentNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(taskVo.getCurrentTask(), RecoveryOrderNodeEnum.EXAMINING.getCode()));
            dto.setCurrentNodeCode(taskVo.getCurrentTask());
        }

        nonStanderWorkOrderManager.update(dto);
    }

    private void processBillIdAndCustomNo(String billId, NonStanderWorkOrderDTO orderDTO, String requestPath) {
        if (StringUtils.isBlank(billId)) {
            throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION.getCode(), "资源开通失败，业务系统id不能为空");
        }

        CmpTenantDTO cmpTenantDTO = cmpTenantService.selectByBillId(billId);
        // 通过订单绑定的业务系统获取业务系统所属租户id，避免直接从用户中获取，因为用户和租户是一对多的
        if (cmpTenantDTO == null) {
            throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION.getCode(), String.format("资源开通失败，e55计费号：[%s]数据库中不存在", billId));
        }
        orderDTO.setTenantId(cmpTenantDTO.getId());
        CmpAppDTO cmpAppDTO = businessService.selectByTenantId(cmpTenantDTO.getId());
        if (cmpAppDTO != null){
            orderDTO.setBusinessSystemCode(cmpAppDTO.getSystemCode());
            orderDTO.setBusinessSystemName(cmpAppDTO.getSystemName());
        }
    }

    // 创建主产品（ESC）
    private NonStanderWorkOrderProductDTO createMainProduct(EcsModel model, Long mainId, String productType, String workOrderId) {
        NonStanderWorkOrderProductDTO product = new NonStanderWorkOrderProductDTO();
        product.setId(mainId);
        product.setProductType(productType);
        product.setWorkOrderId(workOrderId);
        product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
        product.setGid(UuidUtil.getGid(productType));
        product.setSubOrderId(IdUtil.getSnowflake().nextId());
        product.setPropertySnapshot(JSON.toJSONString(model));
        return product;
    }

    private List<Long> generateMainIds(int openNum) {
        List<Long> mainIds = Lists.newArrayListWithCapacity(openNum);
        for (int i = 0; i < openNum; i++) {
            mainIds.add(IdUtil.getSnowflake().nextId());
        }
        return mainIds;
    }

    // 批量创建子产品（SSD/EIP）
    private void createChildProducts(Long parentId, String workOrderId, EcsModel model) {
        // 创建SSD子产品
        createEvsProduct(model.getMountDataDiskList(), parentId, workOrderId);

        // 创建EIP子产品
        createEipProduct(model.getEipModelList(), parentId, workOrderId);
    }

    private void createEvsProduct(List<EvsModel> models, Long parentId, String workOrderId) {
        if (models == null) return;
        models.stream().map(model -> {
            long id = IdUtil.getSnowflake().nextId();
            model.setId(id);
            NonStanderWorkOrderProductDTO product = new NonStanderWorkOrderProductDTO();
            product.setId(id);
            product.setProductType(ProductTypeEnum.EVS.getCode());
            product.setWorkOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setPropertySnapshot(JSON.toJSONString(model));
            product.setParentProductId(parentId);
            product.setGid(UuidUtil.getGid(ProductTypeEnum.EVS.getCode()));
            return product;
        }).forEach(productManager::insert);
    }

    private void createEipProduct(List<EipModel> models, Long parentId, String workOrderId) {
        if (models == null) return;
        models.stream().map(model -> {
            long id = IdUtil.getSnowflake().nextId();
            model.setId(id);
            NonStanderWorkOrderProductDTO product = new NonStanderWorkOrderProductDTO();
            product.setId(id);
            product.setProductType(ProductTypeEnum.EIP.getCode());
            product.setWorkOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setPropertySnapshot(JSON.toJSONString(model));
            product.setParentProductId(parentId);
            product.setGid(UuidUtil.getGid(ProductTypeEnum.EIP.getCode()));
            return product;
        }).forEach(productManager::insert);
    }


}
