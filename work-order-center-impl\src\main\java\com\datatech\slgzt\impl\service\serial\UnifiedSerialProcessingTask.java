package com.datatech.slgzt.impl.service.serial;

import com.datatech.slgzt.config.ScheduledTaskProperties;
import com.datatech.slgzt.enums.*;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.dto.ChangeWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderDTO;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.change.ChangeWorkOrderDTO;
import com.datatech.slgzt.model.query.StandardWorkOrderProductQuery;
import com.datatech.slgzt.service.change.ChangeResourceService;
import com.datatech.slgzt.service.recovery.RecoveryResourceService;
import com.datatech.slgzt.service.serial.SerialTaskLockService;
import com.datatech.slgzt.service.standard.StandardResOpenService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 统一串行处理任务
 *
 * <AUTHOR>
 * @description 合并标准工单和变更工单的串行处理任务，按优先级顺序执行
 * @date 2025年 07月23日
 */
@Slf4j
@Component
public class UnifiedSerialProcessingTask implements InitializingBean {

    @Resource
    private StandardWorkOrderProductManager standardProductManager;

    @Resource
    private ChangeWorkOrderProductManager changeWorkOrderProductManager;

    @Resource
    private ChangeWorkOrderManager changeWorkOrderManager;

    @Resource
    private RecoveryWorkOrderProductManager recoveryWorkOrderProductManager;

    @Resource
    private RecoveryWorkOrderManager recoveryWorkOrderManager;

    @Autowired
    private List<StandardResOpenService> standardResOpenServiceList;

    @Autowired
    private List<ChangeResourceService> changeResourceServiceList;

    @Autowired
    private List<RecoveryResourceService> recoveryResourceServiceList;

    @Resource
    private ScheduledTaskProperties scheduledTaskProperties;

    @Resource
    private SerialTaskLockService serialTaskLockService;

    private final Map<String, StandardResOpenService> standardResOpenServiceMap = Maps.newHashMap();
    private final Map<String, ChangeResourceService> changeResourceServiceMap = Maps.newHashMap();
    private final Map<String, RecoveryResourceService> recoveryResourceServiceMap = Maps.newHashMap();

    /**
     * 统一串行处理任务
     * <p>按资源池（REGION_CODE）分组的并行串行执行逻辑：
     * 1. 按REGION_CODE分组获取每个资源池中待执行的任务
     * 2. 为每个资源池尝试获取Redis分布式锁
     * 3. 获取锁成功的资源池并行执行对应的任务
     * 4. 每个资源池内部保持串行执行（开通、回收、变更任务互斥）
     * <p>执行优先级（在每个资源池内）：
     * 1. 回收工单串行回收任务（最高优先级）
     * 2. 标准工单串行开通任务
     * 3. 变更工单串行变更任务（最低优先级）
     */
    @Scheduled(cron = "#{@scheduledTaskProperties.serialTaskCron}")
    public void unifiedSerialProcessing() {
        log.info("统一串行处理任务开始");
        if (!scheduledTaskProperties.isEnableSerialTask()) {
            log.info("串行任务未开启，跳过执行");
            return;
        }

        try {
            // 按资源池分组处理任务
            processTasksByRegionCode();
        } catch (Exception e) {
            log.error("统一串行处理任务执行异常：{}", ExceptionUtils.getStackTrace(e));
        }

        log.info("统一串行处理任务结束");
    }

    /**
     * 按资源池分组处理任务
     * 为每个资源池获取待执行的任务，并尝试获取分布式锁执行
     */
    private void processTasksByRegionCode() {
        // 获取所有资源池的待执行任务
        Map<String, RecoveryWorkOrderProductDTO> recoveryTasks = recoveryWorkOrderProductManager.listNextSerialRecoveryByRegionCode();
        Map<String, StandardWorkOrderProductDTO> standardTasks = standardProductManager.listNextOpenByRegionCode();
        Map<String, ChangeWorkOrderProductDTO> changeTasks = changeWorkOrderProductManager.listNextSerialChangeByRegionCode();
        log.info("recoveryTasks:{}, standardTasks:{}, changeTasks:{}", recoveryTasks, standardTasks, changeTasks);

        // 收集所有涉及的资源池
        java.util.Set<String> allRegionCodes = new java.util.HashSet<>();
        allRegionCodes.addAll(recoveryTasks.keySet());
        allRegionCodes.addAll(standardTasks.keySet());
        allRegionCodes.addAll(changeTasks.keySet());

        if (allRegionCodes.isEmpty()) {
            log.info("当前没有需要串行处理的工单");
            return;
        }

        log.info("发现{}个资源池有待处理的串行任务：{}", allRegionCodes.size(), allRegionCodes);

        // 为每个资源池尝试执行任务
        int processedCount = 0;
        for (String regionCode : allRegionCodes) {
            try {
                if (processTasksForRegionCode(regionCode, recoveryTasks.get(regionCode),
                        standardTasks.get(regionCode), changeTasks.get(regionCode))) {
                    processedCount++;
                }
            } catch (Exception e) {
                log.error("资源池串行任务失败，资源池：{}，error message：{}", regionCode, ExceptionUtils.getStackTrace(e));
            }
        }

        log.info("本次执行完成，共处理了{}个资源池的任务", processedCount);
    }

    /**
     * 处理指定资源池的任务
     * 按优先级顺序：回收 > 标准开通 > 变更
     *
     * @param regionCode 资源池代码
     * @param recoveryTask 回收任务
     * @param standardTask 标准开通任务
     * @param changeTask 变更任务
     * @return true-处理了任务，false-未处理任务
     */
    private boolean processTasksForRegionCode(String regionCode, RecoveryWorkOrderProductDTO recoveryTask,
                                              StandardWorkOrderProductDTO standardTask,
                                              ChangeWorkOrderProductDTO changeTask) {
        // 尝试获取该资源池的分布式锁
        if (!serialTaskLockService.tryLock(regionCode)) {
            log.info("资源池[{}]的串行任务锁已被占用，跳过处理", regionCode);
            return false;
        }

        // 是否有任务成功下发过(标准工单，回收工单，变更工单都算，至少一个成功过)
        boolean taskSucceeded = false;
        try {
            log.info("成功获取资源池[{}]的串行任务锁，开始处理任务", regionCode);

            // 优先级1：处理回收工单（最高优先级）
            if (recoveryTask != null) {
                taskSucceeded = processRecoveryTask(recoveryTask);
                if (taskSucceeded && scheduledTaskProperties.isEnableAllSerial()) {
                    log.info("资源池[{}]：已处理回收工单任务", regionCode);
                    return true;
                }
            }

            // 优先级2：处理标准工单开通
            if (standardTask != null) {
                boolean standardResult = processStandardTask(standardTask);
                taskSucceeded |= standardResult;
                if (standardResult && scheduledTaskProperties.isEnableAllSerial()) {
                    log.info("资源池[{}]：已处理标准工单任务", regionCode);
                    return true;
                }
            }

            // 优先级3：处理变更工单（最低优先级）
            if (changeTask != null) {
                boolean changeResult = processChangeTask(changeTask);
                taskSucceeded |= changeResult;
                if (changeResult && scheduledTaskProperties.isEnableAllSerial()) {
                    log.info("资源池[{}]：已处理变更工单任务", regionCode);
                    return true;
                }
            }

            log.info("资源池[{}]：当前没有可执行的任务", regionCode);
            return false;
        } finally {
            // finally块，如果异常前有任务执行过了(三种任务非并行的情况下)，也不需要释放
            // 如果没有执行任何任务，则直接释放锁；否则等待回调/超时释放
            if (!taskSucceeded) {
                // 释放锁
                serialTaskLockService.unlock(regionCode);
            }
        }
    }

    /**
     * 处理单个回收任务
     *
     * @param nextProduct 待处理的回收产品
     * @return true-处理成功，false-处理失败
     */
    private boolean processRecoveryTask(RecoveryWorkOrderProductDTO nextProduct) {
        log.info("开始处理回收工单串行回收产品：{}", nextProduct.getId());

        // 安全检查：确保产品状态确实是待回收状态，避免重复处理
        // ！！！！
        // 这里的数据是提前获取的，然后再上锁
        // 需要再次判断，因为可能已经被其他服务改掉状态了
        if (!SerialRecoveryStatusEnum.RECOVERY_READY.getCode().equals(nextProduct.getSerialRecoveryStatus())) {
            log.warn("回收工单产品：{}状态异常，当前状态：{}，跳过处理",
                    nextProduct.getId(), nextProduct.getSerialRecoveryStatus());
            return false;
        }

        // 获取对应的回收服务
        RecoveryResourceService recoveryResourceService = recoveryResourceServiceMap.get(nextProduct.getProductType());
        if (recoveryResourceService == null) {
            log.error("未找到产品类型：{}对应的回收服务", nextProduct.getProductType());
            handleRecoveryFailure(nextProduct.getId(), "未找到对应的回收服务");
            return false;
        }

        // 获取工单信息
        RecoveryWorkOrderDTO workOrder = recoveryWorkOrderManager.getById(nextProduct.getWorkOrderId());
        if (workOrder == null) {
            log.error("未找到工单：{}", nextProduct.getWorkOrderId());
            handleRecoveryFailure(nextProduct.getId(), "未找到对应的工单");
            return false;
        }
        String regionCode = nextProduct.getRegionCode();
        long count = recoveryWorkOrderProductManager.listByWorkOrderId(nextProduct.getWorkOrderId()).stream()
                .filter(i ->
                        i.getRecoveryStatus().equals(RecoveryStatusEnum.RECLAIM_FAILURE.getType().toString())
                                && i.getRegionCode().equals(regionCode))
                .count();
        if (count > 2) {
            log.error("工单：{}，资源池：{}，存在3次回收失败的产品，跳过处理", nextProduct.getWorkOrderId(), regionCode);
            // 设置工单下待回收的产品为回收失败
            recoveryWorkOrderProductManager.updateSerialRecoveryStatusByOrderId(nextProduct.getWorkOrderId(),
                    regionCode,
                    SerialRecoveryStatusEnum.RECOVERY_READY.getCode(),
                    SerialRecoveryStatusEnum.RECOVERY_FAILED.getCode()
            );
            return false;
        }

        // 先更新产品状态为回收过，再执行回收操作
        RecoveryWorkOrderProductDTO updatingDTO = new RecoveryWorkOrderProductDTO();
        updatingDTO.setId(nextProduct.getId());
        updatingDTO.setSerialRecoveryStatus(SerialRecoveryStatusEnum.RECOVERED.getCode());
        recoveryWorkOrderProductManager.update(updatingDTO);

        // 执行回收操作
        try {
            // 调用回收服务执行回收操作（异步调用）
            recoveryResourceService.recoveryResource(workOrder, Collections.singletonList(nextProduct));
            log.info("回收工单产品：{}回收操作已提交执行", nextProduct.getId());
            return true;
        } catch (Exception e) {
            log.error("回收工单串行回收任务执行失败, product:{}, error message:{}",
                    nextProduct.getId(), ExceptionUtils.getStackTrace(e));
            handleRecoveryFailure(nextProduct.getId(), e.getMessage());
            return false;
        }
    }

    /**
     * 处理单个标准开通任务
     *
     * @param nextProduct 待处理的标准产品
     * @return true-处理成功，false-处理失败
     */
    private boolean processStandardTask(StandardWorkOrderProductDTO nextProduct) {
        log.info("开始处理标准工单串行开通产品：{}", nextProduct.getId());

        // 安全检查：确保产品状态确实是待开通状态，避免重复处理
        if (nextProduct.getSerialOpenStatus() == null || !Integer.valueOf(2).equals(nextProduct.getSerialOpenStatus())) {
            log.warn("标准工单产品：{}状态异常，当前状态：{}，跳过处理",
                    nextProduct.getId(), nextProduct.getSerialOpenStatus());
            return false;
        }

        // 获取对应的开通服务
        StandardResOpenService standardResOpenService = standardResOpenServiceMap.get(nextProduct.getProductType());
        if (standardResOpenService == null) {
            log.error("未找到产品类型：{}对应的开通服务", nextProduct.getProductType());
            // 处理开通失败
            StandardWorkOrderProductDTO failUpdateDTO = new StandardWorkOrderProductDTO();
            failUpdateDTO.setId(nextProduct.getId());
            failUpdateDTO.setOpenStatus(ResOpenEnum.OPEN_FAIL.getCode());
            failUpdateDTO.setMessage("未找到对应的开通服务");
            standardProductManager.update(failUpdateDTO);
            return false;
        }

        String regionCode = nextProduct.getRegionCode();
        StandardWorkOrderProductQuery query = new StandardWorkOrderProductQuery();
        query.setOrderId(nextProduct.getWorkOrderId());
        long count = standardProductManager.list(query).stream()
                .filter(i ->
                        i.getOpenStatus().equals(ResOpenEnum.OPEN_FAIL.getCode())
                                && i.getRegionCode().equals(regionCode))
                .count();
        if (count > 2) {
            log.error("工单：{}，资源池：{}，存在3次开通失败的产品，跳过处理", nextProduct.getWorkOrderId(), regionCode);
            // 设置工单下待开通的产品为开通失败
            standardProductManager.updateSerialOpenStatusByOrderId(nextProduct.getWorkOrderId(),
                    regionCode,
                    2,
                    -1
            );
            return false;
        }

        // 更新产品状态为开通中
        StandardWorkOrderProductDTO openingUpdateDTO = new StandardWorkOrderProductDTO();
        openingUpdateDTO.setId(nextProduct.getId());
        openingUpdateDTO.setSerialOpenStatus(3);
        standardProductManager.update(openingUpdateDTO);

        // 执行开通操作
        try {
            standardResOpenService.openStandardResource(nextProduct);
            log.info("标准工单产品：{}开通操作已提交执行", nextProduct.getId());
            return true;
        } catch (Exception e) {
            log.error("标准工单串行开通任务执行失败, product:{}, error message:{}",
                    nextProduct.getId(), ExceptionUtils.getStackTrace(e));

            // 处理开通失败
            StandardWorkOrderProductDTO failUpdateDTO = new StandardWorkOrderProductDTO();
            failUpdateDTO.setId(nextProduct.getId());
            failUpdateDTO.setOpenStatus(ResOpenEnum.OPEN_FAIL.getCode());
            failUpdateDTO.setMessage(e.getMessage());
            standardProductManager.update(failUpdateDTO);
            return false;
        }
    }

    /**
     * 处理单个变更任务
     *
     * @param nextProduct 待处理的变更产品
     * @return true-处理成功，false-处理失败
     */
    private boolean processChangeTask(ChangeWorkOrderProductDTO nextProduct) {
        log.info("开始处理变更工单串行变更产品：{}", nextProduct.getId());

        // 安全检查：确保产品状态确实是待变更状态，避免重复处理
        if (!SerialChangeStatusEnum.CHANGE_READY.getCode().equals(nextProduct.getSerialChangeStatus())) {
            log.warn("变更工单产品：{}状态异常，当前状态：{}，跳过处理",
                    nextProduct.getId(), nextProduct.getSerialChangeStatus());
            return false;
        }

        // 获取对应的变更服务
        ChangeResourceService changeResourceService = changeResourceServiceMap.get(nextProduct.getProductType());
        if (changeResourceService == null) {
            log.error("未找到产品类型：{}对应的变更服务", nextProduct.getProductType());
            handleChangeFailure(nextProduct.getId(), "未找到对应的变更服务");
            return false;
        }

        // 更新产品状态为变更过
        ChangeWorkOrderProductDTO updatingDTO = new ChangeWorkOrderProductDTO();
        updatingDTO.setId(nextProduct.getId());
        updatingDTO.setSerialChangeStatus(SerialChangeStatusEnum.CHANGED.getCode());
        changeWorkOrderProductManager.update(updatingDTO);

        // 执行变更操作
        try {
            // 获取工单信息
            ChangeWorkOrderDTO workOrder = changeWorkOrderManager.getById(nextProduct.getWorkOrderId());
            if (workOrder == null) {
                log.error("未找到工单：{}", nextProduct.getWorkOrderId());
                handleChangeFailure(nextProduct.getId(), "未找到对应的工单");
                return false;
            }

            // 调用变更服务执行变更操作（异步调用）
            changeResourceService.changeResource(workOrder, Collections.singletonList(nextProduct));
            log.info("变更工单产品：{}变更操作已提交执行", nextProduct.getId());
            return true;
        } catch (Exception e) {
            log.error("变更工单串行变更任务执行失败, product:{}, error message:{}",
                    nextProduct.getId(), ExceptionUtils.getStackTrace(e));
            handleChangeFailure(nextProduct.getId(), e.getMessage());
            return false;
        }
    }

    /**
     * 处理回收失败的情况
     * 失败的产品保持当前状态，需要人工介入处理，不自动重新排队
     */
    private void handleRecoveryFailure(Long productId, String errorMessage) {
        RecoveryWorkOrderProductDTO failDTO = new RecoveryWorkOrderProductDTO();
        failDTO.setId(productId);
        // 失败时保持当前的串行状态（RECOVERED），不重新排队，需要人工介入处理
        // failDTO.setSerialRecoveryStatus() 不设置，保持当前状态
        failDTO.setRecoveryStatus(String.valueOf(RecoveryStatusEnum.RECLAIM_FAILURE.getType()));
        failDTO.setMessage(errorMessage);
        recoveryWorkOrderProductManager.update(failDTO);

        log.warn("回收工单产品：{}回收失败，已设置为失败状态，需要人工介入处理。错误信息：{}", productId, errorMessage);
    }

    /**
     * 处理变更失败的情况
     * 失败的产品保持当前状态，需要人工介入处理，不自动重新排队
     */
    private void handleChangeFailure(Long productId, String errorMessage) {
        ChangeWorkOrderProductDTO failDTO = new ChangeWorkOrderProductDTO();
        failDTO.setId(productId);
        // 失败时保持当前的串行状态（CHANGED），不重新排队，需要人工介入处理
        failDTO.setChangeStatus(ChangeTypeProductStatusEnum.CHANGE_FAIL.getCode());
        failDTO.setMessage(errorMessage);
        changeWorkOrderProductManager.update(failDTO);

        log.warn("变更工单产品：{}变更失败，已设置为失败状态，需要人工介入处理。错误信息：{}", productId, errorMessage);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化标准工单开通服务映射
        for (StandardResOpenService standardResOpenService : standardResOpenServiceList) {
            standardResOpenServiceMap.put(standardResOpenService.registerOpenService()
                    .getCode(), standardResOpenService);
        }

        // 初始化变更工单变更服务映射
        for (ChangeResourceService changeResourceService : changeResourceServiceList) {
            changeResourceServiceMap.put(changeResourceService.registerOpenService().getCode(), changeResourceService);
        }

        // 初始化回收工单回收服务映射
        for (RecoveryResourceService recoveryResourceService : recoveryResourceServiceList) {
            recoveryResourceServiceMap.put(recoveryResourceService.registerOpenService().getCode(), recoveryResourceService);
        }

        log.info("统一串行处理任务初始化完成，注册了{}个标准开通服务，{}个变更服务，{}个回收服务",
                standardResOpenServiceMap.size(), changeResourceServiceMap.size(), recoveryResourceServiceMap.size());
    }
}
