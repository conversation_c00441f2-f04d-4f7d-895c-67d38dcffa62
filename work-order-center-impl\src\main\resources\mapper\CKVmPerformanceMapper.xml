<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datatech.slgzt.dao.mapper.CKVmPerformanceMapper">

    <!-- 聚合虚拟机性能数据 -->
    <select id="aggregateVmPerformance" resultType="com.datatech.slgzt.dao.model.VMResourcePerformanceDO">
        SELECT 
            toStartOfHour(create_time) AS ckLastedTime,
            avg(if(isNull(cpu_util) OR cpu_util &lt; 0, 0, cpu_util)) AS ckCpuUtil,
            avg(if(isNull(mem_util) OR mem_util &lt; 0, 0, mem_util)) AS ckMemUtil,
            avg(if(isNull(disk_read_iops) OR disk_read_iops &lt; 0, 0, disk_read_iops)) AS ckDiskReadIops,
            avg(if(isNull(disk_write_iops) OR disk_write_iops &lt; 0, 0, disk_write_iops)) AS ckDiskWriteIops,
            avg(if(isNull(capacity) OR capacity &lt; 0, 0, capacity)) AS ckCapacity,
            avg(if(isNull(capacity_used) OR capacity_used &lt; 0, 0, capacity_used)) AS ckCapacityUsed,
            avg(if(isNull(capacity_util) OR capacity_util &lt; 0, 0, capacity_util)) AS ckCapacityUtil
        FROM 
            vm_performance_distributed 
        WHERE
            create_time &gt;= #{startTime}
            AND create_time &lt; #{endTime}
            <!-- 需要判断regionCodes是否有数值 -->
            <if test="regionCodes != null and regionCodes.size() > 0">
                AND region_code IN 
                <foreach collection="regionCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        GROUP BY 
            ckLastedTime
        ORDER BY 
            ckLastedTime
    </select>

    <!-- 聚合虚拟机性能数据不group算总数 -->
    <select id="aggregateVmPerformanceTotal" resultType="com.datatech.slgzt.model.dto.VMResourcePerformanceStringDTO">
        SELECT
        avg(if(isNull(cpu_util) OR cpu_util &lt; 0, 0, cpu_util)) AS ckCpuUtil,
        avg(if(isNull(mem_util) OR mem_util &lt; 0, 0, mem_util)) AS ckMemUtil,
        avg(if(isNull(disk_read_iops) OR disk_read_iops &lt; 0, 0, disk_read_iops)) AS ckDiskReadIops,
        avg(if(isNull(disk_write_iops) OR disk_write_iops &lt; 0, 0, disk_write_iops)) AS ckDiskWriteIops,
        avg(if(isNull(capacity) OR capacity &lt; 0, 0, capacity)) AS ckCapacity,
        avg(if(isNull(capacity_used) OR capacity_used &lt; 0, 0, capacity_used)) AS ckCapacityUsed,
        avg(if(isNull(capacity_util) OR capacity_util &lt; 0, 0, capacity_util)) AS ckCapacityUtil
        FROM
        vm_performance_distributed
        WHERE
        create_time &gt;= #{startTime}
        AND create_time &lt; #{endTime}
        <!-- 需要判断regionCodes是否有数值 -->
        <if test="regionCodes != null and regionCodes.size() > 0">
            AND region_code IN
            <foreach collection="regionCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 查询虚拟机性能列表 -->
    <select id="listVmPerformance" resultType="com.datatech.slgzt.dao.model.VMResourcePerformanceDO">
        SELECT
            uuid as vmInstanceUuid,
            argMax(host_name, create_time) AS ckHostName,
            argMax(region_code, create_time) AS resourcePoolCode,
            argMax(cloud, create_time) AS domainCode,
            avg(if(isNull(cpu_util) OR cpu_util &lt; 0, 0, cpu_util)) AS CK_CPU_UTIL,
            avg(if(isNull(mem_util) OR mem_util &lt; 0, 0, mem_util)) AS CK_MEM_UTIL,
            avg(if(isNull(disk_read_iops) OR disk_read_iops &lt; 0, 0, disk_read_iops)) AS CK_DISK_READ_IOPS,
            avg(if(isNull(disk_write_iops) OR disk_write_iops &lt; 0, 0, disk_write_iops)) AS CK_DISK_WRITE_IOPS,
            avg(if(isNull(capacity) OR capacity &lt; 0, 0, capacity)) AS CK_CAPACITY,
            avg(if(isNull(capacity_used) OR capacity_used &lt; 0, 0, capacity_used)) AS CK_CAPACITY_USED,
            avg(if(isNull(capacity_util) OR capacity_util &lt; 0, 0, capacity_util)) AS CK_CAPACITY_UTIL
        FROM
            vm_performance_distributed
        WHERE
            create_time &gt;= #{startTime}
            AND create_time &lt; #{endTime}
            AND host_name IS NOT NULL
            AND host_name &lt;&gt; ''
            <!-- 需要判断regionCodes是否有数值 -->
            <if test="regionCodes != null and regionCodes.size() > 0">
                AND region_code IN
                <foreach collection="regionCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        GROUP BY
            uuid
    </select>

    <!-- 手动分页查询虚拟机性能列表 -->
    <select id="listVmPerformanceWithPage" resultType="com.datatech.slgzt.dao.model.VMResourcePerformanceDO">
        SELECT
            uuid as vmInstanceUuid,
            argMax(host_name, create_time) AS ckHostName,
            argMax(region_code, create_time) AS resourcePoolCode,
            argMax(cloud, create_time) AS domainCode,
            avg(cpu_util) AS ckCpuUtil,
            avg(mem_util) AS ckMemUtil,
            avg(disk_read_iops) AS ckDiskReadIops,
            avg(disk_write_iops) AS ckDiskWriteIops,
            avg(capacity) AS ckCapacity,
            avg(capacity_used) AS ckCapacityUsed,
            avg(capacity_util) AS ckCapacityUtil
        FROM
            vm_performance_distributed
        WHERE
            create_time &gt;= #{startTime}
            AND create_time &lt; #{endTime}
            AND host_name IS NOT NULL
            AND host_name &lt;&gt; ''
            <if test="regionCodes != null and regionCodes.size() > 0">
                AND region_code IN
                <foreach collection="regionCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        GROUP BY
            uuid
        ORDER BY
            uuid
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 查询虚拟机性能列表总数 -->
    <select id="countVmPerformance" resultType="java.lang.Long">
        SELECT
            count(DISTINCT uuid)
        FROM
            vm_performance_distributed
        WHERE
            create_time &gt;= #{startTime}
            AND create_time &lt; #{endTime}
            AND host_name IS NOT NULL
            AND host_name &lt;&gt; ''
            <if test="regionCodes != null and regionCodes.size() > 0">
                AND region_code IN
                <foreach collection="regionCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </select>

</mapper>
