package com.datatech.slgzt.dao;

import com.datatech.slgzt.dao.model.CcaeTaskDO;
import com.datatech.slgzt.model.query.CcaeTaskQuery;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CCAE任务DAO测试类
 * 
 * <AUTHOR>
 * @date 2025-12-30
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class CcaeTaskDAOTest {

    @Resource
    private CcaeTaskDAO ccaeTaskDAO;

    @Test
    public void testInsertAndQueryWithNewFields() {
        // 准备测试数据
        CcaeTaskDO ccaeTaskDO = new CcaeTaskDO();
        ccaeTaskDO.setCcaeId("test-ccae-001");
        ccaeTaskDO.setPassItemCount(10);
        ccaeTaskDO.setCheckItemCount(20);
        ccaeTaskDO.setDeviceScope("主机");
        ccaeTaskDO.setTemplateCategory("Deep");
        ccaeTaskDO.setExecuteMethod(0);
        ccaeTaskDO.setElapsedTimeSec(300);
        ccaeTaskDO.setProgress(50);
        ccaeTaskDO.setCcaeCreateTime(LocalDateTime.now());
        ccaeTaskDO.setCreatorId(1001L);
        ccaeTaskDO.setCreatorName("测试用户");
        ccaeTaskDO.setCreateTime(LocalDateTime.now());
        ccaeTaskDO.setModifyTime(LocalDateTime.now());
        
        // 设置新字段
        List<String> computeList = new ArrayList<>(Arrays.asList("SN001", "SN002", "SN003"));
        List<String> networkIpList = new ArrayList<>(Arrays.asList("*************", "*************", "*************"));
        ccaeTaskDO.setComputeList(computeList);
        ccaeTaskDO.setNetworkIpList(networkIpList);

        // 插入数据
        int insertResult = ccaeTaskDAO.insert(ccaeTaskDO);
        assertEquals(1, insertResult);
        assertNotNull(ccaeTaskDO.getId());

        // 根据ID查询验证
        CcaeTaskDO queryResult = ccaeTaskDAO.getById(ccaeTaskDO.getId());
        assertNotNull(queryResult);
        assertEquals("test-ccae-001", queryResult.getCcaeId());
        assertEquals(computeList, queryResult.getComputeList());
        assertEquals(networkIpList, queryResult.getNetworkIpList());

        // 测试根据计算节点SN查询
        CcaeTaskQuery query1 = new CcaeTaskQuery();
        query1.setComputeSn("SN002");
        List<CcaeTaskDO> results1 = ccaeTaskDAO.list(query1);
        assertFalse(results1.isEmpty());
        assertTrue(results1.stream().anyMatch(task -> "test-ccae-001".equals(task.getCcaeId())));

        // 测试根据网络IP查询
        CcaeTaskQuery query2 = new CcaeTaskQuery();
        query2.setNetworkIp("*************");
        List<CcaeTaskDO> results2 = ccaeTaskDAO.list(query2);
        assertFalse(results2.isEmpty());
        assertTrue(results2.stream().anyMatch(task -> "test-ccae-001".equals(task.getCcaeId())));

        // 测试查询不存在的SN
        CcaeTaskQuery query3 = new CcaeTaskQuery();
        query3.setComputeSn("SN999");
        List<CcaeTaskDO> results3 = ccaeTaskDAO.list(query3);
        assertTrue(results3.isEmpty() || results3.stream().noneMatch(task -> "test-ccae-001".equals(task.getCcaeId())));
    }

    @Test
    public void testUpdateWithNewFields() {
        // 先插入一条记录
        CcaeTaskDO ccaeTaskDO = new CcaeTaskDO();
        ccaeTaskDO.setCcaeId("test-ccae-002");
        ccaeTaskDO.setPassItemCount(5);
        ccaeTaskDO.setCheckItemCount(10);
        ccaeTaskDO.setDeviceScope("网络");
        ccaeTaskDO.setTemplateCategory("Basic");
        ccaeTaskDO.setExecuteMethod(1);
        ccaeTaskDO.setElapsedTimeSec(150);
        ccaeTaskDO.setProgress(0);
        ccaeTaskDO.setCcaeCreateTime(LocalDateTime.now());
        ccaeTaskDO.setCreatorId(1002L);
        ccaeTaskDO.setCreatorName("测试用户2");
        ccaeTaskDO.setCreateTime(LocalDateTime.now());
        ccaeTaskDO.setModifyTime(LocalDateTime.now());
        
        List<String> initialComputeList = Arrays.asList("SN100");
        List<String> initialNetworkIpList = Arrays.asList("********");
        ccaeTaskDO.setComputeList(initialComputeList);
        ccaeTaskDO.setNetworkIpList(initialNetworkIpList);

        ccaeTaskDAO.insert(ccaeTaskDO);

        // 更新字段
        List<String> updatedComputeList = Arrays.asList("SN100", "SN200", "SN300");
        List<String> updatedNetworkIpList = Arrays.asList("********", "********", "********");
        ccaeTaskDO.setComputeList(updatedComputeList);
        ccaeTaskDO.setNetworkIpList(updatedNetworkIpList);
        ccaeTaskDO.setProgress(100);
        ccaeTaskDO.setModifyTime(LocalDateTime.now());

        int updateResult = ccaeTaskDAO.updateById(ccaeTaskDO);
        assertEquals(1, updateResult);

        // 验证更新结果
        CcaeTaskDO updatedTask = ccaeTaskDAO.getById(ccaeTaskDO.getId());
        assertNotNull(updatedTask);
        assertEquals(100, updatedTask.getProgress());
        assertEquals(updatedComputeList, updatedTask.getComputeList());
        assertEquals(updatedNetworkIpList, updatedTask.getNetworkIpList());
    }
}
