package com.datatech.slgzt.service;

import com.datatech.slgzt.model.dto.CcaeTaskDTO;
import com.datatech.slgzt.model.query.CcaeTaskQuery;
import com.datatech.slgzt.service.ccae.CcaeTaskService;
import com.datatech.slgzt.utils.PageResult;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CCAE任务Service测试类
 *
 * <AUTHOR>
 * @date 2025-12-30
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class CcaeTaskServiceTest {

    @Resource
    private CcaeTaskService ccaeTaskService;

    @Test
    public void testCreateTaskWithNewFields() {
        // 准备测试数据
        CcaeTaskDTO ccaeTaskDTO = new CcaeTaskDTO();
        ccaeTaskDTO.setCcaeId("service-test-001");
        ccaeTaskDTO.setDeviceScope("主机");
        ccaeTaskDTO.setTemplateCategory("Deep");
        ccaeTaskDTO.setExecuteMethod(0);
        ccaeTaskDTO.setCreatorId(2001L);
        ccaeTaskDTO.setCreatorName("Service测试用户");

        // 设置新字段
        List<String> computeList = Arrays.asList("SN-SERVICE-001", "SN-SERVICE-002");
        List<String> networkIpList = Arrays.asList("***********", "***********");
        ccaeTaskDTO.setComputeList(computeList);
        ccaeTaskDTO.setNetworkIpList(networkIpList);

        // 创建任务
        ccaeTaskService.createTask(ccaeTaskDTO);

        // 验证创建结果
        CcaeTaskDTO createdTask = ccaeTaskService.getByCcaeId("service-test-001");
        assertNotNull(createdTask);
        assertEquals("service-test-001", createdTask.getCcaeId());
        assertEquals(computeList, createdTask.getComputeList());
        assertEquals(networkIpList, createdTask.getNetworkIpList());
        assertEquals(0, createdTask.getProgress()); // 默认进度为0
    }

    @Test
    public void testQueryTasksByNewFields() {
        // 创建多个测试任务
        createTestTask("query-test-001",
                Arrays.asList("SN-QUERY-001", "SN-QUERY-002"),
                Arrays.asList("*************", "*************"));

        createTestTask("query-test-002",
                Arrays.asList("SN-QUERY-003", "SN-QUERY-004"),
                Arrays.asList("*************", "*************"));

        createTestTask("query-test-003",
                Arrays.asList("SN-QUERY-001", "SN-QUERY-005"),
                Arrays.asList("*************", "*************"));

        // 测试根据计算节点SN查询
        CcaeTaskQuery query1 = new CcaeTaskQuery();
        query1.setComputeSn("SN-QUERY-001");
        List<CcaeTaskDTO> results1 = ccaeTaskService.listTasks(query1);
        assertEquals(2, results1.size()); // 应该找到2个任务
        assertTrue(results1.stream().anyMatch(task -> "query-test-001".equals(task.getCcaeId())));
        assertTrue(results1.stream().anyMatch(task -> "query-test-003".equals(task.getCcaeId())));

        // 测试根据网络IP查询
        CcaeTaskQuery query2 = new CcaeTaskQuery();
        query2.setNetworkIp("*************");
        List<CcaeTaskDTO> results2 = ccaeTaskService.listTasks(query2);
        assertEquals(2, results2.size()); // 应该找到2个任务
        assertTrue(results2.stream().anyMatch(task -> "query-test-001".equals(task.getCcaeId())));
        assertTrue(results2.stream().anyMatch(task -> "query-test-003".equals(task.getCcaeId())));

        // 测试分页查询
        CcaeTaskQuery pageQuery = new CcaeTaskQuery();
        pageQuery.setPageNum(1);
        pageQuery.setPageSize(2);
        PageResult<CcaeTaskDTO> pageResult = ccaeTaskService.pageTasks(pageQuery);
        assertNotNull(pageResult);
        assertTrue(pageResult.getTotal() >= 3);
        assertEquals(2, pageResult.getRecords().size());
    }

    @Test
    public void testUpdateTaskWithNewFields() {
        // 创建初始任务
        String ccaeId = "update-test-001";
        createTestTask(ccaeId,
                Arrays.asList("SN-UPDATE-001"),
                Arrays.asList("**********"));

        // 获取任务并更新
        CcaeTaskDTO task = ccaeTaskService.getByCcaeId(ccaeId);
        assertNotNull(task);

        // 更新字段
        List<String> newComputeList = Arrays.asList("SN-UPDATE-001", "SN-UPDATE-002", "SN-UPDATE-003");
        List<String> newNetworkIpList = Arrays.asList("**********", "**********", "**********");
        task.setComputeList(newComputeList);
        task.setNetworkIpList(newNetworkIpList);
        task.setProgress(75);

        ccaeTaskService.updateTask(task);

        // 验证更新结果
        CcaeTaskDTO updatedTask = ccaeTaskService.getByCcaeId(ccaeId);
        assertNotNull(updatedTask);
        assertEquals(75, updatedTask.getProgress());
        assertEquals(newComputeList, updatedTask.getComputeList());
        assertEquals(newNetworkIpList, updatedTask.getNetworkIpList());
    }

    @Test
    public void testListOperations() {
        // 创建任务
        CcaeTaskDTO task = new CcaeTaskDTO();
        task.setCcaeId("list-ops-test-001");
        task.setDeviceScope("主机");
        task.setTemplateCategory("Deep");
        task.setExecuteMethod(0);
        task.setCreatorId(4001L);
        task.setCreatorName("List测试用户");

        // 使用可变List
        List<String> computeList = new ArrayList<>();
        computeList.add("SN-LIST-001");
        computeList.add("SN-LIST-002");

        List<String> networkIpList = new ArrayList<>();
        networkIpList.add("***********");
        networkIpList.add("***********");

        task.setComputeList(computeList);
        task.setNetworkIpList(networkIpList);

        ccaeTaskService.createTask(task);

        // 获取任务并验证
        CcaeTaskDTO createdTask = ccaeTaskService.getByCcaeId("list-ops-test-001");
        assertNotNull(createdTask);
        assertEquals(2, createdTask.getComputeList().size());
        assertEquals(2, createdTask.getNetworkIpList().size());

        // 动态添加元素
        createdTask.getComputeList().add("SN-LIST-003");
        createdTask.getNetworkIpList().add("***********");

        ccaeTaskService.updateTask(createdTask);

        // 验证动态添加的结果
        CcaeTaskDTO updatedTask = ccaeTaskService.getByCcaeId("list-ops-test-001");
        assertEquals(3, updatedTask.getComputeList().size());
        assertEquals(3, updatedTask.getNetworkIpList().size());
        assertTrue(updatedTask.getComputeList().contains("SN-LIST-003"));
        assertTrue(updatedTask.getNetworkIpList().contains("***********"));
    }

    /**
     * 创建测试任务的辅助方法
     */
    private void createTestTask(String ccaeId, List<String> computeList, List<String> networkIpList) {
        CcaeTaskDTO ccaeTaskDTO = new CcaeTaskDTO();
        ccaeTaskDTO.setCcaeId(ccaeId);
        ccaeTaskDTO.setDeviceScope("主机");
        ccaeTaskDTO.setTemplateCategory("Basic");
        ccaeTaskDTO.setExecuteMethod(0);
        ccaeTaskDTO.setCreatorId(3001L);
        ccaeTaskDTO.setCreatorName("测试创建者");
        ccaeTaskDTO.setComputeList(computeList);
        ccaeTaskDTO.setNetworkIpList(networkIpList);

        ccaeTaskService.createTask(ccaeTaskDTO);
    }
}
