package com.datatech.slgzt.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "ccae")
public class CcaeProperties {
    private String username = "yunshu";
    private String password = "Rj4$yffj";
    private String url = "https://188.108.249.69:26335";
}
