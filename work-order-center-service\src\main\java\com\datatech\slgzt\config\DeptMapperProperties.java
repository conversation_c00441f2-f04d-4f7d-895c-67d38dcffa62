package com.datatech.slgzt.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 定时任务配置
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "dept")
public class DeptMapperProperties {



    private String mapperStr = "[]";


}
