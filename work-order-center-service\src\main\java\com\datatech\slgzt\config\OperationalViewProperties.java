package com.datatech.slgzt.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 定时任务配置
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "operational")
public class OperationalViewProperties {
    private String eventUrl;
    private String eventAppId;
    private String eventAppSecret;
    private String taTaiUrl;
    private String taTaiAccount;
    private String taTaiPassword;
    private String logUrl;
    private String logUsername;
    private String logPassword;
}
