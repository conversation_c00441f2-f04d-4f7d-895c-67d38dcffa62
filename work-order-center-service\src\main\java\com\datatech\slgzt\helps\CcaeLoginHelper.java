package com.datatech.slgzt.helps;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.config.CcaeProperties;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.OkHttpsUtils;
import com.ejlchina.data.Mapper;
import com.google.common.collect.Maps;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 04月09日 14:46:04
 */
@Component
public class CcaeLoginHelper implements InitializingBean {

    public static CcaeLoginHelper INSTANCE;

    @Resource
    private CcaeProperties ccaeProperties;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 获取adminToken
     */
    public String getToken() throws NoSuchAlgorithmException, KeyManagementException {
        // redis string类型，key为ccae:token，value就是token，不是map类型！！！！！
        RBucket<String> bucket = redissonClient.getBucket("ccae:token");
        String redisToken = bucket.get();
        if (ObjNullUtils.isNotNull(redisToken)) {
            return redisToken;
        }

        //登录参数
        Map<String, String> requestMap = Maps.newHashMap();
        requestMap.put("userName", ccaeProperties.getUsername());
        requestMap.put("value", ccaeProperties.getPassword());
        requestMap.put("grantType", "password");
        Mapper mapper = OkHttpsUtils
                .httpIgnoreHttpsCertificate()
                .sync(ccaeProperties.getUrl() + "/api/v1/security/sessions")
                .addBodyPara(requestMap)
                .bodyType("json")
                .put()
                .getBody()
                .toMapper();
        // {
        //    "retCode": 0,
        //    "retMsg": "success",
        //    "data": {
        //        "accessSession": "x-7ygavxan07sauq895jrzarqq7zljukvt7s7wbulj7vs4sac844ml6q1cfx08jxlhnwfwsaap6o48djupjvils7qmkas9mmftqpqn5ccao4855cg4eq85fsk6jvdh09di",
        //        "roaRand": "5ad0fd30c3525f3124846aa9357851018aa34c4dab275982",
        //        "expires": 1800,
        //        "additionalInfo": null
        //    }
        //}
        String jsonStr = CcaeUnpackHelper.unpackData(mapper, "调用底层登录接口失败");
        JSONObject jsonObject = JSON.parseObject(jsonStr);
        //获取token
        String token = jsonObject.getString("accessSession");
        // expires
        int expires = jsonObject.getIntValue("expires");
        bucket.set(token, expires - 60, TimeUnit.SECONDS);
        return token;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        INSTANCE = this;
    }
}
