package com.datatech.slgzt.helps;

import com.datatech.slgzt.exception.BusinessException;
import com.ejlchina.data.Mapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 04月09日 14:46:04
 */
@Component
@Slf4j
public class CcaeUnpackHelper {

    public static String unpackData(Mapper mapper, String errorMsg) {
        //正常会返回success true ,但是可能会是另一对象，没有data这个属性直接就是500错
        try {
            int code = mapper.getInt("retCode");
            if (code != 0) {
                log.warn("{}", mapper);
                throw new BusinessException("调用底层失败 失败内容为：" + mapper.getString("retMsg"));
            }
            String string = mapper.getString("data");
            log.info("{}", string);
            return string;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            // 如果没有data这个属性，直接返回500
            throw new BusinessException(errorMsg);
        }
    }
}
