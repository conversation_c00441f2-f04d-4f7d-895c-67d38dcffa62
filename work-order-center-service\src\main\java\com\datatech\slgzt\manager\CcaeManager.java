package com.datatech.slgzt.manager;


import com.alibaba.fastjson.JSONArray;
import com.datatech.slgzt.model.to.CcaeComputeDeviceTO;
import com.datatech.slgzt.model.to.CcaeNeDeviceTO;
import com.datatech.slgzt.model.to.CcaeNetworkDeviceTO;
import com.datatech.slgzt.model.to.CcaeTemplateTO;

import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

public interface CcaeManager {

    JSONArray listCheckItem() throws NoSuchAlgorithmException, KeyManagementException;

    List<CcaeTemplateTO> listTemplate() throws NoSuchAlgorithmException, KeyManagementException;

    CcaeTemplateTO getTemplateByResId(String resId) throws NoSuchAlgorithmException, KeyManagementException;

    List<CcaeComputeDeviceTO> listComputeDevice() throws NoSuchAlgorithmException, KeyManagementException;

    List<CcaeNeDeviceTO> listComputeNeDevices() throws Exception;

    List<CcaeNetworkDeviceTO> listNetworkDevice() throws NoSuchAlgorithmException, KeyManagementException;

    String createNetworkTask(String taskName, JSONArray checkItems, List<String> networkIpList) throws NoSuchAlgorithmException, KeyManagementException;

    String createComputeTask(String taskName, String templateName, String serverIdType, List<String> computeList) throws NoSuchAlgorithmException, KeyManagementException;
}
