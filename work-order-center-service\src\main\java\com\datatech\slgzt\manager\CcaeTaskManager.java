package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.CcaeTaskDTO;
import com.datatech.slgzt.model.query.CcaeTaskQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

/**
 * CCAE任务Manager接口
 *
 * <AUTHOR>
 * @date 2025-12-30
 */
public interface CcaeTaskManager {

    /**
     * 根据ID查询
     */
    CcaeTaskDTO getById(Long id);

    /**
     * 根据CCAE任务ID查询
     */
    CcaeTaskDTO getByCcaeId(String ccaeId);

    /**
     * 新增
     */
    void save(CcaeTaskDTO ccaeTaskDTO);

    /**
     * 更新
     */
    void updateById(CcaeTaskDTO ccaeTaskDTO);

    /**
     * 删除
     */
    void deleteById(Long id);

    /**
     * 列表查询
     */
    List<CcaeTaskDTO> list(CcaeTaskQuery query);

    /**
     * 分页查询
     */
    PageResult<CcaeTaskDTO> page(CcaeTaskQuery query);

    /**
     * 根据CCAE任务ID更新进度
     */
    void updateProgressByCcaeId(String ccaeId, Integer progress);

    /**
     * 根据CCAE任务ID更新任务状态
     */
    void updateTaskStatusByCcaeId(String ccaeId, Integer progress, Integer passItemCount, Integer checkItemCount, Integer elapsedTimeSec);
}
