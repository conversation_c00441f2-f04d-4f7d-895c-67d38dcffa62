package com.datatech.slgzt.manager;



import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.model.query.DeviceMetricQuery;

import java.util.List;

/**
 * 设备监控指标
 */
public interface DeviceCardMetricsManager {

    /**
     * 新增显卡指标
     */
    void create(DeviceCardMetricsDTO dto);


    void saveBatch(List<DeviceCardMetricsDTO> deviceCardMetricsDTOS);

    void delByGpuTime(String gpuTime);

    /**
     * 更新显卡指标
     */
    void update(DeviceCardMetricsDTO dto);

    /**
     * 删除显卡指标
     */
    void delete(Long id);

    /**
     * 批量删除显卡指标
     */
    void deleteBatch(List<Long> ids);

    /**
     * 获取gpu 指标信息
     * @param gpuTime
     * @return
     */
    List<DeviceCardMetricsDTO> selectGpuMetricsDTO(String gpuTime);



    /**
     * 获取gpu 指标信息
     * @param DeviceMetricQuery
     * @return
     */
    List<DeviceCardMetricsDTO> selectGpuMetricsDTO(DeviceMetricQuery deviceMetricQuery);

    List<DeviceCardMetricsDTO> queryAvgDeviceMetrics(DeviceMetricQuery deviceMetricQuery);

    /**
     * 按时间聚合查询GPU指标数据（数据库层面聚合，提升性能）
     * @param deviceIds 设备ID列表
     * @param deviceMetricQuery 查询条件（包含时间范围和聚合类型）
     * @return 聚合后的GPU指标数据
     */
    List<DeviceCardMetricsDTO> queryGpuMetricsAggregated(List<String> deviceIds, DeviceMetricQuery deviceMetricQuery);

    /**
     * 按区域和卡类型查询利用率聚合数据
     * @param areaCode 区域编码（可以为空）
     * @param modelName 卡类型（必须）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param aggregationType 聚合类型（day/month）
     * @return 聚合后的利用率数据
     */
    List<DeviceCardMetricsDTO> queryCardUtilizationMetrics(String areaCode, String modelName,
                                                          java.time.LocalDateTime startTime,
                                                          java.time.LocalDateTime endTime,
                                                          String aggregationType,
                                                           List<String> deviceIds);

    /**
     * 按区域和卡类型查询利用率聚合数据
     * @param areaCode 区域编码（可以为空）
     * @param modelName 卡类型（必须）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param aggregationType 聚合类型（day/month）
     * @return 聚合后的利用率数据
     */
    List<DeviceCardMetricsDTO> queryCardUtilizationMetrics2(String areaCode, String modelName,
                                                           java.time.LocalDateTime startTime,
                                                           java.time.LocalDateTime endTime,
                                                           String aggregationType);

    /**
     * 查询业务系统历史利用率数据（按时间范围聚合）
     * @param areaCode 区域编码（可选）
     * @param modelName 卡类型（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 按业务系统聚合的历史利用率数据
     */
    List<DeviceCardMetricsDTO> queryBusinessSystemHistoryMetrics(String areaCode, String modelName,
                                                                java.time.LocalDateTime startTime,
                                                                java.time.LocalDateTime endTime);

}
