package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.HashrateResInfoDTO;
import com.datatech.slgzt.model.query.HashrateResInfoQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

public interface HashrateResInfoManager {


    /**
     * 智算资源查询
     *
     * @param query 智算资源查询参数
     * @return 智算资源列表
     */
    List<HashrateResInfoDTO> list(HashrateResInfoQuery query);


    /**
     * page
     */
    PageResult<HashrateResInfoDTO> page(HashrateResInfoQuery query);


    /**
     * insert
     */
    void insert(HashrateResInfoDTO dto);

    HashrateResInfoDTO getById(Long id);

    /**
     * delByDeviceId
     */
    void delByDeviceId(String deviceId);

    void update(HashrateResInfoDTO dto);

    void delById(Long id);
}
