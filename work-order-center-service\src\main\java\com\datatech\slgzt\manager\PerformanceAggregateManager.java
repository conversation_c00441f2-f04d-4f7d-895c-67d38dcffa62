package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.CKBandwidthPerformanceDTO;
import com.datatech.slgzt.model.dto.VMResourcePerformanceDTO;
import com.datatech.slgzt.model.dto.VMResourcePerformanceStringDTO;
import com.datatech.slgzt.utils.PageResult;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 性能数据聚合Manager接口
 *
 * <AUTHOR>
 * @date 2025-12-21
 */
public interface PerformanceAggregateManager {

    /**
     * 聚合虚拟机性能数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param regionCodes 区域编码列表
     * @return 虚拟机性能数据列表
     */
    List<VMResourcePerformanceDTO> aggregateVmPerformance(LocalDateTime startTime, LocalDateTime endTime, List<String> regionCodes);

    /**
     * 聚合带宽性能数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param regionCodes 区域编码列表
     * @return 带宽性能数据列表
     */
    List<CKBandwidthPerformanceDTO> aggregateBandwidthPerformance(LocalDateTime startTime, LocalDateTime endTime, List<String> regionCodes);

    /**
     * 分页查询虚拟机性能列表
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param regionCodes 区域编码列表
     * @return 虚拟机性能数据分页列表
     */
    PageResult<VMResourcePerformanceDTO> listVmPerformancePage(Integer pageNum, Integer pageSize, LocalDateTime startTime, LocalDateTime endTime, List<String> regionCodes);

    /**
     * 聚合虚拟机性能数据总计（不分组）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param regionCodes 区域编码列表
     * @return 虚拟机性能数据总计
     */
    VMResourcePerformanceStringDTO aggregateVmPerformanceTotal(LocalDateTime startTime, LocalDateTime endTime, List<String> regionCodes);

    /**
     * 聚合带宽性能数据总计（不分组）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param regionCodes 区域编码列表
     * @return 带宽性能数据总计
     */
    CKBandwidthPerformanceDTO aggregateBandwidthPerformanceTotal(LocalDateTime startTime, LocalDateTime endTime, List<String> regionCodes);
}
