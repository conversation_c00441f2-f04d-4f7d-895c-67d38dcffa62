package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.RecoveryWorkOrderProductDTO;

import java.util.List;
import java.util.Map;

public interface RecoveryWorkOrderProductManager {

    void insert(RecoveryWorkOrderProductDTO productDTO);


    RecoveryWorkOrderProductDTO getById(Long id);

    void update(RecoveryWorkOrderProductDTO productDTO);


    List<RecoveryWorkOrderProductDTO> getByIds(List<Long> ids);

    List<RecoveryWorkOrderProductDTO> listByWorkOrderId(String workOrderId);

    void updateStatusByIds(List<Long> ids, String status);

    void updateByResourceDetailId(String resourceDetailId, String status, String message);

    void updateStatusByParentId(Long id, String status);

    RecoveryWorkOrderProductDTO getBySubOrderId(Long subOrderId);

    void updateHcmByCmdbIds(List<String> configIds, String status);

    void updateHcmByIds(List<Long> configIds, String status);

    void updateTenantConfirmByIds(List<Long> ids, Boolean tenantConfirm);

    void deleteByWorkOrderId(String workOrderId);

    RecoveryWorkOrderProductDTO getByCmdbId(String cmdbId);

    /**
     * 查询正在串行回收的产品
     */
    RecoveryWorkOrderProductDTO listSerialRecovering();

    /**
     * 查询下一个需要串行回收的产品
     */
    RecoveryWorkOrderProductDTO listNextSerialRecovery();

    /**
     * 按REGION_CODE分组查询每个资源池中最早的待回收产品
     * @return Map<String, RecoveryWorkOrderProductDTO> key为regionCode，value为该资源池中最早的待回收产品
     */
    Map<String, RecoveryWorkOrderProductDTO> listNextSerialRecoveryByRegionCode();

    /**
     * 统计正在串行回收的产品数量
     */
    int countSerialRecovering();

    /**
     * 根据父产品ID更新串行回收状态
     */
    void updateSerialRecoveryStatusByParentId(Long parentProductId, Integer serialRecoveryStatus);


    /**
     * 根据工单ID更新串行回收状态
     * @param workOrderId 工单ID
     * @param regionCode 资源池代码
     * @param oldSerialRecoveryStatus 旧的串行回收状态
     * @param newSerialRecoveryStatus 新的串行回收状态
     */
    void updateSerialRecoveryStatusByOrderId(String workOrderId, String regionCode, Integer oldSerialRecoveryStatus, Integer newSerialRecoveryStatus);
}
