package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.RegionCodeDTO;

import java.util.Collection;
import java.util.List;

public interface RegionCodeManager {
    List<RegionCodeDTO> list(String cloudCode,String regionName);

    RegionCodeDTO selectById(Long id);



    RegionCodeDTO selectByCode(String code);

    List<RegionCodeDTO> listByCodes(Collection<String> resourcePoolCodes);
}
