package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.order.StandardAuditWorkOrderDTO;
import com.datatech.slgzt.model.query.StandardWorkOrderProductQuery;
import com.datatech.slgzt.model.resourcce.ResourceShowInfoDTO;

import java.util.List;
import java.util.Map;

public interface StandardWorkOrderProductManager {

    List<StandardWorkOrderProductDTO> list(StandardWorkOrderProductQuery query);

    /**
     * 查询正在开通中的资源
     * @return
     */
    StandardWorkOrderProductDTO listOpening();

    /**
     * 查询下一个待开通的资源
     * @return
     */
    StandardWorkOrderProductDTO listNextOpen();

    /**
     * 按REGION_CODE分组查询每个资源池中最早的待开通资源
     * @return Map<String, StandardWorkOrderProductDTO> key为regionCode，value为该资源池中最早的待开通产品
     */
    Map<String, StandardWorkOrderProductDTO> listNextOpenByRegionCode();

    /**
     * update
     */
    void update(StandardWorkOrderProductDTO dto);


    StandardWorkOrderProductDTO getById(Long id);

    StandardWorkOrderProductDTO getByGid(String gid);

    StandardWorkOrderProductDTO getBySubOrderId(Long subOrderId);

    void deleteByWorkOrderId(String id);


    /**
     * insert
     */
    void insert(StandardWorkOrderProductDTO dto);

    void updateStatusById(Long id, String status);


    void updateStatusByParentId(Long id, String status);

    void fillRegionMessage(StandardAuditWorkOrderDTO auditDto);

    ResourceShowInfoDTO selectResourceOverview(StandardWorkOrderProductQuery productQuery);

    /**
     * 更新Job执行ID（仅对CQ产品有效）
     * @param subOrderId 子订单ID
     * @param jobExecutionId Job执行ID
     */
    void updateJobExecutionIdBySubOrderId(Long subOrderId, Long jobExecutionId);

    /**
     * 根据工单ID更新串行开通状态
     * @param workOrderId 工单ID
     * @param regionCode 资源池代码
     * @param oldSerialOpenStatus 旧的串行开通状态
     * @param newSerialOpenStatus 新的串行开通状态
     */
    void updateSerialOpenStatusByOrderId(String workOrderId, String regionCode, int oldSerialOpenStatus, int newSerialOpenStatus);
}
