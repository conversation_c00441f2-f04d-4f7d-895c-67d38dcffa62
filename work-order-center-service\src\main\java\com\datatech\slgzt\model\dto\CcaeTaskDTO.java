package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * CCAE任务DTO
 * 
 * <AUTHOR>
 * @date 2025-12-30
 */
@Data
public class CcaeTaskDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * CCAE任务ID
     */
    private String ccaeId;

    /**
     * 通过检查项数量
     */
    private Integer passItemCount;

    /**
     * 检查项总数量
     */
    private Integer checkItemCount;

    /**
     * 检查范围(专业组)：主机；网络；存储
     */
    private String deviceScope;

    /**
     * 模板分类：Deep-深度健康评估；Basic-快速健康评估；Job-作业运行前检查
     */
    private String templateCategory;

    /**
     * 任务执行方式：0-立即执行；1-每日；7-每周；30-每月
     */
    private Integer executeMethod;

    /**
     * 耗时（秒）
     */
    private Integer elapsedTimeSec;

    /**
     * 进度，-1表示停止，0-99表示进度，100表示完成
     */
    private Integer progress;

    /**
     * CCAE任务创建时间
     */
    private LocalDateTime ccaeCreateTime;

    /**
     * CCAE任务结束时间
     */
    private LocalDateTime ccaeEndTime;

    /**
     * 任务停止时间
     */
    private LocalDateTime stopTime;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 计算节点列表(SN序列号)
     */
    private List<String> computeList;

    /**
     * 网络节点列表(IP列表)
     */
    private List<String> networkIpList;

    /**
     * 状态：1-未删除，0-已删除
     */
    private Integer status;
}
