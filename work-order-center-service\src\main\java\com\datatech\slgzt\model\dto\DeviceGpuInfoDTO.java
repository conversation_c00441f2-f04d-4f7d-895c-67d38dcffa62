package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.io.Serializable;


@Data
public class DeviceGpuInfoDTO implements Serializable {
    private Long id;

//====================基本信息=======================

    private String areaCode;
    /**
     * 云类型名称
     */
    private String catalogueDomainName;

    /**
     * 云类型编码
     */
    private String catalogueDomainCode;

    /**
     * 云平台名称
     */
    private String domainName;

    /**
     * 云平台编码
     */
    private String domainCode;

    /**
     * 资源池id
     * 当工单是线下开通的时候，资源池id是空的
     */
    private Long regionId;

    /**
     * 资源池Code
     */
    private String regionCode;

    /**
     * 资源池名称
     */
    private String regionName;


    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

//====================设备基本信息=======================
    /**
     * GPU/NPU卡序列号/uuid
     */
    private String deviceId;

    /**
     * GPU/NPU卡型号
     */
    private String deviceType;

    /**
     * 显卡索引
     */

    private Integer deviceIndex;

    /**
     * 显卡名称
     */
    private String deviceName;

    /**
     * 显卡型号
     */
    private String modelName;


    /**
     * 指定时间的指标
     */
    private String  gpuTime;




    /**
     * 显卡驱动版本
     */
    private String driverVersion;

    /**
     * 显卡节点IP
     */
    private String deviceIp;



    /**
     *  DCN地址
     */
    private String dcnNetAddr;

    /**
     *  虚机/物理机私网地址
     */
    private String privateNetAddr;

    /**
     * 设备分配状态
     */
    private String inUsed;



    /**
     * 设备显存大小
     */
    private Integer memory;


    /**
     * 算力利用率
     */
    private Double gpuUtilPercent;


    //来源类型
    private String sourceType;

    /**
     * 显存利用率
     */
    private Double memUtilpercent;

    /**
     * 显存大小 显存大小（GB）
     */
    private Integer memoryUsage;

    /**
     * 算力能耗top
     */
    private Double devPowerUsage;

    /**
     * 任务数
     */
    private Integer allocationCount;

    /**
     * 温度
     */
    private Double devGpuTemp;

    /**
     * 虚拟卡index
     */
    private Integer deviceVirtualIndex;

    /**
     * 虚拟卡所属物理卡id
     */
    private String physicalDeviceId;

    private String vmId;

    //最新的指标数据 json
    private String lastPeriod;

    private String sliceStatus;

    private String deptName;

    private String subModelName;

    //采集状态
    private String collectStatus;

    //运行状态
    private String runStatus;

    private String vmName;

    private String applyUserName;


    private String inManage;

    private Integer indexT;

    private String vmType;

    private String vmStatus;

    private String vmCpu;

    private String vmMemory;

    private String vmDisk;

    private String osName;

    private String applicant;

    private String productGategory;


    private String applicationTime;

    private String expirationTime;


}
