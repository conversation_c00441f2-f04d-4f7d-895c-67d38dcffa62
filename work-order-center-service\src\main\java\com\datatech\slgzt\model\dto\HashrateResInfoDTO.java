package com.datatech.slgzt.model.dto;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.Precondition;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 智算资源数据传输对象
 * 用于业务层之间的数据传输
 * 
 * <AUTHOR>
 * @date 2025-12-20
 */
@Data
public class HashrateResInfoDTO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 所属云编码
     */
    private String domainCode;
    
    /**
     * 所属云名称
     */
    @ExcelExportHeader(value = "所属云")
    private String domainName;
    
    /**
     * 资源池名称
     */
    @ExcelExportHeader(value = "资源池")
    private String regionName;
    

    
    /**
     * 业务系统
     */
    @ExcelExportHeader(value = "业务系统")
    private String businessName;
    
    /**
     * 私网地址
     */
    @ExcelExportHeader(value = "私网地址")
    private String privateNetworkAddress;
    
    /**
     * DCN地址
     */
    @ExcelExportHeader(value = "DCN地址")
    private String dcnAddress;


    /**
     * 资源池ID
     */
    private String regionId;

    /**
     * 资源ID
     */
    @ExcelExportHeader(value = "资源ID")
    private String deviceId;
    
    /**
     * 设备名称
     */
    @ExcelExportHeader(value = "设备名称")
    private String deviceName;
    
    /**
     * 设备类型
     */
    @ExcelExportHeader(value = "设备类型")
    private String deviceType;
    
    /**
     * 设备状态
     */
    @ExcelExportHeader(value = "设备状态")
    private String deviceStatus;
    
    /**
     * CPU
     */
    @ExcelExportHeader(value = "CPU(核)")
    private String cpu;
    
    /**
     * 内存
     */
    @ExcelExportHeader(value = "内存(GB)")
    private String memory;
    
    /**
     * 硬盘
     */
    @ExcelExportHeader(value = "硬盘(GB)")
    private String disk;
    
    /**
     * 操作系统
     */
    @ExcelExportHeader(value = "操作系统")
    private String osName;


    /**
     * 索引T
     */
    @ExcelExportHeader(value = "卡序号")
    private Integer indexT;
    
    /**
     * 型号
     */
    @ExcelExportHeader(value = "型号")
    private String model;
    
    /**
     * 分配状态
     */
    @ExcelExportHeader(value = "分配状态")
    private String allocationStatus;
    
    /**
     * 部门信息
     */
    @ExcelExportHeader(value = "部门信息")
    private String deptName;
    
    /**
     * 申请人
     */
    @ExcelExportHeader(value = "申请人")
    private String applicant;
    
    /**
     * 产品类别
     */
    @ExcelExportHeader(value = "产品类别")
    private String productCategory;
    
    /**
     * 申请时间
     */
    @ExcelExportHeader(value = "申请时间")
    private LocalDateTime applicationTime;
    
    /**
     * 到期时间
     */
    @ExcelExportHeader(value = "到期时间")
    private LocalDateTime expirationTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private String areaCode;




    /**
     * 数据验证方法
     * 验证必填字段、格式等
     */
    public void validate() {
        // 必填字段验证
        Precondition.checkArgument(ObjNullUtils.isNotNull(deviceId), "资源ID不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(domainName), "所属云不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(regionName), "资源池不能为空");

        // IP地址格式验证
        if (ObjNullUtils.isNotNull(privateNetworkAddress)) {
            Precondition.checkArgument(isValidIp(privateNetworkAddress), "私网地址格式不正确");
        }

        if (ObjNullUtils.isNotNull(dcnAddress)) {
            Precondition.checkArgument(isValidIp(dcnAddress), "DCN地址格式不正确");
        }

        // 字段长度验证
        if (ObjNullUtils.isNotNull(deviceName)) {
            Precondition.checkArgument(deviceName.length() <= 100, "设备名称长度不能超过100字符");
        }

        if (ObjNullUtils.isNotNull(deviceId)) {
            Precondition.checkArgument(deviceId.length() <= 50, "资源ID长度不能超过50字符");
        }
    }

    /**
     * IP地址格式验证
     */
    private boolean isValidIp(String ip) {
        if (ObjNullUtils.isNull(ip)) {
            return false;
        }
        return ip.matches("^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");
    }

}