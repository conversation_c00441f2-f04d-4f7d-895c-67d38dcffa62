package com.datatech.slgzt.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 虚拟机性能数据表(String类型，防止NAN转换错误问题)
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class VMResourcePerformanceStringDTO {

    /**
     * CK中云主机cpu利用率
     */
    private String ckCpuUtil;

    /**
     * CK中云主机磁盘读IOPS
     */
    private String ckDiskReadIops;

    /**
     * CK中云主机磁盘写IOPS
     */
    private String ckDiskWriteIops;

    /**
     * CK中云主机内存利用率
     */
    private String ckMemUtil;
} 