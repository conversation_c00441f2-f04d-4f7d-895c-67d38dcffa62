package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * CCAE任务查询对象
 * 
 * <AUTHOR>
 * @date 2025-12-30
 */
@Data
@Accessors(chain = true)
public class CcaeTaskQuery {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 主键ID列表
     */
    private List<Long> ids;

    /**
     * CCAE任务ID
     */
    private String ccaeId;

    /**
     * CCAE任务ID列表
     */
    private List<String> ccaeIds;

    /**
     * 检查范围(专业组)：主机；网络；存储
     */
    private String deviceScope;

    /**
     * 检查范围列表
     */
    private List<String> deviceScopes;

    /**
     * 模板分类：Deep-深度健康评估；Basic-快速健康评估；Job-作业运行前检查
     */
    private String templateCategory;

    /**
     * 模板分类列表
     */
    private List<String> templateCategories;

    /**
     * 任务执行方式：0-立即执行；1-每日；7-每周；30-每月
     */
    private Integer executeMethod;

    /**
     * 任务执行方式列表
     */
    private List<Integer> executeMethods;

    /**
     * 进度，-1表示停止，0-99表示进度，100表示完成
     */
    private Integer progress;

    /**
     * 进度列表
     */
    private List<Integer> progressList;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人ID列表
     */
    private List<Long> creatorIds;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;

    /**
     * CCAE任务创建时间开始
     */
    private LocalDateTime ccaeCreateTimeStart;

    /**
     * CCAE任务创建时间结束
     */
    private LocalDateTime ccaeCreateTimeEnd;

    /**
     * CCAE任务结束时间开始
     */
    private LocalDateTime ccaeEndTimeStart;

    /**
     * CCAE任务结束时间结束
     */
    private LocalDateTime ccaeEndTimeEnd;

    /**
     * 分页页码
     */
    private Integer pageNum;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 计算节点SN序列号（用于查询包含指定SN的任务）
     */
    private String computeSn;

    /**
     * 网络IP地址（用于查询包含指定IP的任务）
     */
    private String networkIp;
}
