package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 智算资源查询参数
 *
 * <AUTHOR>
 * @date 2025年 12月20日
 */
@Data
@Accessors(chain = true)
public class HashrateResInfoQuery {
    
    private Integer pageNum = 1;
    
    private Integer pageSize = 10;
    
    /**
     * 所属云代码
     */
    private String domainCode;

    private List<String> areaCodes;

    private String areaCode;

    private List<String> areaCodeList;


    private List<String> modelNames;

    private String domainName;

    private String modelName;

    private List<String> deptNameList;


    /**
     * 资源池名称
     */
    private String regionName;

    private List<String> regionNames;
    
    /**
     * 业务系统名称
     */
    private String businessName;
    
    /**
     * 资源ID
     */
    private String deviceId;

    private Integer indexT;
    
    /**
     * 设备名称
     */
    private String deviceName;
    
    /**
     * 设备状态
     */
    private String deviceStatus;

    private List<String> deviceStatusList;
    
    /**
     * 分配状态
     */
    private String allocationStatus;


    private List<String> allocationStatusList;

    /**
     * 申请人
     */
    private String applicant;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
}