package com.datatech.slgzt.model.report;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * GPU云主机资源报表导出实体
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
public class VmCountReportExcelDTO {

    //序号
    @ExcelProperty("序号")
    private String serialNumber;

    //使用方
    @ExcelProperty("使用方")
    private String deptName;

    //台数汇总
    @ExcelProperty("台数汇总")
    private Integer vmCount;

    //类型
    @ExcelProperty("类型")
    private String vmModelType;

    //910B2
    @ExcelProperty("910B2")
    private String model910B2Count;

    //910B4
    @ExcelProperty("910B4")
    private String model910B4Count;

    //910C
    @ExcelProperty("910C")
    private String model910CCount;


} 