package com.datatech.slgzt.model.to;

import lombok.Data;

@Data
public class CcaeNeDeviceTO {
    // {
    //     "resId": "f543d580-9577-3810-9b28-aa0d1ebe38fc",
    //     "name": "AIC-HZ-CYY-01B-1104-A02-S-GSRV-01",
    //     "parentResId": "3",
    //     "productName": "Atlas 900 A3 SuperPoD Compute Node",
    //     "sn": "2102315MUG10R7100041",
    //     "ipAddress": "**************",
    //     "adminStatus": "Online",
    //     "manufacturer": "Huawei",
    //     "macAddress": "6C-6C-0F-B6-95-00",
    //     "clusterId": "98ae0f6d-662f-4b1f-8466-6efeceb1972c",
    //     "l1SwitchIds": [
    //         "900d93fc-70d8-3f41-88e7-339c4f8868ad"
    //     ]
    // }
    private String resId;
    private String name;
    private String parentResId;
    private String productName;
    private String sn;
    private String ipAddress;
    private String adminStatus;
    private String manufacturer;
    private String macAddress;
    private String clusterId;
    private String[] l1SwitchIds;
}
