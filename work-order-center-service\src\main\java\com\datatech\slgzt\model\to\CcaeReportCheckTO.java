package com.datatech.slgzt.model.to;

import lombok.Data;

import java.util.List;

/**
 * 不通过紧急项列表
 */
@Data
public class CcaeReportCheckTO {
    /**
     * 所有 紧急项/风险项 总的检查结果，取值范围：“Pass”、“Failed”或“Abnormal”
     */
    private String result;
    /**
     * 紧急项/风险项 通过数
     */
    private Integer passItemsCnt;
    /**
     * 通过 紧急项/风险项 列表
     */
    private List<CcaeReportPassItemTO> passItems;
    /**
     * 紧急项/风险项 不通过数
     */
    private Integer failItemsCnt;
    /**
     * 不通过 紧急项/风险项 列表
     */
    private List<CcaeReportFailItemTO> failItems;
}
