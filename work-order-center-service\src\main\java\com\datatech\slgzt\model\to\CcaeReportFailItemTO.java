package com.datatech.slgzt.model.to;

import lombok.Data;

import java.util.List;

/**
 * 不通过紧急项列表
 */
@Data
public class CcaeReportFailItemTO {
    //  {
    //      "checkItem": "NetCardStatus",
    //      "checkResult": "Failed",
    //      "details": "/api/jobmaintenance/v1/healthcheck/job/precheck/fe442c49-8760-4225-92ce-def3dd645ec9/itemsdetail/NetCardStatus",
    //      "failDevices": [
    //          {
    //              "serverId": "2102315MUG10R7100019",
    //              "device": [
    //                  "0",
    //                  "1",
    //                  "2",
    //                  "3",
    //                  "4",
    //                  "5",
    //                  "6",
    //                  "7"
    //              ]
    //          },
    //          {
    //              "serverId": "2102315MUG10R7100013",
    //              "device": [
    //                  "0",
    //                  "1",
    //                  "2",
    //                  "3",
    //                  "4",
    //                  "5",
    //                  "6",
    //                  "7"
    //              ]
    //          }
    //      ]
    //  }
    /**
     * 紧急项/风险项 名称
     */
    private String checkItem;
    /**
     * 检查项结果 "Failed"，或者"Abnormal"
     */
    private String checkResult;
    /**
     * 失败紧急项详情查询接口URI或者“Abnormal”
     */
    private String details;
    /**
     * 不通过紧急项的节点信息
     */
    private List<CcaeReportFailItemDeviceTO> failDevices;
}
