package com.datatech.slgzt.model.to;

import lombok.Data;

import java.util.List;

/**
 * 不通过紧急项列表
 */
@Data
public class CcaeReportPassItemTO {
    // {
    //     "checkItem": "ComputeIssueCheck",
    //     "checkResult": "Pass",
    //     "details": ""
    // }
    /**
     * 紧急项名称
     */
    private String checkItem;
    /**
     * 通过项为 "Pass"，跳过项为 "SKIP"
     */
    private String checkResult;
    /**
     * 当checkResult为"Pass"时，为空；为"SKIP"时，为不涉及原因及处理建议。
     */
    private String details;
}
