package com.datatech.slgzt.model.to;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class CcaeTemplateItemTO {
    private String name;
    private String field;
    private String level;
    private String category;
    private String type;
    private Boolean configurable;
    @JSONField(name = "name_i18n")
    private CcaeNameI18nTO nameI18n;

    private Object BIOSConfig;
    private Object backLogConfig;
    private Object clockConfig;
    private Object consumptionConfig;
    private Object hbmStressTestConfig;
    private Object HCCLPerformanceConfig;
    private Object processConfig;
    private Object kernelConfig;
    private Object linkDisConfig;
    private Object lingquVersionConfig;
    private Object memConfig;
    private Object userConfig;
    private Object osEnvConfig;
    private Object serviceConfig;
    private Object containerConfig;
    private Object ulimitEnvConfig;
}
