package com.datatech.slgzt.service;

import com.datatech.slgzt.model.dto.OperationalReportDTO;
import com.datatech.slgzt.model.opm.OperationalReportLogCreateOpm;
import com.datatech.slgzt.model.query.OperationalReportQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

/**
 * 运维报表Service接口
 *
 * <AUTHOR>
 * @date 2025-12-20
 */
public interface OperationalReportService {

    /**
     * 创建运维报表
     *
     * @param dto 运维报表DTO
     * @return 主键ID
     */
    Long createOperationalReport(OperationalReportDTO dto);

    /**
     * 更新运维报表
     *
     * @param dto 运维报表DTO
     */
    void updateOperationalReport(OperationalReportDTO dto);

    /**
     * 根据ID删除运维报表
     *
     * @param id 主键ID
     */
    void deleteOperationalReportById(Long id);

    /**
     * 根据ID查询运维报表
     *
     * @param id 主键ID
     * @return 运维报表DTO
     */
    OperationalReportDTO getOperationalReportById(Long id);

    /**
     * 分页查询运维报表列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageResult<OperationalReportDTO> pageOperationalReports(OperationalReportQuery query);

    /**
     * 查询运维报表列表
     *
     * @param query 查询条件
     * @return 运维报表列表
     */
    List<OperationalReportDTO> listOperationalReports(OperationalReportQuery query);

    /**
     * 根据用户ID查询运维报表列表
     *
     * @param userId 用户ID
     * @return 运维报表列表
     */
    List<OperationalReportDTO> listOperationalReportsByUserId(Long userId);

    /**
     * 根据运维视图ID查询运维报表列表
     *
     * @param operationalViewId 运维视图ID
     * @return 运维报表列表
     */
    List<OperationalReportDTO> listOperationalReportsByViewId(Long operationalViewId);

    /**
     * 启用运维报表
     *
     * @param id 主键ID
     */
    void enableOperationalReport(Long id);

    /**
     * 禁用运维报表
     *
     * @param id 主键ID
     */
    void disableOperationalReport(Long id);

    /**
     * 手动生成运维报表
     */
    void generateOperationalReportManually(OperationalReportLogCreateOpm opm);

    void autoGenerateOperationalReport();

    /**
     * 批量删除运维报表
     *
     * @param ids 主键ID列表
     */
    void batchDeleteOperationalReports(List<Long> ids);

    String getTaTaiToken();

    String getTaTaiUrlWithToken();
}
