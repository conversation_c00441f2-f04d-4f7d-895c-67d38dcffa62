package com.datatech.slgzt.service.ccae;

import com.datatech.slgzt.model.dto.CcaeTaskDTO;
import com.datatech.slgzt.model.query.CcaeTaskQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

/**
 * CCAE任务Service接口
 * 
 * <AUTHOR>
 * @date 2025-12-30
 */
public interface CcaeTaskService {

    /**
     * 根据ID查询
     */
    CcaeTaskDTO getById(Long id);

    /**
     * 根据CCAE任务ID查询
     */
    CcaeTaskDTO getByCcaeId(String ccaeId);

    /**
     * 创建CCAE任务
     */
    void createTask(CcaeTaskDTO ccaeTaskDTO);

    /**
     * 更新CCAE任务
     */
    void updateTask(CcaeTaskDTO ccaeTaskDTO);

    /**
     * 删除CCAE任务
     */
    void deleteTask(Long id);

    /**
     * 列表查询
     */
    List<CcaeTaskDTO> listTasks(CcaeTaskQuery query);

    /**
     * 分页查询
     */
    PageResult<CcaeTaskDTO> pageTasks(CcaeTaskQuery query);

    /**
     * 启动CCAE任务
     */
    void startTask(String ccaeId);

    /**
     * 停止CCAE任务
     */
    void stopTask(String ccaeId);

    /**
     * 更新任务进度
     */
    void updateTaskProgress(String ccaeId, Integer progress);

    /**
     * 完成CCAE任务
     */
    void completeTask(String ccaeId, Integer passItemCount, Integer checkItemCount, Integer elapsedTimeSec);

    /**
     * 获取任务执行统计信息
     */
    CcaeTaskDTO getTaskStatistics(String ccaeId);
}
