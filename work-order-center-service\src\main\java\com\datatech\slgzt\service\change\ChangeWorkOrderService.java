package com.datatech.slgzt.service.change;

import com.datatech.slgzt.model.bpmn.ActivityTaskVo;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.WorkOrderAuthLogDTO;
import com.datatech.slgzt.model.dto.change.ChangeAuditWorkOrderDTO;
import com.datatech.slgzt.model.dto.change.ChangeWorkOrderDTO;
import com.datatech.slgzt.model.home.AuditCountVo;
import com.datatech.slgzt.model.opm.ChangeWorkOrderCreateOpm;
import com.datatech.slgzt.model.query.ChangeWorkOrderQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月31日 15:32:47
 */
public interface ChangeWorkOrderService {

    String createChangeWorkOrder(ChangeWorkOrderCreateOpm opm);

    PageResult<ChangeWorkOrderDTO> page(ChangeWorkOrderQuery orderQuery, Long userId);

    AuditCountVo orderCount(ChangeWorkOrderQuery query);

    void cancel(String workOrderId);

    void audit(ChangeAuditWorkOrderDTO auditDTO, Long userId);

    String draft(ChangeWorkOrderCreateOpm opm);

    /**
     * 检查 变更流程 是否需要 发短信，如果检查通过，那么发短信
     * <p>1.ActivitiStatusEnum.RESOURCE_CHANGE
     * <p>2.所有的产品变更已完成
     * <p>3.ecs，gcs已经开机
     *
     * @param resourceDetailId 资源详情id
     */
    void checkAndSendSMS(Long resourceDetailId);

    ChangeWorkOrderDTO getDraft(Long userId, String orderId);

    void checkDraftChangeOrderCanCreate(ChangeWorkOrderCreateOpm opm);
    void checkChangeOrderCanCreate(ChangeWorkOrderCreateOpm opm);

    ActivityTaskVo getTaskNodes(String orderId);

    void getNextTaskNodes(String orderId, List<WorkOrderAuthLogDTO> authLogDTOS, String currentTaskName);

    void alarmSuppress(List<Long> productIds);

    void tenantConfirm(List<Long> productIds);

    ChangeAuditWorkOrderDTO getSchemaInfo(String workOrderId);

    void tryStartEcs(ResourceDetailDTO detailDTO);
}
