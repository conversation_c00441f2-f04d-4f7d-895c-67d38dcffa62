package com.datatech.slgzt.service.nostander;

import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.bpmn.ActivityTaskTreeVo;
import com.datatech.slgzt.model.bpmn.TaskTreeNodeDTO;
import com.datatech.slgzt.model.dto.NonStanderWorkOrderDTO;
import com.datatech.slgzt.model.home.AuditCountVo;
import com.datatech.slgzt.model.dto.WorkOrderAuthLogDTO;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.opm.FlavorCreateOpm;
import com.datatech.slgzt.model.query.NonStanderWorkOrderQuery;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.dto.nonstandard.NonStanderAuditWorkOrderDTO;
import com.datatech.slgzt.service.OrderCommonService;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

/**
 * 非标订工单服务
 */
public interface NonStanderWorkOrderService extends OrderCommonService {

    /**
     * 根据process key获取tree节点
     * @return tree节点
     */
    TaskTreeNodeDTO selectTreeNodeByKey();

    /**
     * 创建非标订工单并且开始流程
     * @param dto
     * @param requestPath
     * @param userDTO
     */
    void createNonStandardOrderAndStartProcess(NonStanderWorkOrderDTO dto, String requestPath, UserCenterUserDTO userDTO);

    void audit(NonStanderAuditWorkOrderDTO dto, Long userId);

    void checkFillEscResource(EcsModel model, String workOrderId);

    ActivityTaskTreeVo getTaskNodesTree(String orderId);

    void getNextTaskNodes(String orderId, List<WorkOrderAuthLogDTO> authLogDTOS, String currentTaskName);

    void processLogRoleNames(List<WorkOrderAuthLogDTO> authLogDTOS);

    void checkFillEscResourceOffline(EcsModel model, String workOrderId);

    void checkFillMysqlResource(MysqlModel model, String workOrderId);

    void checkFillRedisResource(RedisModel model, String workOrderId);

    void checkFillBmsResource(BareMetalModel model, String workOrderId);
    void checkFillGmsResource(BareMetalModel model, String workOrderId);

    void checkFillPostgreSqlResource(PostgreSqlModel model, String workOrderId);


    PageResult<NonStanderWorkOrderDTO> page(NonStanderWorkOrderQuery query, Long userId);

    void cancel(String workOrderId);

    AuditCountVo orderCount(NonStanderWorkOrderQuery query);

    CommonResult<String> createFlavor(FlavorCreateOpm opm);
}
