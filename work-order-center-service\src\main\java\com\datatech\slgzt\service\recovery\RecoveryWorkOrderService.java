package com.datatech.slgzt.service.recovery;

import com.datatech.slgzt.model.bpmn.ActivityTaskVo;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderDTO;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.WorkOrderAuthLogDTO;
import com.datatech.slgzt.model.dto.order.RecoveryAuditWorkOrderDTO;
import com.datatech.slgzt.model.dto.recovery.RecoveryWorkOrderDetailDTO;
import com.datatech.slgzt.model.home.AuditCountVo;
import com.datatech.slgzt.model.opm.RecoveryWorkOrderCreateOpm;
import com.datatech.slgzt.model.query.RecoveryWorkOrderQuery;
import com.datatech.slgzt.model.vo.network.NetworkOrderResult;
import com.datatech.slgzt.model.vo.vpc.VpcOrderResult;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

public interface RecoveryWorkOrderService {


    /**
     * createRecoveryWorkOrder
     */
    String createRecoveryWorkOrder(RecoveryWorkOrderCreateOpm opm);

    void cancel(String workOrderId);

    void audit(RecoveryAuditWorkOrderDTO auditDTO, Long userId);

    PageResult<RecoveryWorkOrderDTO> page(RecoveryWorkOrderQuery query, Long currentUserId);

    /**
     * 填充创建工单的校验信息
     * 主要是设备和网络直接的校验
     *
     * @param opm
     */
    void fillCheckCreate(RecoveryWorkOrderCreateOpm opm);

    AuditCountVo orderCount(RecoveryWorkOrderQuery orderQuery);

    ActivityTaskVo getTaskNodes(String orderId);

    void getNextTaskNodes(String orderId, List<WorkOrderAuthLogDTO> authLogDTOS, String currentTaskName);

    void selectResourceDetails(String workOrderId, RecoveryWorkOrderDetailDTO detailDTO);

    List<ResourceDetailDTO> fillResourceDetailIds(List<RecoveryWorkOrderProductDTO> products);

    List<VpcOrderResult> processVpc(List<RecoveryWorkOrderProductDTO> products);

    List<NetworkOrderResult> processNetwork(List<RecoveryWorkOrderProductDTO> products);
}
