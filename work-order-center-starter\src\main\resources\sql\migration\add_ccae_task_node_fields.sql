-- 为WOC_CCAE_TASK表添加计算节点和网络节点字段
-- 执行时间: 2025-12-30
-- 作者: system

-- 添加计算节点列表字段
ALTER TABLE SLGZT.WOC_CCAE_TASK ADD COMPUTE_LIST CLOB NULL;

-- 添加网络节点列表字段
ALTER TABLE SLGZT.WOC_CCAE_TASK ADD NETWORK_IP_LIST CLOB NULL;

-- 添加状态字段（逻辑删除）
ALTER TABLE SLGZT.WOC_CCAE_TASK ADD STATUS NUMBER(1,0) DEFAULT 1 NOT NULL;

-- 添加字段注释
COMMENT ON COLUMN SLGZT.WOC_CCAE_TASK.COMPUTE_LIST IS '计算节点列表(SN序列号)，JSON格式存储';
COMMENT ON COLUMN SLGZT.WOC_CCAE_TASK.NETWORK_IP_LIST IS '网络节点列表(IP列表)，JSON格式存储';
COMMENT ON COLUMN SLGZT.WOC_CCAE_TASK.STATUS IS '状态：1-未删除，0-已删除';

-- 验证字段添加成功
SELECT COLUMN_NAME, DATA_TYPE, NULLABLE, COMMENTS
FROM USER_COL_COMMENTS
WHERE TABLE_NAME = 'WOC_CCAE_TASK'
AND COLUMN_NAME IN ('COMPUTE_LIST', 'NETWORK_IP_LIST', 'STATUS');

-- 为现有数据设置默认状态（如果有现有数据的话）
UPDATE SLGZT.WOC_CCAE_TASK SET STATUS = 1 WHERE STATUS IS NULL;
