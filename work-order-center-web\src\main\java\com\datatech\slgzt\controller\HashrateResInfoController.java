package com.datatech.slgzt.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.convert.HashrateResInfoWebConvert;
import com.datatech.slgzt.manager.DeviceGpuInfoManager;
import com.datatech.slgzt.manager.HashrateResInfoManager;
import com.datatech.slgzt.manager.RegionCodeManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.dto.HashrateResInfoDTO;
import com.datatech.slgzt.model.query.HashrateResInfoQuery;
import com.datatech.slgzt.model.req.HashrateResInfoImportReq;
import com.datatech.slgzt.model.vo.device.DeviceGpuInfoExportVO;
import com.datatech.slgzt.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 12月20日 11:16:47
 */
@Slf4j
@RestController
@RequestMapping("/hashrateResInfo")
public class HashrateResInfoController {


    @Resource
    private HashrateResInfoManager hashrateResInfoManager;

    @Resource
    private DeviceGpuInfoManager deviceGpuInfoManager;

    @Resource
    private RegionCodeManager regionCodeManager;

    @Resource
    private HashrateResInfoWebConvert hashrateResInfoWebConvert;


    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/importHashrateResInfo")
    public CommonResult<Void> importHashrateResInfo(@RequestBody HashrateResInfoImportReq req) {
        List<HashrateResInfoDTO> hashrateResInfoDTOList = req.getHashrateResInfoDTOList();
        Precondition.checkArgument(hashrateResInfoDTOList != null, "<UNK> 智算资源数据传输对象列表不能为空");
        Map<String, String> areaMap = getyAreaMap();
        //导入数据
        hashrateResInfoDTOList.forEach(hashrateResInfoDTO -> {
            //如果deviceId 是斜杠或者空直接charu 不检查物理卡表
            if ("/".equals(hashrateResInfoDTO.getDeviceId())||
                    ObjNullUtils.isNull(hashrateResInfoDTO.getDeviceId())||
                    ObjNullUtils.isNull(hashrateResInfoDTO.getIndexT())) {
                hashrateResInfoManager.insert(hashrateResInfoDTO);
                return;
            }
            //想要查询下当前表里是否存在数据 以资源ID+indexT作为唯一标识
            HashrateResInfoQuery query = new HashrateResInfoQuery();
            query.setDeviceId(hashrateResInfoDTO.getDeviceId());
            query.setIndexT(hashrateResInfoDTO.getIndexT());
            HashrateResInfoDTO dbDTO = StreamUtils.findAny(hashrateResInfoManager.list(query));
            if (ObjNullUtils.isNull(dbDTO)) {
                hashrateResInfoManager.insert(hashrateResInfoDTO);
                if ("/".equals(hashrateResInfoDTO.getDeviceId())||ObjNullUtils.isNull(hashrateResInfoDTO.getDeviceId())||ObjNullUtils.isNull(hashrateResInfoDTO.getIndexT())) {
                    return;
                }
                //新增以后 也要检查物理卡表是否存在
                DeviceGpuInfoDTO deviceGpuInfoDTO = deviceGpuInfoManager.getbyVmIdAndIndexT(hashrateResInfoDTO.getDeviceId(), hashrateResInfoDTO.getIndexT());
                if (ObjNullUtils.isNull(deviceGpuInfoDTO)) {
                    //如果不存在 则新增
                    DeviceGpuInfoDTO insertDto = new DeviceGpuInfoDTO();
                    insertDto.setCatalogueDomainName(hashrateResInfoDTO.getDomainName());
                    insertDto.setRegionName(hashrateResInfoDTO.getRegionName());
                    insertDto.setBusinessSystemName(hashrateResInfoDTO.getBusinessName());
                    insertDto.setDeviceType(convertDeviceType(hashrateResInfoDTO.getModel()));
                    insertDto.setModelName(hashrateResInfoDTO.getModel());
                    insertDto.setSubModelName(hashrateResInfoDTO.getModel());
                    insertDto.setDcnNetAddr(hashrateResInfoDTO.getDcnAddress());
                    insertDto.setPrivateNetAddr(hashrateResInfoDTO.getPrivateNetworkAddress());
                    insertDto.setInUsed(convertInUsed(hashrateResInfoDTO.getAllocationStatus()));
                    //areaCode 需要通过资源池名称去查询
                    insertDto.setAreaCode(areaMap.get(hashrateResInfoDTO.getRegionName()));
                    insertDto.setVmId(hashrateResInfoDTO.getDeviceId());
                    insertDto.setIndexT(hashrateResInfoDTO.getIndexT());
                    //内存可能要根据ka来翻译;
                    insertDto.setMemory(0);
                    insertDto.setSourceType(convertSourceType(hashrateResInfoDTO.getModel()));
                    insertDto.setSliceStatus("0");
                    insertDto.setDeptName(hashrateResInfoDTO.getDeptName());
                    insertDto.setVmName(hashrateResInfoDTO.getDeviceName());
                    insertDto.setInManage("0");
                    insertDto.setVmType(insertDto.getVmType());
                    insertDto.setVmCpu(hashrateResInfoDTO.getCpu());
                    insertDto.setVmMemory(hashrateResInfoDTO.getMemory());
                    insertDto.setVmDisk(hashrateResInfoDTO.getDisk());
                    insertDto.setOsName(hashrateResInfoDTO.getOsName());
                    insertDto.setApplicant(hashrateResInfoDTO.getApplicant());
                    insertDto.setProductGategory(hashrateResInfoDTO.getProductCategory());
                    insertDto.setApplicationTime(ObjNullUtils.isNull(hashrateResInfoDTO.getApplicationTime()) ? null : DateUtils.toString(hashrateResInfoDTO.getApplicationTime()));
                    insertDto.setExpirationTime(ObjNullUtils.isNull(hashrateResInfoDTO.getExpirationTime()) ? null : DateUtils.toString(hashrateResInfoDTO.getExpirationTime()));
                    insertDto.setLastPeriod("{}");
                    insertDto.setRunStatus("离线");
                    insertDto.setCollectStatus("异常");
                    deviceGpuInfoManager.create(insertDto);
                }
            } else {
                hashrateResInfoDTO.setId(dbDTO.getId());
                //更新啊
                hashrateResInfoManager.update(hashrateResInfoDTO);
                if ("/".equals(hashrateResInfoDTO.getDeviceId())||ObjNullUtils.isNull(hashrateResInfoDTO.getDeviceId())) {
                    return;
                }
                //更新物理卡表
                DeviceGpuInfoDTO deviceGpuInfoDTO = deviceGpuInfoManager.getbyVmIdAndIndexT(hashrateResInfoDTO.getDeviceId(), hashrateResInfoDTO.getIndexT());
                if (ObjNullUtils.isNull(deviceGpuInfoDTO)) {
                    log.info("更新物理卡表失败, 物理卡不存在, deviceId: {}, indexT: {}", hashrateResInfoDTO.getDeviceId(), hashrateResInfoDTO.getIndexT());
                    return;
                }
                deviceGpuInfoDTO.setCatalogueDomainName(hashrateResInfoDTO.getDomainName());
                deviceGpuInfoDTO.setRegionName(hashrateResInfoDTO.getRegionName());
                deviceGpuInfoDTO.setBusinessSystemName(hashrateResInfoDTO.getBusinessName());
                deviceGpuInfoDTO.setDcnNetAddr(hashrateResInfoDTO.getDcnAddress());
                deviceGpuInfoDTO.setPrivateNetAddr(hashrateResInfoDTO.getPrivateNetworkAddress());
                deviceGpuInfoDTO.setInUsed(convertInUsed(hashrateResInfoDTO.getAllocationStatus()));
                //areaCode 需要通过资源池名称去查询
                deviceGpuInfoDTO.setAreaCode(areaMap.get(hashrateResInfoDTO.getRegionName()));
                //内存可能要根据ka来翻译;
                deviceGpuInfoDTO.setMemory(0);
                deviceGpuInfoDTO.setSliceStatus("0");
                deviceGpuInfoDTO.setDeptName(hashrateResInfoDTO.getDeptName());
                deviceGpuInfoDTO.setVmName(hashrateResInfoDTO.getDeviceName());
                deviceGpuInfoDTO.setModelName(hashrateResInfoDTO.getModel());
                deviceGpuInfoDTO.setSubModelName(hashrateResInfoDTO.getModel());
                deviceGpuInfoDTO.setInManage("0");
                deviceGpuInfoDTO.setVmType(deviceGpuInfoDTO.getVmType());
                deviceGpuInfoDTO.setVmId(hashrateResInfoDTO.getDeviceId());
                deviceGpuInfoDTO.setVmCpu(hashrateResInfoDTO.getCpu());
                deviceGpuInfoDTO.setVmMemory(hashrateResInfoDTO.getMemory());
                deviceGpuInfoDTO.setVmDisk(hashrateResInfoDTO.getDisk());
                deviceGpuInfoDTO.setOsName(hashrateResInfoDTO.getOsName());
                deviceGpuInfoDTO.setApplicant(hashrateResInfoDTO.getApplicant());
                deviceGpuInfoDTO.setProductGategory(hashrateResInfoDTO.getProductCategory());
                deviceGpuInfoDTO.setApplicationTime(ObjNullUtils.isNull(hashrateResInfoDTO.getApplicationTime()) ? null : DateUtils.toString(hashrateResInfoDTO.getApplicationTime()));
                deviceGpuInfoDTO.setExpirationTime(ObjNullUtils.isNull(hashrateResInfoDTO.getExpirationTime()) ? null : DateUtils.toString(hashrateResInfoDTO.getExpirationTime()));
                deviceGpuInfoManager.update(deviceGpuInfoDTO);
            }
        });
        return CommonResult.success(null);
    }

    //page
    @RequestMapping("/page")
    public CommonResult<PageResult<HashrateResInfoDTO>> page(@RequestBody HashrateResInfoQuery query) {
        return CommonResult.success(hashrateResInfoManager.page(query));
    }



    //编辑
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/add")
    public CommonResult<Void> add(@RequestBody HashrateResInfoDTO hashrateResInfoDTO) {
        DeviceGpuInfoDTO deviceGpuInfoDTO = deviceGpuInfoManager.getbyVmIdAndIndexT(hashrateResInfoDTO.getDeviceId(), hashrateResInfoDTO.getIndexT());
        //不能重复添加
        Precondition.checkArgument(deviceGpuInfoDTO == null, "<UNK> 物理卡已存在，不能重复添加");
        Map<String, String> areaMap = getyAreaMap();
        hashrateResInfoDTO.setAreaCode(areaMap.get(hashrateResInfoDTO.getRegionName()));
        //插入
        hashrateResInfoManager.insert(hashrateResInfoDTO);

        //如果不存在 则新增
        DeviceGpuInfoDTO insertDto = new DeviceGpuInfoDTO();
        insertDto.setCatalogueDomainName(hashrateResInfoDTO.getDomainName());
        insertDto.setRegionName(hashrateResInfoDTO.getRegionName());
        insertDto.setBusinessSystemName(hashrateResInfoDTO.getBusinessName());
        insertDto.setDeviceType(convertDeviceType(hashrateResInfoDTO.getModel()));
        insertDto.setModelName(hashrateResInfoDTO.getModel());
        insertDto.setSubModelName(hashrateResInfoDTO.getModel());
        insertDto.setDcnNetAddr(hashrateResInfoDTO.getDcnAddress());
        insertDto.setPrivateNetAddr(hashrateResInfoDTO.getPrivateNetworkAddress());
        insertDto.setInUsed(convertInUsed(hashrateResInfoDTO.getAllocationStatus()));
        //areaCode 需要通过资源池名称去查询
        insertDto.setAreaCode(areaMap.get(hashrateResInfoDTO.getRegionName()));
        insertDto.setVmId(hashrateResInfoDTO.getDeviceId());
        insertDto.setIndexT(hashrateResInfoDTO.getIndexT());
        //内存可能要根据ka来翻译;
        insertDto.setMemory(0);
        insertDto.setSourceType(convertSourceType(hashrateResInfoDTO.getModel()));
        insertDto.setSliceStatus("0");
        insertDto.setDeptName(hashrateResInfoDTO.getDeptName());
        insertDto.setVmName(hashrateResInfoDTO.getDeviceName());
        insertDto.setInManage("0");
        insertDto.setVmCpu(hashrateResInfoDTO.getCpu());
        insertDto.setVmMemory(hashrateResInfoDTO.getMemory());
        insertDto.setVmDisk(hashrateResInfoDTO.getDisk());
        insertDto.setOsName(hashrateResInfoDTO.getOsName());
        insertDto.setApplicant(hashrateResInfoDTO.getApplicant());
        insertDto.setProductGategory(hashrateResInfoDTO.getProductCategory());
        insertDto.setApplicationTime(ObjNullUtils.isNull(hashrateResInfoDTO.getApplicationTime()) ? null : DateUtils.toString(hashrateResInfoDTO.getApplicationTime()));
        insertDto.setExpirationTime(ObjNullUtils.isNull(hashrateResInfoDTO.getExpirationTime()) ? null : DateUtils.toString(hashrateResInfoDTO.getExpirationTime()));
        insertDto.setLastPeriod("{}");
        insertDto.setRunStatus("离线");
        insertDto.setCollectStatus("异常");
        deviceGpuInfoManager.create(insertDto);
        return CommonResult.success(null);
    }


    //编辑
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/edit")
    public CommonResult<Void> edit(@RequestBody HashrateResInfoDTO hashrateResInfoDTO) {
        hashrateResInfoManager.update(hashrateResInfoDTO);
        //更新完后查询下
        HashrateResInfoDTO dbDTO = hashrateResInfoManager.getById(hashrateResInfoDTO.getId());
        if (ObjNullUtils.isNull(dbDTO)) {
            log.info("更新智算资源失败, 智算资源不存在, id: {}", hashrateResInfoDTO.getId());
            return CommonResult.failure("更新智算资源失败, 智算资源不存在");
        }
        if ("/".equals(dbDTO.getDeviceId())||ObjNullUtils.isNull(dbDTO.getDeviceId())||ObjNullUtils.isNull(dbDTO.getIndexT())) {
            return CommonResult.success(null);
        }
        Map<String, String> areaMap = getyAreaMap();
        //查询物理卡表
        DeviceGpuInfoDTO deviceGpuInfoDTO = deviceGpuInfoManager.getbyVmIdAndIndexT(dbDTO.getDeviceId(), dbDTO.getIndexT());
        if (ObjNullUtils.isNull(deviceGpuInfoDTO)) {
            log.info("更新物理卡表失败, 物理卡不存在, deviceId: {}, indexT: {}", hashrateResInfoDTO.getDeviceId(), hashrateResInfoDTO.getIndexT());
            return CommonResult.failure("更新物理卡表失败, 物理卡不存在");
        }
        //同步更新物理卡表
        deviceGpuInfoDTO.setCatalogueDomainName(hashrateResInfoDTO.getDomainName());
        deviceGpuInfoDTO.setRegionName(hashrateResInfoDTO.getRegionName());
        deviceGpuInfoDTO.setBusinessSystemName(hashrateResInfoDTO.getBusinessName());
        deviceGpuInfoDTO.setDcnNetAddr(hashrateResInfoDTO.getDcnAddress());
        deviceGpuInfoDTO.setPrivateNetAddr(hashrateResInfoDTO.getPrivateNetworkAddress());
        deviceGpuInfoDTO.setInUsed(convertInUsed(hashrateResInfoDTO.getAllocationStatus()));
        //areaCode 需要通过资源池名称去查询
        deviceGpuInfoDTO.setAreaCode(areaMap.get(hashrateResInfoDTO.getRegionName()));
        //内存可能要根据ka来翻译;
        deviceGpuInfoDTO.setMemory(0);
        deviceGpuInfoDTO.setSliceStatus("0");
        deviceGpuInfoDTO.setDeptName(hashrateResInfoDTO.getDeptName());
        deviceGpuInfoDTO.setVmName(hashrateResInfoDTO.getDeviceName());
        deviceGpuInfoDTO.setInManage("0");
        deviceGpuInfoDTO.setModelName(hashrateResInfoDTO.getModel());
        deviceGpuInfoDTO.setSubModelName(hashrateResInfoDTO.getModel());
        deviceGpuInfoDTO.setVmType(deviceGpuInfoDTO.getVmType());
        deviceGpuInfoDTO.setVmCpu(hashrateResInfoDTO.getCpu());
        deviceGpuInfoDTO.setVmMemory(hashrateResInfoDTO.getMemory());
        deviceGpuInfoDTO.setVmDisk(hashrateResInfoDTO.getDisk());
        deviceGpuInfoDTO.setOsName(hashrateResInfoDTO.getOsName());
        deviceGpuInfoDTO.setApplicant(hashrateResInfoDTO.getApplicant());
        deviceGpuInfoDTO.setProductGategory(hashrateResInfoDTO.getProductCategory());
        deviceGpuInfoDTO.setApplicationTime(ObjNullUtils.isNull(hashrateResInfoDTO.getApplicationTime()) ? null : DateUtils.toString(hashrateResInfoDTO.getApplicationTime()));
        deviceGpuInfoDTO.setExpirationTime(ObjNullUtils.isNull(hashrateResInfoDTO.getExpirationTime()) ? null : DateUtils.toString(hashrateResInfoDTO.getExpirationTime()));
        deviceGpuInfoManager.update(deviceGpuInfoDTO);
        return CommonResult.success(null);
    }

    //删除
    @RequestMapping("/delete")
    public CommonResult<Void> delById(@RequestBody HashrateResInfoDTO dto) {
        //先查询是否存在
        HashrateResInfoDTO hashrateResInfoDTO = hashrateResInfoManager.getById(dto.getId());
        if (ObjNullUtils.isNull(hashrateResInfoDTO)) {
            log.info("删除智算资源失败, 资源不存在, id: {}", dto.getId());
            return CommonResult.failure("删除智算资源失败, 资源不存在");
        }
        hashrateResInfoManager.delById(dto.getId());
        if ("/".equals(hashrateResInfoDTO.getDeviceId())||ObjNullUtils.isNull(hashrateResInfoDTO.getDeviceId())||ObjNullUtils.isNull(hashrateResInfoDTO.getIndexT())) {
            return CommonResult.success(null);
        }
        //删除物理卡表
        DeviceGpuInfoDTO deviceGpuInfoDTO = deviceGpuInfoManager.getbyVmIdAndIndexT(hashrateResInfoDTO.getDeviceId(), hashrateResInfoDTO.getIndexT());

        deviceGpuInfoManager.delete(deviceGpuInfoDTO.getId());
        return CommonResult.success(null);
    }


    //导出
    @RequestMapping("/export")
    public void export(@RequestBody HashrateResInfoQuery query, HttpServletResponse response) {
        List<HashrateResInfoDTO> list = hashrateResInfoManager.list(query);
        // 生成文件路径
        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/export/" + UUID.randomUUID().toString().replaceAll("-", "") + ".xlsx";
        // 确保导出目录存在
        File exportDir = new File(projectPath + "/export/");
        if (!exportDir.exists()) {
            exportDir.mkdirs();
        }
        // 导出Excel
        FileUtils.doExport(list, HashrateResInfoDTO.class, filePath);
        // 下载文件
        downloadFile(response, filePath, "智算资源信息列表.xlsx");
    }



    private String convertDeviceType(String model) {
        if (model == null) {
            return "UNKNOWN";
        }

        switch (model.toUpperCase()) {

            // ===== NVIDIA GPU =====
            case "T4":
            case "V100":
            case "A10":
            case "A40":
                return "GPU";

            // ===== 华为昇腾 NPU =====
            case "300I":
            case "910B2":
            case "910B4":
            case "910C":
                return "NPU";

            default:
                return "UNKNOWN";
        }
    }


    private String convertSourceType(String model) {
        if (model == null) {
            return "UNKNOWN";
        }

        switch (model.toUpperCase()) {

            // ===== NVIDIA GPU =====
            case "T4":
                return "QD-physical";
            case "V100":
            case "A10":
            case "A40":
                return "jd-physical";

            // ===== 华为昇腾 NPU =====
            case "300I":
            case "910B2":
            case "910B4":
                return "NB-physical";
            case "910C":
                return "CCAE-physical";

            default:
                return "UNKNOWN";
        }
    }


    private String convertInUsed(String deviceStatus) {
        if ("已分配".equals(deviceStatus)) {
            return "1";
        }
        if ("未分配".equals(deviceStatus)) {
            return "0";
        }
        if ("未集成".equals(deviceStatus)) {
            return "2";
        }
        return "0";

    }


    private Map<String, String> getyAreaMap() {
        // 初始化HashMap用于存储结果
        Map<String, String> areaMap = new HashMap<>();
        // 解析JSON字符串为JSONArray
        JSONArray objects = JSONArray.parseArray(AREA_CODE_JSON);

        // 遍历JSONArray中的每个JSONObject
        for (int i = 0; i < objects.size(); i++) {
            JSONObject jsonObject = objects.getJSONObject(i);

            // 提取regionName和areaCode字段（做非空判断避免空指针）
            String regionName = jsonObject.getString("regionName");
            String areaCode = jsonObject.getString("areaCode");
            // 仅当两个字段都不为空时才放入Map
            if (regionName != null && areaCode != null) {
                areaMap.put(regionName, areaCode);
            }
        }
        return areaMap;

    }

    /**
     * 下载文件
     */
    private void downloadFile(HttpServletResponse response, String exportPath, String fileName) {
        try {
            // 以流的形式下载文件
            InputStream fis = new BufferedInputStream(Files.newInputStream(Paths.get(exportPath)));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();

            // 清空response
            response.reset();
            // 设置response响应头
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

            // 添加Content-Length
            File file = new File(exportPath);
            response.setContentLength((int) file.length());

            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(buffer);
            outputStream.flush();
            outputStream.close();

            // 删除临时文件
            Files.deleteIfExists(Paths.get(exportPath));
        } catch (IOException e) {
            // 记录日志但不抛出异常
        }
    }

    private final String AREA_CODE_JSON = "[\n" +
            "  {\n" +
            "    \"regionName\": \"边缘云省节点\",\n" +
            "    \"areaCode\": \"杭州\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"平台云-萧山02\",\n" +
            "    \"areaCode\": \"杭州\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"平台云-萧山03-通用-威睿01\",\n" +
            "    \"areaCode\": \"杭州\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"平台云-萧山04-GPU-威睿01\",\n" +
            "    \"areaCode\": \"杭州\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"融合边缘云-杭州01\",\n" +
            "    \"areaCode\": \"杭州\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"融合边缘云-湖州01\",\n" +
            "    \"areaCode\": \"湖州\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"融合边缘云-嘉兴01\",\n" +
            "    \"areaCode\": \"嘉兴\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"融合边缘云-金华01\",\n" +
            "    \"areaCode\": \"金华\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"融合边缘云-金华02\",\n" +
            "    \"areaCode\": \"金华\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"融合边缘云-金华省级资源池\",\n" +
            "    \"areaCode\": \"金华\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"融合边缘云-丽水01\",\n" +
            "    \"areaCode\": \"丽水\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"融合边缘云-宁波01\",\n" +
            "    \"areaCode\": \"宁波\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"融合边缘云-宁波省级资源池\",\n" +
            "    \"areaCode\": \"宁波\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"融合边缘云-衢州01\",\n" +
            "    \"areaCode\": \"衢州\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"融合边缘云-绍兴01\",\n" +
            "    \"areaCode\": \"绍兴\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"融合边缘云-台州01\",\n" +
            "    \"areaCode\": \"台州\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"融合边缘云-温州01\",\n" +
            "    \"areaCode\": \"温州\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"融合边缘云-舟山01\",\n" +
            "    \"areaCode\": \"舟山\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"网络云-大金金华网管18\",\n" +
            "    \"areaCode\": \"金华\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-杭州-地市-威睿-paas\",\n" +
            "    \"areaCode\": \"杭州\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-杭州-地市-威睿-业务\",\n" +
            "    \"areaCode\": \"杭州\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-湖州-地市-威睿-paas\",\n" +
            "    \"areaCode\": \"湖州\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-嘉兴-地市-威睿-paas\",\n" +
            "    \"areaCode\": \"嘉兴\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-嘉兴-地市-威睿-业务\",\n" +
            "    \"areaCode\": \"嘉兴\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-金华-地市-威睿-paas\",\n" +
            "    \"areaCode\": \"金华\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-金华-地市-威睿-业务\",\n" +
            "    \"areaCode\": \"金华\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-金华-义乌区-华三-业务\",\n" +
            "    \"areaCode\": \"金华\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-金华-浙中-威睿-业务\",\n" +
            "    \"areaCode\": \"金华\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-丽水-地市-威睿-paas\",\n" +
            "    \"areaCode\": \"丽水\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-丽水-地市-威睿-业务\",\n" +
            "    \"areaCode\": \"丽水\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-宁波-地市-威睿-paas\",\n" +
            "    \"areaCode\": \"宁波\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-宁波-地市-威睿-业务\",\n" +
            "    \"areaCode\": \"宁波\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-衢州-地市-威睿-paas\",\n" +
            "    \"areaCode\": \"衢州\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-绍兴-地市-威睿-paas\",\n" +
            "    \"areaCode\": \"绍兴\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-台州-地市-威睿-paas\",\n" +
            "    \"areaCode\": \"台州\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-温州-地市-威睿-paas\",\n" +
            "    \"areaCode\": \"温州\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"浙江-温州-地市-威睿-业务\",\n" +
            "    \"areaCode\": \"温州\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"regionName\": \"智算资源池\",\n" +
            "    \"areaCode\": \"杭州\"\n" +
            "  }\n" +
            "]";


}
