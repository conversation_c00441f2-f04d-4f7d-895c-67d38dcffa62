package com.datatech.slgzt.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.datatech.slgzt.convert.ChangeWorkOrderWebConvert;
import com.datatech.slgzt.convert.NonStanderWorkOrderWebConvert;
import com.datatech.slgzt.convert.RecoveryWorkOrderWebConvert;
import com.datatech.slgzt.convert.StandardWorkOrderWebConvert;
import com.datatech.slgzt.enums.ChangeTypeEnum;
import com.datatech.slgzt.enums.ModuleEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.exception.BusinessException;
import com.datatech.slgzt.exception.UniversalException;
import com.datatech.slgzt.home.HomeDataStatisticsService;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.bpmn.ActivityTaskTreeVo;
import com.datatech.slgzt.model.bpmn.ActivityTaskVo;
import com.datatech.slgzt.model.bpmn.InnerTask;
import com.datatech.slgzt.model.bpmn.TaskTreeNodeDTO;
import com.datatech.slgzt.model.change.*;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.dto.change.ChangeAuditWorkOrderDTO;
import com.datatech.slgzt.model.dto.change.ChangeWorkOrderDTO;
import com.datatech.slgzt.model.dto.nonstandard.NonStanderAuditWorkOrderDTO;
import com.datatech.slgzt.model.dto.order.RecoveryAuditWorkOrderDTO;
import com.datatech.slgzt.model.dto.order.StandardAuditWorkOrderDTO;
import com.datatech.slgzt.model.home.AuditCountVo;
import com.datatech.slgzt.model.home.ExtendAuditCountVO;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.query.*;
import com.datatech.slgzt.model.recovery.*;
import com.datatech.slgzt.model.req.moa.ApproveReq;
import com.datatech.slgzt.model.req.moa.GetDecisionReq;
import com.datatech.slgzt.model.req.moa.GetFlowRecordReq;
import com.datatech.slgzt.model.req.moa.GetTaskInfoReq;
import com.datatech.slgzt.model.resourcce.ResourceShowInfoDTO;
import com.datatech.slgzt.model.user.OacRoleInfo;
import com.datatech.slgzt.model.user.OacUser;
import com.datatech.slgzt.model.usercenter.UserCenterRoleDTO;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.change.ChangeWorkOrderDetailVO;
import com.datatech.slgzt.model.vo.moa.*;
import com.datatech.slgzt.model.vo.network.NetworkOrderResult;
import com.datatech.slgzt.model.vo.nonstander.NonStanderWorkOrderVO;
import com.datatech.slgzt.model.vo.recovery.RecoveryWorkOrderDetailVO;
import com.datatech.slgzt.model.vo.standard.StandardWorkOrderDetailVO;
import com.datatech.slgzt.model.vo.vpc.VpcOrderResult;
import com.datatech.slgzt.service.UserService;
import com.datatech.slgzt.service.change.ChangeWorkOrderService;
import com.datatech.slgzt.service.nostander.NonStanderWorkOrderService;
import com.datatech.slgzt.service.recovery.RecoveryWorkOrderService;
import com.datatech.slgzt.service.standard.StandardWorkOrderService;
import com.datatech.slgzt.service.usercenter.IUserSelectStrategy;
import com.datatech.slgzt.utils.*;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @program: workordercenterproject
 * @description: MOA系统对接
 * @author: LK
 * @create: 2025-12-11 11:08
 **/
@Slf4j
@RestController
@RequestMapping("/moa")
public class MoaOrderController {

    private static final String MODULE_STANDARD_WORK_ORDER = "standardWorkOrder";
    private static final String MODULE_NON_STANDARD_WORK_ORDER = "nonStandardWorkOrder";
    private static final String MODULE_RECOVERY_WORK_ORDER = "recoveryWorkOrder";
    private static final String MODULE_CHANGE_WORK_ORDER = "changeWorkOrder";

    private static final String SYSTEM_ERROR = "系统异常";

    private static final List<String> VM_PRODUCT = ListUtil.of("ecs", "gcs", "redis", "mysql", "postgreSql");

    @Resource
    private StandardWorkOrderService standardWorkOrderService;
    @Resource
    private NonStanderWorkOrderService nonStanderWorkOrderService;
    @Resource
    private RecoveryWorkOrderService recoveryWorkOrderService;
    @Resource
    private ChangeWorkOrderService changeWorkOrderService;

    @Resource
    private WorkOrderAuthLogManager workOrderAuthLogManager;

    @Resource
    private IUserSelectStrategy iUserSelectStrategy;
    @Resource
    private HomeDataStatisticsService homeDataStatisticsService;
    @Resource
    private StandardWorkOrderManager standardWorkOrderManager;
    @Resource
    private StandardWorkOrderProductManager standardWorkOrderProductManager;
    @Resource
    private StandardWorkOrderWebConvert convert;
    @Resource
    private NonStanderWorkOrderManager nonStanderWorkOrderManager;
    @Resource
    private NonStanderWorkOrderProductManager nonStandardWorkOrderProductManager;
    @Resource
    private RecoveryWorkOrderManager recoveryWorkOrderManager;
    @Resource
    private NonStanderWorkOrderWebConvert nonStanderWorkOrderWebConvert;
    @Resource
    private RecoveryWorkOrderProductManager recoveryWorkOrderProductManager;
    @Resource
    private RecoveryWorkOrderWebConvert recoveryWorkOrderWebConvert;
    @Resource
    private ChangeWorkOrderManager changeWorkOrderManager;
    @Resource
    private ChangeWorkOrderProductManager changeWorkOrderProductManager;
    @Resource
    private ChangeWorkOrderWebConvert changeWorkOrderWebConvert;
    @Resource
    private ResourceDetailManager resourceDetailManager;
    @Resource
    private VpcOrderManager vpcOrderManager;
    @Resource
    private NetworkOrderManager networkOrderManager;
    @Resource
    private ContainerQuotaManager containerQuotaManager;

    @Resource
    private UserService userService;

    /**
     * 待办工单数量
     *
     * @param req
     * @return
     */
    @PostMapping("/getReadyTaskCount")
    public JSONObject getReadyTaskCount(@RequestParam("paramjson") String paramjson) {
        log.info("MOA系统对接，getReadyTaskCount--入参：{}", paramjson);
        try {
            MoaCountReq req = JSON.parseObject(paramjson, MoaCountReq.class);
            return buildTaskCountResponse(req, AuditCountVo::getPendingCount);
        } catch (Exception e) {
            log.error("MOA系统对接，getReadyTaskCount请求失败：{}", ExceptionUtils.getStackTrace(e));
            return buildErrorResponse(e);
        }
    }

    /**
     * 已办工单数量
     *
     * @param req
     * @return
     */
    @PostMapping("/getCompletedTaskCount")
    public JSONObject getCompletedTaskCount(@RequestParam("paramjson") String paramjson) {
        log.info("MOA系统对接，getCompletedTaskCount--入参：{}", paramjson);
        try {
            MoaCountReq req = JSON.parseObject(paramjson, MoaCountReq.class);
            return buildTaskCountResponse(req, AuditCountVo::getApprovedCount);
        } catch (Exception e) {
            log.error("MOA系统对接，getCompletedTaskCount请求失败：{}", ExceptionUtils.getStackTrace(e));
            return buildErrorResponse(e);
        }
    }

    /**
     * 工单详情
     *
     * @param req
     * @return
     */
    @PostMapping("/taskInfo")
    public JSONObject taskInfo(@RequestParam("paramjson") String paramjson) {
        log.info("MOA系统对接，taskInfo--入参：{}", paramjson);
        try {
            MoaDetailReq req = JSON.parseObject(paramjson, MoaDetailReq.class);
            Precondition.checkArgument(req.getUserId(), "userId 不能为空");
            Precondition.checkArgument(req.getAppId(), "应用id 不能为空");
            String moduleId = req.getModuleId();
            Precondition.checkArgument(moduleId, "模块id 不能为空");
            String workOrderId = req.getWorkOrderId();
            Precondition.checkArgument(workOrderId, "工单id 不能为空");
            JSONObject resp = new JSONObject();
            resp.put("responsecode", 1);
            resp.put("responsemessage", "");
            resp.put("globalparam", req.getGlobalParam());
            resp.put("otherparam", req.getOtherParam());
//        try {
            if (ModuleEnum.STANDARD.getCode().equals(req.getModuleId())) {
                StandardWorkOrderDetailVO standardWorkOrderDetail = getStandardWorkOrderDetail(workOrderId);
                // 1. 加载模板（建议从资源文件读取）
                JSONObject template = loadStandardTemplate(ModuleEnum.STANDARD);
                // 2. 转 entity 为 Map，保留 null
                String entityJson = JSON.toJSONString(standardWorkOrderDetail, SerializerFeature.WriteMapNullValue);
                Map<String, Object> dataMap = JSON.parseObject(entityJson, new TypeReference<Map<String, Object>>() {
                });
                // 3. 从 auditLogList 提取审核结果，补充到 dataMap
                enrichAuditResults(dataMap, standardWorkOrderDetail.getAuditLogList());
                // 4. 替换所有 ${xxx}
                replacePlaceholdersInJson(template, dataMap);
                // 5. 构造产品信息
                JSONArray resultList = processShowInfo(template, standardWorkOrderDetail, ModuleEnum.STANDARD, workOrderId);
                // 6. 响应赋值
                resp.put("resultlist", resultList);
            } else if (ModuleEnum.RECOVERY.getCode().equals(req.getModuleId())) {
                RecoveryWorkOrderDetailVO recoveryWorkOrderDetail = getRecoveryWorkOrderDetail(workOrderId);
                JSONObject template = loadStandardTemplate(ModuleEnum.RECOVERY);
                String entityJson = JSON.toJSONString(recoveryWorkOrderDetail, SerializerFeature.WriteMapNullValue);
                Map<String, Object> dataMap = JSON.parseObject(entityJson, new TypeReference<Map<String, Object>>() {
                });
                replacePlaceholdersInJson(template, dataMap);
                // 填充变更资源数据
                JSONArray resultList = processShowInfo(template, recoveryWorkOrderDetail, ModuleEnum.RECOVERY, workOrderId);
                resp.put("resultlist", resultList);
            } else if (ModuleEnum.CHANGE.getCode().equals(req.getModuleId())) {
                ChangeWorkOrderDetailVO changeWorkOrderDetail = getChangeWorkOrderDetail(workOrderId, req.getUserId());
                JSONObject template = loadStandardTemplate(ModuleEnum.CHANGE);
                String entityJson = JSON.toJSONString(changeWorkOrderDetail, SerializerFeature.WriteMapNullValue);
                Map<String, Object> dataMap = JSON.parseObject(entityJson, new TypeReference<Map<String, Object>>() {
                });
                Object businessPlanning = dataMap.get("businessPlanning");
                Object businessArchitecture = dataMap.get("businessArchitecture");
                Object cloudArchitecture = dataMap.get("cloudArchitecture");
                Object cloudResources = dataMap.get("cloudResources");
                if (businessPlanning == null || Boolean.FALSE.equals(businessPlanning)) {
                    dataMap.put("businessPlanning", "否");
                } else {
                    dataMap.put("businessPlanning", "是");
                }
                if (businessArchitecture == null || Boolean.FALSE.equals(businessArchitecture)) {
                    dataMap.put("businessArchitecture", "否");
                } else {
                    dataMap.put("businessArchitecture", "是");
                }
                if (cloudArchitecture == null || Boolean.FALSE.equals(cloudArchitecture)) {
                    dataMap.put("cloudArchitecture", "否");
                } else {
                    dataMap.put("cloudArchitecture", "是");
                }
                if (cloudResources == null || Boolean.FALSE.equals(cloudResources)) {
                    dataMap.put("cloudResources", "否");
                } else {
                    dataMap.put("cloudResources", "是");
                }
                replacePlaceholdersInJson(template, dataMap);
                // 填充变更资源数据
                JSONArray resultList = processShowInfo(template, changeWorkOrderDetail, ModuleEnum.CHANGE, workOrderId);
                resp.put("resultlist", resultList);
            } else if (ModuleEnum.NON_STANDARD.getCode().equals(req.getModuleId())) {
                NonStanderWorkOrderVO nonStanderWorkOrderDetail = getNonStanderWorkOrderDetail(workOrderId);
                JSONObject template = loadStandardTemplate(ModuleEnum.NON_STANDARD);
                String entityJson = JSON.toJSONString(nonStanderWorkOrderDetail, SerializerFeature.WriteMapNullValue);
                Map<String, Object> dataMap = JSON.parseObject(entityJson, new TypeReference<Map<String, Object>>() {
                });
                replacePlaceholdersInJson(template, dataMap);
                // 填充变更资源数据
                JSONArray resultList = processShowInfo(template, nonStanderWorkOrderDetail, ModuleEnum.NON_STANDARD, workOrderId);
                resp.put("resultlist", resultList);
            }
//        } catch (Exception e) {
//            log.error("请求失败：{}", e.getMessage());
//            resp.put("responsecode", 0);
//            resp.put("responsemessage", e.getMessage());
//            resp.put("resultlist", null);
//        }

            return resp;
        } catch (Exception e) {
            log.error("MOA系统对接，taskInfo请求失败：{}", ExceptionUtils.getStackTrace(e));
            return buildErrorResponse(e);
        }
    }

    /**
     * 获取待办任务列表
     *
     * @param req req
     * @return 任务信息
     */
    private MoaCommonResult<List<TaskInfoVO>> getReadyTask(GetTaskInfoReq req) {
        log.info("MOA系统对接，getReadyTask--入参：{}", req);
        try {
            OacUser oacUser = checkOAUser(req.getUserId());
            if (StringUtils.isBlank(req.getModuleId())) {
                // 查所有
                req.setModuleId(MODULE_STANDARD_WORK_ORDER + "|" + MODULE_NON_STANDARD_WORK_ORDER + "|" + MODULE_RECOVERY_WORK_ORDER + "|" + MODULE_CHANGE_WORK_ORDER);
                return getReadyTask(req);
            } else {
                String[] moduleIds = StringUtils.split(req.getModuleId(), '|');
                if (moduleIds.length == 1) {
                    String moduleId = moduleIds[0];
                    if (MODULE_STANDARD_WORK_ORDER.equals(moduleId)) {
                        return selectStandardWorkOrder(oacUser, moduleId, "pending", req.getPage(), req.getPageSize());
                    } else if (MODULE_NON_STANDARD_WORK_ORDER.equals(moduleId)) {
                        return selectNonStanderWorkOrder(oacUser, moduleId, "pending", req.getPage(), req.getPageSize());
                    } else if (MODULE_RECOVERY_WORK_ORDER.equals(moduleId)) {
                        return selectRecoveryWorkOrder(oacUser, moduleId, "pending", req.getPage(), req.getPageSize());
                    } else if (MODULE_CHANGE_WORK_ORDER.equals(moduleId)) {
                        return selectChangeWorkOrder(oacUser, moduleId, "pending", req.getPage(), req.getPageSize());
                    } else {
                        return MoaCommonResult.failure("不支持的模块ID");
                    }
                } else {
                    // 查多个
                    MoaCommonResult<List<TaskInfoVO>> standardWorkOrder = null;
                    MoaCommonResult<List<TaskInfoVO>> nonStanderWorkOrder = null;
                    MoaCommonResult<List<TaskInfoVO>> recoveryWorkOrder = null;
                    MoaCommonResult<List<TaskInfoVO>> changeWorkOrder = null;
                    // 根据date降序
                    for (String moduleId : moduleIds) {
                        if (MODULE_STANDARD_WORK_ORDER.equals(moduleId)) {
                            standardWorkOrder = selectStandardWorkOrder(oacUser, moduleId, "pending", 1, 10000);
                        }
                        if (MODULE_NON_STANDARD_WORK_ORDER.equals(moduleId)) {
                            nonStanderWorkOrder = selectNonStanderWorkOrder(oacUser, moduleId, "pending", 1, 10000);
                        }
                        if (MODULE_RECOVERY_WORK_ORDER.equals(moduleId)) {
                            recoveryWorkOrder = selectRecoveryWorkOrder(oacUser, moduleId, "pending", 1, 10000);
                        }
                        if (MODULE_CHANGE_WORK_ORDER.equals(moduleId)) {
                            changeWorkOrder = selectChangeWorkOrder(oacUser, moduleId, "pending", 1, 10000);
                        }
                    }
                    // 根据date降序
                    List<TaskInfoVO> taskInfoVOList = Lists.newArrayList();
                    if (standardWorkOrder != null) {
                        taskInfoVOList.addAll(standardWorkOrder.getResultList());
                    }
                    if (nonStanderWorkOrder != null) {
                        taskInfoVOList.addAll(nonStanderWorkOrder.getResultList());
                    }
                    if (recoveryWorkOrder != null) {
                        taskInfoVOList.addAll(recoveryWorkOrder.getResultList());
                    }
                    if (changeWorkOrder != null) {
                        taskInfoVOList.addAll(changeWorkOrder.getResultList());
                    }
                    taskInfoVOList.sort((o1, o2) -> o2.getDate().compareTo(o1.getDate()));
                    // 根据req.page和req.pageSize分页
                    int fromIndex = (req.getPage() - 1) * req.getPageSize();
                    int toIndex = Math.min(fromIndex + req.getPageSize(), taskInfoVOList.size());
                    taskInfoVOList = taskInfoVOList.subList(fromIndex, toIndex);
                    MoaCommonResult<List<TaskInfoVO>> result = MoaCommonResult.success(taskInfoVOList);
                    result.setTotalCount((long) taskInfoVOList.size());
                    result.setPageSize(Long.valueOf(req.getPageSize()));
                    result.setPageNo(Long.valueOf(req.getPage()));
                    return result;
                }
            }
        } catch (Exception e) {
            return getMoaCommonResultByException(e);
        }
    }

    @RequestMapping("/getReadyTask")
    public MoaCommonResult<List<TaskInfoVO>> getReadyTask(@RequestParam("paramjson") String paramjson) {
        log.info("MOA系统对接，getReadyTask--入参：{}", paramjson);
        try {
            GetTaskInfoReq req = JSON.parseObject(paramjson, GetTaskInfoReq.class);
            OacUser oacUser = checkOAUser(req.getUserId());
            if (StringUtils.isBlank(req.getModuleId())) {
                // 查所有
                req.setModuleId(MODULE_STANDARD_WORK_ORDER + "|" + MODULE_NON_STANDARD_WORK_ORDER + "|" + MODULE_RECOVERY_WORK_ORDER + "|" + MODULE_CHANGE_WORK_ORDER);
                return getReadyTask(req);
            } else {
                String[] moduleIds = StringUtils.split(req.getModuleId(), '|');
                if (moduleIds.length == 1) {
                    String moduleId = moduleIds[0];
                    if (MODULE_STANDARD_WORK_ORDER.equals(moduleId)) {
                        return selectStandardWorkOrder(oacUser, moduleId, "pending", req.getPage(), req.getPageSize());
                    } else if (MODULE_NON_STANDARD_WORK_ORDER.equals(moduleId)) {
                        return selectNonStanderWorkOrder(oacUser, moduleId, "pending", req.getPage(), req.getPageSize());
                    } else if (MODULE_RECOVERY_WORK_ORDER.equals(moduleId)) {
                        return selectRecoveryWorkOrder(oacUser, moduleId, "pending", req.getPage(), req.getPageSize());
                    } else if (MODULE_CHANGE_WORK_ORDER.equals(moduleId)) {
                        return selectChangeWorkOrder(oacUser, moduleId, "pending", req.getPage(), req.getPageSize());
                    } else {
                        return MoaCommonResult.failure("不支持的模块ID");
                    }
                } else {
                    // 查多个
                    MoaCommonResult<List<TaskInfoVO>> standardWorkOrder = null;
                    MoaCommonResult<List<TaskInfoVO>> nonStanderWorkOrder = null;
                    MoaCommonResult<List<TaskInfoVO>> recoveryWorkOrder = null;
                    MoaCommonResult<List<TaskInfoVO>> changeWorkOrder = null;
                    // 根据date降序
                    for (String moduleId : moduleIds) {
                        if (MODULE_STANDARD_WORK_ORDER.equals(moduleId)) {
                            standardWorkOrder = selectStandardWorkOrder(oacUser, moduleId, "pending", 1, 10000);
                        }
                        if (MODULE_NON_STANDARD_WORK_ORDER.equals(moduleId)) {
                            nonStanderWorkOrder = selectNonStanderWorkOrder(oacUser, moduleId, "pending", 1, 10000);
                        }
                        if (MODULE_RECOVERY_WORK_ORDER.equals(moduleId)) {
                            recoveryWorkOrder = selectRecoveryWorkOrder(oacUser, moduleId, "pending", 1, 10000);
                        }
                        if (MODULE_CHANGE_WORK_ORDER.equals(moduleId)) {
                            changeWorkOrder = selectChangeWorkOrder(oacUser, moduleId, "pending", 1, 10000);
                        }
                    }
                    // 根据date降序
                    List<TaskInfoVO> taskInfoVOList = Lists.newArrayList();
                    if (standardWorkOrder != null) {
                        taskInfoVOList.addAll(standardWorkOrder.getResultList());
                    }
                    if (nonStanderWorkOrder != null) {
                        taskInfoVOList.addAll(nonStanderWorkOrder.getResultList());
                    }
                    if (recoveryWorkOrder != null) {
                        taskInfoVOList.addAll(recoveryWorkOrder.getResultList());
                    }
                    if (changeWorkOrder != null) {
                        taskInfoVOList.addAll(changeWorkOrder.getResultList());
                    }
                    taskInfoVOList.sort((o1, o2) -> o2.getDate().compareTo(o1.getDate()));
                    // 根据req.page和req.pageSize分页
                    int fromIndex = (req.getPage() - 1) * req.getPageSize();
                    int toIndex = Math.min(fromIndex + req.getPageSize(), taskInfoVOList.size());
                    taskInfoVOList = taskInfoVOList.subList(fromIndex, toIndex);
                    MoaCommonResult<List<TaskInfoVO>> result = MoaCommonResult.success(taskInfoVOList);
                    result.setTotalCount((long) taskInfoVOList.size());
                    result.setPageSize(Long.valueOf(req.getPageSize()));
                    result.setPageNo(Long.valueOf(req.getPage()));
                    return result;
                }
            }
        } catch (Exception e) {
            return getMoaCommonResultByException(e);
        }
    }

    /**
     * 获取已办任务列表
     *
     * @param req req
     * @return 任务信息
     */
    @RequestMapping("/getCompletedTask")
    public MoaCommonResult<List<TaskInfoVO>> getCompletedTask(@RequestParam("paramjson") String paramjson) {
        log.info("MOA系统对接，getReadyTask--入参：{}", paramjson);
        try {
            GetTaskInfoReq req = JSON.parseObject(paramjson, GetTaskInfoReq.class);
            OacUser oacUser = checkOAUser(req.getUserId());
            if (StringUtils.isBlank(req.getModuleId())) {
                // 查所有
                return MoaCommonResult.failure("请传入模块ID");
            } else {
                String[] moduleIds = StringUtils.split(req.getModuleId(), '|');
                if (moduleIds.length == 1) {
                    String moduleId = moduleIds[0];
                    if (MODULE_STANDARD_WORK_ORDER.equals(moduleId)) {
                        return selectStandardWorkOrder(oacUser, moduleId, "approved", req.getPage(), req.getPageSize());
                    } else if (MODULE_NON_STANDARD_WORK_ORDER.equals(moduleId)) {
                        return selectNonStanderWorkOrder(oacUser, moduleId, "approved", req.getPage(), req.getPageSize());
                    } else if (MODULE_RECOVERY_WORK_ORDER.equals(moduleId)) {
                        return selectRecoveryWorkOrder(oacUser, moduleId, "approved", req.getPage(), req.getPageSize());
                    } else if (MODULE_CHANGE_WORK_ORDER.equals(moduleId)) {
                        return selectChangeWorkOrder(oacUser, moduleId, "approved", req.getPage(), req.getPageSize());
                    } else {
                        return MoaCommonResult.failure("不支持的模块ID");
                    }
                } else {
                    // 查多个
                    return MoaCommonResult.failure("不支持多个模块ID");
                }
            }
        } catch (Exception e) {
            return MoaCommonResult.failure(SYSTEM_ERROR);
        }
    }

    /**
     * 获取决策选项
     *
     * @param req req
     * @return 决策选项
     */
    @RequestMapping("/getDecision")
    public MoaCommonResult getDecision(@RequestParam("paramjson") String paramjson) {
        log.info("MOA系统对接，getDecision--入参：{}", paramjson);
        try {
            GetDecisionReq req = JSON.parseObject(paramjson, GetDecisionReq.class);
            OacUser oacUser = checkOAUser(req.getUserId());
            switch (req.getModuleId()) {
                case MODULE_STANDARD_WORK_ORDER:
                    ActivityTaskVo standardActivityTask = standardWorkOrderService.getTaskNodes(req.getApprivalId());
                    switch (standardActivityTask.getCurrentTask()) {
                        //三级业务部门领导审核
                        case "business2_depart_leader":
                            //二级业务部门领导审核
                        case "business_depart_leader":
                            //三级云资源部领导审核
                        case "cloud_leader":
                            //二级云资源部领导审核
                        case "cloud_leader_2":// 驳回到之前的任意节点
                            List<DecisionVO> decisionVOS = new ArrayList<>();
                            for (InnerTask task : standardActivityTask.getAllTasks()) {
                                if (task.getTask().equals(standardActivityTask.getCurrentTask())) {
                                    break;
                                }
                                DecisionVO decisionVO = new DecisionVO();
                                decisionVO.setTypeId("reject_" + task.getTask());
                                decisionVO.setTransition(task.getTask());
                                decisionVO.setTypeValue("驳回到" + task.getTaskName());
                                decisionVO.setMore(0);
                                decisionVO.setNextUser("");
                                decisionVO.setNeedSuggestion(1);
                                decisionVO.setType("none");
                                decisionVOS.add(decisionVO);
                            }
                            // 通过
                            DecisionVO passDecisionVO = new DecisionVO();
                            passDecisionVO.setTypeId("approve");
                            passDecisionVO.setTransition("");
                            passDecisionVO.setTypeValue("审批通过");
                            passDecisionVO.setMore(0);
                            passDecisionVO.setNextUser("");
                            passDecisionVO.setNeedSuggestion(1);
                            passDecisionVO.setType("none");
                            decisionVOS.add(passDecisionVO);

                            MoaCommonResult success = MoaCommonResult.success();
                            success.setTypeList(decisionVOS);
                            return success;
                        default:
                            return MoaCommonResult.noMobile();
                    }
                case MODULE_RECOVERY_WORK_ORDER:
                    ActivityTaskVo recoveryActivityTask = recoveryWorkOrderService.getTaskNodes(req.getApprivalId());
                    switch (recoveryActivityTask.getCurrentTask()) {
                        //三级业务部门领导审核
                        case "business_depart_leader2":
                            // 驳回到之前的任意节点
                            List<DecisionVO> decisionVOS = new ArrayList<>();
                            for (InnerTask task : recoveryActivityTask.getAllTasks()) {
                                if (task.getTask().equals(recoveryActivityTask.getCurrentTask())) {
                                    break;
                                }
                                DecisionVO decisionVO = new DecisionVO();
                                decisionVO.setTypeId("reject_" + task.getTask());
                                decisionVO.setTransition(task.getTask());
                                decisionVO.setTypeValue("驳回到" + task.getTaskName());
                                decisionVO.setMore(0);
                                decisionVO.setNextUser("");
                                decisionVO.setNeedSuggestion(1);
                                decisionVO.setType("none");
                                decisionVOS.add(decisionVO);
                            }
                            // 通过
                            DecisionVO passDecisionVO = new DecisionVO();
                            passDecisionVO.setTypeId("approve");
                            passDecisionVO.setTransition("");
                            passDecisionVO.setTypeValue("审批通过");
                            passDecisionVO.setMore(0);
                            passDecisionVO.setNextUser("");
                            passDecisionVO.setNeedSuggestion(1);
                            passDecisionVO.setType("none");
                            decisionVOS.add(passDecisionVO);

                            MoaCommonResult success = MoaCommonResult.success();
                            success.setTypeList(decisionVOS);
                            return success;
                        default:
                            return MoaCommonResult.noMobile();
                    }
                case MODULE_CHANGE_WORK_ORDER:
                    ActivityTaskVo changeActivityTask = changeWorkOrderService.getTaskNodes(req.getApprivalId());
                    switch (changeActivityTask.getCurrentTask()) {
                        // 三级业务部门领导审核
                        case "business2_depart_leader":
                            // 二级业务部门领导审核
                        case "business_depart_leader":
                            // 三级云资源部领导审核
                        case "cloud_leader":
                            // 二级云资源部领导审核
                        case "cloud_leader_2":
                            // 驳回到之前的任意节点
                            List<DecisionVO> decisionVOS = new ArrayList<>();
                            for (InnerTask task : changeActivityTask.getAllTasks()) {
                                if (task.getTask().equals(changeActivityTask.getCurrentTask())) {
                                    break;
                                }
                                DecisionVO decisionVO = new DecisionVO();
                                decisionVO.setTypeId("reject_" + task.getTask());
                                decisionVO.setTransition(task.getTask());
                                decisionVO.setTypeValue("驳回到" + task.getTaskName());
                                decisionVO.setMore(0);
                                decisionVO.setNextUser("");
                                decisionVO.setNeedSuggestion(1);
                                decisionVO.setType("none");
                                decisionVOS.add(decisionVO);
                            }
                            // 通过
                            DecisionVO passDecisionVO = new DecisionVO();
                            passDecisionVO.setTypeId("approve");
                            passDecisionVO.setTransition("");
                            passDecisionVO.setTypeValue("审批通过");
                            passDecisionVO.setMore(0);
                            passDecisionVO.setNextUser("");
                            passDecisionVO.setNeedSuggestion(1);
                            passDecisionVO.setType("none");
                            decisionVOS.add(passDecisionVO);

                            MoaCommonResult success = MoaCommonResult.success();
                            success.setTypeList(decisionVOS);
                            return success;
                        default:
                            return MoaCommonResult.noMobile();
                    }
                case MODULE_NON_STANDARD_WORK_ORDER:
                    ActivityTaskTreeVo nonStandardActivityTask = nonStanderWorkOrderService.getTaskNodesTree(req.getApprivalId());
                    switch (nonStandardActivityTask.getCurrentTaskBpmnName()) {
                        //分公司领导审批
                        case "branch_leader":
                            //省政企领导审批
                        case "province_gov_leader":
                            //云资源部领导审批
                        case "cloud_resource_leader":
                            // 驳回到之前的任意节点
                            // 非标需要递归
                            // 1.找到当前的节点
                            // 2.它的所有parent都可以驳回到
                            List<InnerTask> rejectToTasks = getRejectToTasks(nonStandardActivityTask);
                            List<DecisionVO> decisionVOS = new ArrayList<>();
                            for (InnerTask rejectToTask : rejectToTasks) {
                                DecisionVO decisionVO = new DecisionVO();
                                decisionVO.setTypeId("reject_" + rejectToTask.getTask());
                                decisionVO.setTransition(rejectToTask.getTask());
                                decisionVO.setTypeValue("驳回到" + rejectToTask.getTaskName());
                                decisionVO.setMore(0);
                                decisionVO.setNextUser("");
                                decisionVO.setNeedSuggestion(1);
                                decisionVO.setType("none");
                                decisionVOS.add(decisionVO);
                            }
                            // 通过
                            DecisionVO passDecisionVO = new DecisionVO();
                            passDecisionVO.setTypeId("approve");
                            passDecisionVO.setTransition("");
                            passDecisionVO.setTypeValue("审批通过");
                            passDecisionVO.setMore(0);
                            passDecisionVO.setNextUser("");
                            passDecisionVO.setNeedSuggestion(1);
                            passDecisionVO.setType("none");
                            decisionVOS.add(passDecisionVO);

                            MoaCommonResult success = MoaCommonResult.success();
                            success.setTypeList(decisionVOS);
                            return success;
                        default:
                            return MoaCommonResult.noMobile();
                    }
                default:
                    return MoaCommonResult.failure("不支持的模块ID");
            }
        } catch (Exception e) {
            return MoaCommonResult.failure(SYSTEM_ERROR);
        }
    }

    /**
     * 审批
     *
     * @param req req
     * @return MoaCommonResult
     */
    @RequestMapping("/approve")
    public MoaCommonResult approve(@RequestParam("paramjson") String paramjson) {
        log.info("MOA系统对接，approve--入参：{}", paramjson);
        try {
            ApproveReq req = JSON.parseObject(paramjson, ApproveReq.class);
            OacUser oacUser = checkOAUser(req.getUserId());
            // todo 参数部分modifyDatagridList先不考虑
            switch (req.getModuleId()) {
                case MODULE_STANDARD_WORK_ORDER:
                    ActivityTaskVo standardActivityTask = standardWorkOrderService.getTaskNodes(req.getApprivalId());
                    StandardAuditWorkOrderDTO auditWorkOrderDTO = new StandardAuditWorkOrderDTO();
                    auditWorkOrderDTO.setOrderId(req.getApprivalId());
                    auditWorkOrderDTO.setAuditAdvice(req.getApprovalDesc());
                    auditWorkOrderDTO.setCurrentNodeCode(standardActivityTask.getCurrentTask());
                    auditWorkOrderDTO.setActiviteStatus(req.getTypeId().equals("approve") ? 1 : 0);
                    // reject时才有
                    auditWorkOrderDTO.setNodeCode(req.getTransition());
//                    auditWorkOrderDTO.setUserId(0L);
//                    auditWorkOrderDTO.setRegionId(0L);
//                    auditWorkOrderDTO.setRegionCode("");
//                    auditWorkOrderDTO.setRegionName("");
//                    auditWorkOrderDTO.setCatalogueDomainCode("");
//                    auditWorkOrderDTO.setDomainCode("");
//                    auditWorkOrderDTO.setCloudLeaderId(0L);
//                    auditWorkOrderDTO.setSecondLevelLeaderId(0L);
//                    auditWorkOrderDTO.setGoodsModelList(Lists.newArrayList());
//                    auditWorkOrderDTO.setAuditLogList(Lists.newArrayList());

                    standardWorkOrderService.audit(auditWorkOrderDTO, oacUser.getId());
                    return MoaCommonResult.success();
                case MODULE_RECOVERY_WORK_ORDER:
                    ActivityTaskVo recoveryActivityTask = recoveryWorkOrderService.getTaskNodes(req.getApprivalId());
                    RecoveryAuditWorkOrderDTO auditDTO = new RecoveryAuditWorkOrderDTO();
                    auditDTO.setOrderId(req.getApprivalId());
                    auditDTO.setAuditAdvice(req.getApprovalDesc());
                    auditDTO.setCurrentNodeCode(recoveryActivityTask.getCurrentTask());
                    auditDTO.setActiviteStatus(req.getTypeId().equals("approve") ? 1 : 0);
                    auditDTO.setNodeCode(req.getTransition());
                    recoveryWorkOrderService.audit(auditDTO, oacUser.getId());
                    return MoaCommonResult.success();
                case MODULE_CHANGE_WORK_ORDER:
                    ActivityTaskVo changeActivityTask = changeWorkOrderService.getTaskNodes(req.getApprivalId());
                    ChangeAuditWorkOrderDTO changeAuditDTO = new ChangeAuditWorkOrderDTO();
                    changeAuditDTO.setOrderId(req.getApprivalId());
                    changeAuditDTO.setAuditAdvice(req.getApprovalDesc());
                    changeAuditDTO.setCurrentNodeCode(changeActivityTask.getCurrentTask());
                    changeAuditDTO.setActiviteStatus(req.getTypeId().equals("approve") ? 1 : 0);
                    changeAuditDTO.setNodeCode(req.getTransition());
                    changeWorkOrderService.audit(changeAuditDTO, oacUser.getId());
                    return MoaCommonResult.success();
                case MODULE_NON_STANDARD_WORK_ORDER:
                    ActivityTaskTreeVo nonStandardActivityTask = nonStanderWorkOrderService.getTaskNodesTree(req.getApprivalId());
                    NonStanderAuditWorkOrderDTO nonStanderAuditDTO = new NonStanderAuditWorkOrderDTO();
                    nonStanderAuditDTO.setOrderId(req.getApprivalId());
                    nonStanderAuditDTO.setAuditAdvice(req.getApprovalDesc());
                    nonStanderAuditDTO.setCurrentNodeCode(nonStandardActivityTask.getCurrentTaskBpmnName());
                    nonStanderAuditDTO.setActiviteStatus(req.getTypeId().equals("approve") ? 1 : 0);
                    nonStanderAuditDTO.setNodeCode(req.getTransition());
                    nonStanderWorkOrderService.audit(nonStanderAuditDTO, oacUser.getId());
                    return MoaCommonResult.success();
                default:
                    return MoaCommonResult.failure("不支持的模块ID");
            }
        } catch (Exception e) {
            return getMoaCommonResultByException(e);
        }
    }

    @RequestMapping("/getFlowRecord")
    public MoaCommonResult<List<ApproveHistoryVO>> getFlowRecord(@RequestParam("paramjson") String paramjson) {
        log.info("MOA系统对接，getFlowRecord--入参：{}", paramjson);
        try {
            GetFlowRecordReq req = JSONObject.parseObject(paramjson, GetFlowRecordReq.class);
            OacUser oacUser = checkOAUser(req.getUserId());
            ApproveHistoryVO approveHistoryVO = getFlowRecord(req.getModuleId(), req.getApprivalId());
            return MoaCommonResult.success(Collections.singletonList(approveHistoryVO));
        } catch (BusinessException e) {
            return getMoaCommonResultByException(e);
        }
    }

    private ApproveHistoryVO getFlowRecord(String moduleId, String apprivalId) {
        ApproveHistoryVO approveHistoryVO = new ApproveHistoryVO();
        approveHistoryVO.setSecondList(Lists.newArrayList());
        LocalDateTime currentNodeStartTime = null;
        //获取审批记录
        List<WorkOrderAuthLogDTO> authLogs = workOrderAuthLogManager.list(new WorkOrderAuthLogQuery().setWorkOrderId(apprivalId));
        switch (moduleId) {
            case MODULE_STANDARD_WORK_ORDER:
                StandardWorkOrderDTO orderDTO = standardWorkOrderManager.getById(apprivalId);
                currentNodeStartTime = orderDTO.getCurrentNodeStartTime();
                ActivityTaskVo standardActivityTask = standardWorkOrderService.getTaskNodes(apprivalId);
                standardWorkOrderService.getNextTaskNodes(apprivalId, authLogs, standardActivityTask.getCurrentTaskName());
                break;
            case MODULE_RECOVERY_WORK_ORDER:
                RecoveryWorkOrderDTO recoveryWorkOrderDTO = recoveryWorkOrderManager.getById(apprivalId);
                currentNodeStartTime = recoveryWorkOrderDTO.getCurrentNodeStartTime();
                ActivityTaskVo recoveryActivityTask = recoveryWorkOrderService.getTaskNodes(apprivalId);
                recoveryWorkOrderService.getNextTaskNodes(apprivalId, authLogs, recoveryActivityTask.getCurrentTaskName());
                break;
            case MODULE_CHANGE_WORK_ORDER:
                ChangeWorkOrderDTO changeWorkOrderDTO = changeWorkOrderManager.getById(apprivalId);
                currentNodeStartTime = changeWorkOrderDTO.getCurrentNodeStartTime();
                ActivityTaskVo changeActivityTask = changeWorkOrderService.getTaskNodes(apprivalId);
                changeWorkOrderService.getNextTaskNodes(apprivalId, authLogs, changeActivityTask.getCurrentTaskName());
                break;
            case MODULE_NON_STANDARD_WORK_ORDER:
                NonStanderWorkOrderDTO nonStanderWorkOrderDTO = nonStanderWorkOrderManager.getById(apprivalId);
                currentNodeStartTime = nonStanderWorkOrderDTO.getCurrentNodeStartTime();
                ActivityTaskTreeVo nonStandardActivityTask = nonStanderWorkOrderService.getTaskNodesTree(apprivalId);
                nonStanderWorkOrderService.getNextTaskNodes(apprivalId, authLogs, nonStandardActivityTask.getCurrentTaskDisplayName());
                break;
            default:
                throw new BusinessException("不支持的模块ID");
        }
        for (WorkOrderAuthLogDTO authLogDTO : authLogs) {
            boolean notFinish = "待审核".equals(authLogDTO.getAuditResultDesc());
            if (notFinish) {
                for (UserCenterUserDTO approveUser : authLogDTO.getApproveUsers()) {
                    // 按照接口联调放要求，当可审批人是多个的时候，每个审批人都创建一个secondList
                    ApproveHistoryVO.SecondList secondList = new ApproveHistoryVO.SecondList();
                    secondList.setTitle(authLogDTO.getAuditNodeName());
                    secondList.setThirdList(Lists.newArrayList(
                            new ItemVO("环节名称", authLogDTO.getAuditNodeName()),
                            // 状态 1-代办 2-已办
                            new ItemVO("状态", "1"),
                            new ItemVO("待处理人", approveUser.getUserName()),
                            new ItemVO("已处理人", ""),
                            new ItemVO("到达时间", currentNodeStartTime == null ? "" : currentNodeStartTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))),
                            new ItemVO("处理时间", authLogDTO.getCreateTime() == null ? "" : authLogDTO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))),
                            new ItemVO("提交决策", authLogDTO.getAuditResultDesc() == null ? "" : authLogDTO.getAuditResultDesc()),
                            new ItemVO("处理意见", authLogDTO.getAdvice() == null ? "" : authLogDTO.getAdvice()),
                            new ItemVO("待处理人OA", approveUser.getLoginName()),
                            new ItemVO("已处理人OA", "")
                    ));
                    approveHistoryVO.getSecondList().add(secondList);
                }
            } else {
                ApproveHistoryVO.SecondList secondList = new ApproveHistoryVO.SecondList();
                UserCenterUserDTO userCenterUserDTO = userService.getUserById(authLogDTO.getUserId());
                secondList.setTitle(authLogDTO.getAuditNodeName());
                secondList.setThirdList(Lists.newArrayList(
                        new ItemVO("环节名称", authLogDTO.getAuditNodeName()),
                        // 状态 1-代办 2-已办
                        new ItemVO("状态", "2"),
                        new ItemVO("待处理人", userCenterUserDTO.getUserName()),
                        new ItemVO("已处理人", userCenterUserDTO.getUserName()),
                        new ItemVO("到达时间", currentNodeStartTime == null ? "" : currentNodeStartTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))),
                        new ItemVO("处理时间", authLogDTO.getCreateTime() == null ? "" : authLogDTO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))),
                        new ItemVO("提交决策", authLogDTO.getAuditResultDesc() == null ? "" : authLogDTO.getAuditResultDesc()),
                        new ItemVO("处理意见", authLogDTO.getAdvice() == null ? "" : authLogDTO.getAdvice()),
                        new ItemVO("待处理人OA", userCenterUserDTO.getLoginName()),
                        new ItemVO("已处理人OA", userCenterUserDTO.getLoginName())
                ));
                approveHistoryVO.getSecondList().add(secondList);
            }
        }
        return approveHistoryVO;
    }

    private List<InnerTask> getRejectToTasks(ActivityTaskTreeVo nonStandardActivityTask) {
        List<InnerTask> rejectToTasks = new ArrayList<>();
        TaskTreeNodeDTO root = nonStandardActivityTask.getRoot();
        getRejectToTasks(root, rejectToTasks, nonStandardActivityTask.getCurrentTaskBpmnName());
        return rejectToTasks;
    }

    private boolean getRejectToTasks(TaskTreeNodeDTO current, List<InnerTask> rejectToTasks, String currentTask) {
        if (current.getTaskBpmnName().equals(currentTask)) {
            // 如果是当前节点，返回true
            return true;
        }
        // 遍历孩子节点
        if (!CollectionUtils.isEmpty(current.getChildren())) {
            for (TaskTreeNodeDTO child : current.getChildren()) {
                boolean findCurrentTask = getRejectToTasks(child, rejectToTasks, currentTask);
                if (findCurrentTask) {
                    // 如果孩子节点中存在要找的节点，则当前节点也可以驳回到
                    InnerTask innerTask = new InnerTask();
                    innerTask.setTask(current.getTaskBpmnName());
                    innerTask.setTaskName(current.getTaskDisplayName());
                    rejectToTasks.add(innerTask);
                    // 这条线路上找到当前节点了，持续返回true
                    return true;
                }
            }
        }
        // 如果当前节点不是要找的节点，且孩子节点中也没有要找的节点，则返回false
        return false;
    }

    /**
     * 通用方法：根据指定的计数类型（pending / approved / rejected）构建响应
     *
     * @param req         请求参数
     * @param countGetter 函数：从 AuditCountVo 中提取目标计数值
     * @return JSONObject 响应
     */
    private JSONObject buildTaskCountResponse(MoaCountReq req, Function<AuditCountVo, Long> countGetter) {
        JSONObject jsonObject = new JSONObject();

        try {
            jsonObject.put("responsecode", 1);
            jsonObject.put("responsemessage", "");
            jsonObject.put("traceId", TaceIdContext.generateTraceIdGet());

            String userId = req.getUserId();
            Precondition.checkArgument(userId, "userId 不能为空");

            OacUser oacUser = checkOAUser(userId);

            ExtendAuditCountVO extendAuditCountVO = homeDataStatisticsService.statisticsOrderStatusNumber(oacUser.getId());

            String appidModuleId = req.getAppidModuleId();
            Precondition.checkArgument(appidModuleId != null && appidModuleId.contains("="),
                    "appidModuleId 格式错误，应为: system=module1|module2");

            String[] parts = appidModuleId.split("=", 2);
            String appId = parts[0];
            String modulesStr = parts[1];
            List<String> modules = Arrays.asList(modulesStr.split("\\|"));

            long totalCount = 0L;
            boolean isAllCount = Boolean.TRUE.equals(req.getIsAllCount());

            for (String module : modules) {
                AuditCountVo vo;
                if (ModuleEnum.STANDARD.getCode().equals(module)) {
                    vo = extendAuditCountVO.getWorkOrder();
                } else if (ModuleEnum.NON_STANDARD.getCode().equals(module)) {
                    vo = extendAuditCountVO.getNonStanderOrder();
                } else if (ModuleEnum.RECOVERY.getCode().equals(module)) {
                    vo = extendAuditCountVO.getRecycleOrder();
                } else if (ModuleEnum.CHANGE.getCode().equals(module)) {
                    vo = extendAuditCountVO.getChangeOrder();
                } else {
                    continue; // 忽略不支持的模块
                }

                Long count = (vo != null) ? countGetter.apply(vo) : 0L;
                totalCount += count;

                if (!isAllCount) {
                    jsonObject.put(appId + "-" + module, count);
                }
            }

            jsonObject.put("disi-allmodule", totalCount);

        } catch (Exception e) {
            log.error("请求失败：{}", e.getMessage());
            jsonObject.clear();
            jsonObject.put("responsecode", 0);
            jsonObject.put("responsemessage", "请求失败：" + e.getMessage());
            jsonObject.put("disi-allmodule", 0);
        }

        return jsonObject;
    }

    private JSONObject buildErrorResponse(Exception e) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("responsecode", 0);
        if (e instanceof BusinessException || e instanceof UniversalException) {
            jsonObject.put("responsemessage", e.getMessage());
        } else {
            jsonObject.put("responsemessage", SYSTEM_ERROR);
        }
        jsonObject.put("traceId", TaceIdContext.generateTraceIdGet());
        return jsonObject;
    }


    private MoaCommonResult<List<TaskInfoVO>> selectStandardWorkOrder(OacUser oacUser,
                                                                      String moduleId,
                                                                      String approvalCode,
                                                                      Integer pageNum,
                                                                      Integer pageSize) {
        StandardWorkOrderQuery query = new StandardWorkOrderQuery();
        query.setApprovalCode(approvalCode);
        query.setPageNum(pageNum);
        query.setPageSize(pageSize);
        PageResult<StandardWorkOrderDTO> page = standardWorkOrderService.page(query, oacUser.getId());
        List<TaskInfoVO> taskInfoVOList = new ArrayList<>(page.getRecords().size());
        for (StandardWorkOrderDTO record : page.getRecords()) {
            // 根据page构造TaskInfoVO
            TaskInfoVO taskInfoVO = new TaskInfoVO();
            taskInfoVO.setTitle(record.getOrderTitle());
            taskInfoVO.setApplyId(record.getOrderCode());
            taskInfoVO.setApprivalId(record.getId());
//            taskInfoVO.setOtherParam(null);
            taskInfoVO.setNodeName(record.getCurrentNodeName());
            taskInfoVO.setModuleName("标准工单");
            taskInfoVO.setModuleId(moduleId);
            taskInfoVO.setCreater(record.getCreatedUserName());
            taskInfoVO.setTreatStatus(null);
            taskInfoVO.setDate(record.getCurrentNodeStartTime());
            taskInfoVO.setCanBatchAgree("0");
            taskInfoVOList.add(taskInfoVO);
        }
        MoaCommonResult<List<TaskInfoVO>> result = MoaCommonResult.success(taskInfoVOList);
        result.setPageSize(page.getPageSize());
        result.setPageNo(page.getPageNum());
        result.setTotalCount(page.getTotal());
        return result;
    }

    private MoaCommonResult<List<TaskInfoVO>> selectNonStanderWorkOrder(OacUser oacUser,
                                                                        String moduleId,
                                                                        String approvalCode,
                                                                        Integer pageNum,
                                                                        Integer pageSize) {
        NonStanderWorkOrderQuery query = new NonStanderWorkOrderQuery();
        query.setApprovalCode(approvalCode);
        query.setPageNum(pageNum);
        query.setPageSize(pageSize);
        PageResult<NonStanderWorkOrderDTO> page = nonStanderWorkOrderService.page(query, oacUser.getId());
        List<TaskInfoVO> taskInfoVOList = new ArrayList<>(page.getRecords().size());
        for (NonStanderWorkOrderDTO record : page.getRecords()) {
            // 根据page构造TaskInfoVO
            TaskInfoVO taskInfoVO = new TaskInfoVO();
            taskInfoVO.setTitle(record.getOrderTitle());
            taskInfoVO.setApplyId(record.getOrderCode());
            taskInfoVO.setApprivalId(record.getId());
//            taskInfoVO.setOtherParam(null);
            taskInfoVO.setNodeName(record.getCurrentNodeName());
            taskInfoVO.setModuleName("非标准工单");
            taskInfoVO.setModuleId(moduleId);
            taskInfoVO.setCreater(record.getCreatedUserName());
            taskInfoVO.setTreatStatus(null);
            taskInfoVO.setDate(record.getCurrentNodeStartTime());
            taskInfoVO.setCanBatchAgree("0");
            taskInfoVOList.add(taskInfoVO);
        }
        MoaCommonResult<List<TaskInfoVO>> result = MoaCommonResult.success(taskInfoVOList);
        result.setPageSize(page.getPageSize());
        result.setPageNo(page.getPageNum());
        result.setTotalCount(page.getTotal());
        return result;
    }

    private MoaCommonResult<List<TaskInfoVO>> selectRecoveryWorkOrder(OacUser oacUser,
                                                                      String moduleId,
                                                                      String approvalCode,
                                                                      Integer pageNum,
                                                                      Integer pageSize) {
        RecoveryWorkOrderQuery query = new RecoveryWorkOrderQuery();
        query.setApprovalCode(approvalCode);
        query.setPageNum(pageNum);
        query.setPageSize(pageSize);
        PageResult<RecoveryWorkOrderDTO> page = recoveryWorkOrderService.page(query, oacUser.getId());
        List<TaskInfoVO> taskInfoVOList = new ArrayList<>(page.getRecords().size());
        for (RecoveryWorkOrderDTO record : page.getRecords()) {
            // 根据page构造TaskInfoVO
            TaskInfoVO taskInfoVO = new TaskInfoVO();
            taskInfoVO.setTitle(record.getOrderTitle());
            taskInfoVO.setApplyId(record.getOrderCode());
            taskInfoVO.setApprivalId(record.getId());
//            taskInfoVO.setOtherParam(null);
            taskInfoVO.setNodeName(record.getCurrentNodeName());
            taskInfoVO.setModuleName("回收工单");
            taskInfoVO.setModuleId(moduleId);
            taskInfoVO.setCreater(record.getCreatedUserName());
            taskInfoVO.setTreatStatus(null);
            taskInfoVO.setDate(record.getCurrentNodeStartTime());
            taskInfoVO.setCanBatchAgree("0");
            taskInfoVOList.add(taskInfoVO);
        }
        MoaCommonResult<List<TaskInfoVO>> result = MoaCommonResult.success(taskInfoVOList);
        result.setPageSize(page.getPageSize());
        result.setPageNo(page.getPageNum());
        result.setTotalCount(page.getTotal());
        return result;
    }

    private MoaCommonResult<List<TaskInfoVO>> selectChangeWorkOrder(OacUser oacUser,
                                                                    String moduleId,
                                                                    String approvalCode,
                                                                    Integer pageNum,
                                                                    Integer pageSize) {
        ChangeWorkOrderQuery query = new ChangeWorkOrderQuery();
        query.setApprovalCode(approvalCode);
        query.setPageNum(pageNum);
        query.setPageSize(pageSize);
        PageResult<ChangeWorkOrderDTO> page = changeWorkOrderService.page(query, oacUser.getId());
        List<TaskInfoVO> taskInfoVOList = new ArrayList<>(page.getRecords().size());
        for (ChangeWorkOrderDTO record : page.getRecords()) {
            // 根据page构造TaskInfoVO
            TaskInfoVO taskInfoVO = new TaskInfoVO();
            taskInfoVO.setTitle(record.getOrderTitle());
            taskInfoVO.setApplyId(record.getOrderCode());
            taskInfoVO.setApprivalId(record.getId());
//            taskInfoVO.setOtherParam(null);
            taskInfoVO.setNodeName(record.getCurrentNodeName());
            taskInfoVO.setModuleName("变更工单");
            taskInfoVO.setModuleId(moduleId);
            taskInfoVO.setCreater(record.getCreatedUserName());
            taskInfoVO.setTreatStatus(null);
            taskInfoVO.setDate(record.getCurrentNodeStartTime());
            taskInfoVO.setCanBatchAgree("0");
            taskInfoVOList.add(taskInfoVO);
        }
        MoaCommonResult<List<TaskInfoVO>> result = MoaCommonResult.success(taskInfoVOList);
        result.setPageSize(page.getPageSize());
        result.setPageNo(page.getPageNum());
        result.setTotalCount(page.getTotal());
        return result;
    }

    private OacUser checkOAUser(String account) {
        OacUser oacUser = iUserSelectStrategy.getUserDetail(null, account);
        Precondition.checkArgument(oacUser != null, "用户不存在");
        return oacUser;
    }

    private MoaCommonResult getMoaCommonResultByException(Exception e) {
        log.error("MOA系统对接， error message: {}", ExceptionUtils.getStackTrace(e));
        if (e instanceof BusinessException || e instanceof UniversalException) {
            return MoaCommonResult.failure(e.getMessage());
        } else {
            return MoaCommonResult.failure(SYSTEM_ERROR);
        }
    }

    /**
     * 获取标准工单详情
     *
     * @param workOrderId
     * @return
     */
    private StandardWorkOrderDetailVO getStandardWorkOrderDetail(String workOrderId) {
        StandardWorkOrderDTO dto = standardWorkOrderManager.getById(workOrderId);
        Precondition.checkArgument(dto, "工单异常不存在");
        Precondition.checkArgument(dto.getActivitiId(), "工单异常不存在流程id");
        StandardWorkOrderDetailVO vo = convert.convertDetail(dto);
        //获取资源的产品详情
        List<StandardWorkOrderProductDTO> productDTOS = standardWorkOrderProductManager
                .list(new StandardWorkOrderProductQuery().setOrderId(workOrderId));
        convert.fillDetail(vo, productDTOS, false);
        // 获取资源概览
        ResourceShowInfoDTO showInfoDTO = standardWorkOrderProductManager.selectResourceOverview(new StandardWorkOrderProductQuery().setOrderId(workOrderId));
        vo.setShowInfo(showInfoDTO);
        return vo;
    }

    /**
     * 获取变更工单详情
     *
     * @param workOrderId
     * @param userId
     * @return
     */
    private ChangeWorkOrderDetailVO getChangeWorkOrderDetail(String workOrderId, String userId) {
        OacUser oacUser = checkOAUser(userId);
        UserCenterUserDTO currentUser = new UserCenterUserDTO();
        currentUser.setId(oacUser.getId());
        List<UserCenterRoleDTO> roles = new ArrayList<>();
        for (OacRoleInfo oacRole : oacUser.getOacRoles()) {
            UserCenterRoleDTO roleDTO = new UserCenterRoleDTO();
            roleDTO.setCode(oacRole.getRoleCode());
            roles.add(roleDTO);
        }
        currentUser.setOacRoles(roles);
        ChangeWorkOrderDTO dto = changeWorkOrderManager.getById(workOrderId);
        Precondition.checkArgument(dto, "工单异常不存在");
        ChangeWorkOrderDetailVO vo = changeWorkOrderWebConvert.convertDetail(dto);
        List<ChangeWorkOrderProductDTO> productDTOS = changeWorkOrderProductManager.listByWorkOrderId(workOrderId);
        //productDTOS 直接过滤掉子产品
        productDTOS = productDTOS.stream().filter(productDTO -> productDTO.getParentProductId() == 0).collect(Collectors.toList());
        ArrayListMultimap<String, ChangeWorkOrderProductDTO> type2product =
                com.datatech.slgzt.utils.StreamUtils.toArrayListMultimap(productDTOS, ChangeWorkOrderProductDTO::getProductType);
        //获取资源表里的要调用的就是排除掉网络和vpc的
        List<Long> resDatilsIds = productDTOS.stream()
                .filter(productDTO -> !productDTO.getProductType().equals(ProductTypeEnum.VPC.getCode()))
                .filter(productDTO -> !productDTO.getProductType().equals(ProductTypeEnum.NETWORK.getCode()))
                .filter(productDTO -> productDTO.getParentProductId() == 0)
                .filter(productDTO -> ObjNullUtils.isNotNull(productDTO.getResourceDetailId()))
                .map(ChangeWorkOrderProductDTO::getResourceDetailId)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        //获取资源
        List<ResourceDetailDTO> resourceDetailDTOS = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(resDatilsIds)) {
            resourceDetailDTOS = resourceDetailManager.list(new ResourceDetailQuery().setIds(resDatilsIds));
        }
        changeWorkOrderWebConvert.fillProductDetail(vo, resourceDetailDTOS, type2product, dto.getCreatedBy(), currentUser);
        // 获取审批记录
        List<WorkOrderAuthLogDTO> authLogDTOS = workOrderAuthLogManager.list(new WorkOrderAuthLogQuery().setWorkOrderId(workOrderId));
        // 获取流程节点数据
        ActivityTaskVo activityTaskVo = changeWorkOrderService.getTaskNodes(dto.getId());
        changeWorkOrderService.getNextTaskNodes(dto.getId(), authLogDTOS, activityTaskVo.getCurrentTaskName());
        vo.setActivityTask(activityTaskVo);
        vo.setApproveRecordList(authLogDTOS);

        if (dto.getCurrentNodeCode() != null && !dto.getCurrentNodeCode().equals("autodit end")) {
            ChangeAuditWorkOrderDTO schemaInfo = changeWorkOrderService.getSchemaInfo(workOrderId);
            changeWorkOrderWebConvert.mergeSchemaInfo(vo, schemaInfo);
        }
        return vo;
    }

    /**
     * 获取回收工单详情
     *
     * @param workOrderId
     * @return
     */
    private RecoveryWorkOrderDetailVO getRecoveryWorkOrderDetail(String workOrderId) {
        RecoveryWorkOrderDTO dto = recoveryWorkOrderManager.getById(workOrderId);
        Precondition.checkArgument(dto, "工单异常不存在");
        RecoveryWorkOrderDetailVO vo = recoveryWorkOrderWebConvert.convertDetail(dto);
        List<RecoveryWorkOrderProductDTO> productDTOS = recoveryWorkOrderProductManager.listByWorkOrderId(workOrderId);
        //productDTOS 直接过滤掉子产品
        productDTOS = productDTOS.stream().filter(productDTO -> productDTO.getParentProductId() == 0).collect(Collectors.toList());
        ArrayListMultimap<String, RecoveryWorkOrderProductDTO> type2product =
                com.datatech.slgzt.utils.StreamUtils.toArrayListMultimap(productDTOS, RecoveryWorkOrderProductDTO::getProductType);
        //获取网络资源和vpc
        //获取网络id列表
        List<String> vpcIdList = com.datatech.slgzt.utils.StreamUtils.mapArrayFilterNull(type2product.get(ProductTypeEnum.VPC.getCode()), RecoveryWorkOrderProductDTO::getResourceDetailId);
        List<String> networkIdList = com.datatech.slgzt.utils.StreamUtils.mapArrayFilterNull(type2product.get(ProductTypeEnum.NETWORK.getCode()), RecoveryWorkOrderProductDTO::getResourceDetailId);
        List<String> cqIdList = com.datatech.slgzt.utils.StreamUtils.mapArrayFilterNull(type2product.get(ProductTypeEnum.CQ.getCode()), RecoveryWorkOrderProductDTO::getResourceDetailId);
        //获取资源表里的要调用的就是排除掉网络和vpc的
        List<Long> resDatilsIds = productDTOS.stream()
                .filter(productDTO -> !productDTO.getProductType().equals(ProductTypeEnum.VPC.getCode()))
                .filter(productDTO -> !productDTO.getProductType().equals(ProductTypeEnum.NETWORK.getCode()))
                .filter(productDTO -> productDTO.getParentProductId() == 0)
                .filter(productDTO -> ObjNullUtils.isNotNull(productDTO.getResourceDetailId()))
                .map(RecoveryWorkOrderProductDTO::getResourceDetailId)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        //获取资源
        List<ResourceDetailDTO> resourceDetailDTOS = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(resDatilsIds)) {
            resourceDetailDTOS = resourceDetailManager.list(new ResourceDetailQuery().setIds(resDatilsIds));
        }
        //获取vpc
        List<VpcOrderResult> vpcOrderResultList = new ArrayList<>();
        if (ObjNullUtils.isNotNull(vpcIdList)) {
            vpcOrderResultList = vpcOrderManager.listByIdList(vpcIdList);
        }
        //获取网络
        List<NetworkOrderResult> networkOrderResultList = new ArrayList<>();
        if (ObjNullUtils.isNotNull(networkIdList)) {
            networkOrderResultList = networkOrderManager.selectNetworkRecoveryList(networkIdList);
        }
        //获取容器配额
        List<ContainerQuotaDTO> cqList = new ArrayList<>();
        if (ObjNullUtils.isNotNull(cqIdList)) {
            cqList = containerQuotaManager.queryList(new ContainerQuotaQuery().setContainerIds(com.datatech.slgzt.utils.StreamUtils.toLong(cqIdList)));
        }

        recoveryWorkOrderWebConvert.fillProductDetail(vo, resourceDetailDTOS, vpcOrderResultList, networkOrderResultList, cqList, type2product);
        // 获取审批记录
        List<WorkOrderAuthLogDTO> authLogDTOS = workOrderAuthLogManager.list(new WorkOrderAuthLogQuery().setWorkOrderId(workOrderId));
        // 获取流程节点数据
        ActivityTaskVo activityTaskVo = recoveryWorkOrderService.getTaskNodes(dto.getId());
        recoveryWorkOrderService.getNextTaskNodes(dto.getId(), authLogDTOS, activityTaskVo.getCurrentTaskName());
        vo.setActivityTask(activityTaskVo);
        vo.setApproveRecordList(authLogDTOS);
        return vo;
    }

    private NonStanderWorkOrderVO getNonStanderWorkOrderDetail(String workOrderId) {
        NonStanderWorkOrderDTO dto = nonStanderWorkOrderManager.getById(workOrderId);
        Precondition.checkArgument(dto, "工单异常不存在");
        Precondition.checkArgument(dto.getActivitiId(), "工单异常不存在流程id");
        NonStanderWorkOrderVO vo = nonStanderWorkOrderWebConvert.convertDetail(dto);
        //获取资源的产品详情
        List<NonStanderWorkOrderProductDTO> productDTOS = nonStandardWorkOrderProductManager
                .list(new NonStanderWorkOrderProductQuery().setOrderId(workOrderId));
        nonStanderWorkOrderWebConvert.fillDetail(vo, productDTOS, false);
        //获取审批记录
        List<WorkOrderAuthLogDTO> authLogDTOS = workOrderAuthLogManager.list(new WorkOrderAuthLogQuery().setWorkOrderId(workOrderId));
        ActivityTaskTreeVo activityTaskVo = nonStanderWorkOrderService.getTaskNodesTree(dto.getId());
        nonStanderWorkOrderService.getNextTaskNodes(dto.getId(), authLogDTOS, activityTaskVo.getCurrentTaskDisplayName());
        nonStanderWorkOrderService.processLogRoleNames(authLogDTOS);
        vo.setActivityTask(activityTaskVo);
        vo.setApproveRecordList(authLogDTOS);
        // 获取资源概览
        ResourceShowInfoDTO showInfoDTO = nonStandardWorkOrderProductManager.selectResourceOverview(new NonStanderWorkOrderProductQuery().setOrderId(workOrderId));
        vo.setShowInfo(showInfoDTO);
        return vo;
    }

    private JSONObject loadStandardTemplate(ModuleEnum moduleEnum) {
        try {
            // 从 classpath 下的 templates/xxx.json 加载模板
            ClassPathResource resource = new ClassPathResource("/templates/test.json");
            if (ModuleEnum.STANDARD.getCode().equals(moduleEnum.getCode())) {
                resource = new ClassPathResource("templates/standard_workorder_detail.json");
            } else if (ModuleEnum.CHANGE.getCode().equals(moduleEnum.getCode())) {
                resource = new ClassPathResource("templates/change_workorder_detail.json");
            } else if (ModuleEnum.RECOVERY.getCode().equals(moduleEnum.getCode())) {
                resource = new ClassPathResource("templates/recovery_workorder_detail.json");
            } else if (ModuleEnum.NON_STANDARD.getCode().equals(moduleEnum.getCode())) {
                resource = new ClassPathResource("templates/nonstandard_workorder_detail.json");
            }
            if (!resource.exists()) {
                throw new IllegalStateException("模板文件不存在: templates/standard_workorder_detail.json");
            }

            // 读取文件内容为字符串
            String jsonContent = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);

            // 解析为 JSONObject
            return JSONObject.parseObject(jsonContent);

        } catch (IOException e) {
            throw new RuntimeException("读取工单详情模板文件失败", e);
        }
    }

    /**
     * 从 auditLogList 中提取关键审核项，转为“是/否”，并注入到 dataMap
     */
    private void enrichAuditResults(Map<String, Object> dataMap, List<SaveSchemaAuditLogDTO> auditLogList) {
        // 定义需要处理的审核项
        List<String> targetAuditTypes = ListUtil.of(
                "cloud_resources",
                "cloud_architecture",
                "business_architecture",
                "business_planning"
        );

        // 初始化默认值为“否”
        for (String type : targetAuditTypes) {
            dataMap.putIfAbsent(type, "否");
        }

        if (auditLogList != null) {
            for (SaveSchemaAuditLogDTO log : auditLogList) {
                String auditType = log.getAuditType();
                String auditResult = log.getAuditResult();

                if (auditType != null && targetAuditTypes.contains(auditType)) {
                    String displayValue = "true".equalsIgnoreCase(auditResult) ? "是" : "否";
                    dataMap.put(auditType, displayValue);
                }
            }
        }
    }

    private static final Pattern PATTERN = Pattern.compile("\\$\\{([^}]+)}");

    private void replacePlaceholdersInJson(JSONObject json, Map<String, Object> data) {
        for (Map.Entry<String, Object> entry : json.entrySet()) {
            Object val = entry.getValue();
            if (val instanceof String) {
                entry.setValue(replaceVars((String) val, data));
            } else if (val instanceof JSONObject) {
                replacePlaceholdersInJson((JSONObject) val, data);
            } else if (val instanceof List) {
                ((List<?>) val).forEach(item -> {
                    if (item instanceof JSONObject) {
                        replacePlaceholdersInJson((JSONObject) item, data);
                    }
                });
            }
        }
    }

    private String replaceVars(String text, Map<String, Object> data) {
        Matcher m = PATTERN.matcher(text);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            String key = m.group(1);
            Object value = data.get(key);
            String replacement = (value == null) ? "" : value.toString();
            // 转义 $ 和 \ 防止 replace 报错
            replacement = replacement.replace("\\", "\\\\").replace("$", "\\$");
            m.appendReplacement(sb, replacement);
        }
        m.appendTail(sb);
        return sb.toString();
    }

    private <T> JSONArray processShowInfo(JSONObject template, T workOrder, ModuleEnum moduleEnum, String workOrderId) {
        JSONObject root = JSONObject.parseObject(JSONObject.toJSONString(template));
        JSONArray resultList = root.getJSONArray("resultlist");
        for (int i = 0; i < resultList.size(); i++) {
            JSONObject tab = resultList.getJSONObject(i);
            if ("基础信息".equals(tab.getString("tabname"))) {
                // 获取 secondlist（JSONArray）
                JSONArray secondListArray = tab.getJSONArray("secondlist");
                JSONArray thirdlist1 = secondListArray.getObject(1, JSONObject.class).getJSONArray("thirdlist");
                // 转为 List<Map<String, Object>>
                List<Map> thirdlist1Map = thirdlist1.toJavaList(Map.class);
                if (ModuleEnum.STANDARD.getCode().equals(moduleEnum.getCode())) {
                    StandardWorkOrderDetailVO standardWorkOrderDetailVO = JSONObject.parseObject(JSONObject.toJSONString(workOrder), StandardWorkOrderDetailVO.class);
                    addStandardResourceToSecondList(standardWorkOrderDetailVO, thirdlist1Map);
                } else if (ModuleEnum.CHANGE.getCode().equals(moduleEnum.getCode())) {
                    ChangeWorkOrderDetailVO changeWorkOrderDetailVO = JSONObject.parseObject(JSONObject.toJSONString(workOrder), ChangeWorkOrderDetailVO.class);
                    addChangeResourceToSecondList(changeWorkOrderDetailVO, thirdlist1Map);
                } else if (ModuleEnum.RECOVERY.getCode().equals(moduleEnum.getCode())) {
                    RecoveryWorkOrderDetailVO recoveryWorkOrderDetailVO = JSONObject.parseObject(JSONObject.toJSONString(workOrder), RecoveryWorkOrderDetailVO.class);
                    addRecoveryResourceToSecondList(recoveryWorkOrderDetailVO, thirdlist1Map);
                } else if (ModuleEnum.NON_STANDARD.getCode().equals(moduleEnum.getCode())) {
                    NonStanderWorkOrderVO nonStanderWorkOrderVO = JSONObject.parseObject(JSONObject.toJSONString(workOrder), NonStanderWorkOrderVO.class);
                    addNonStandardResourceToSecondList(nonStanderWorkOrderVO, thirdlist1Map);
                }
                // 把thirdlist1Map重新设置回去
                secondListArray.getObject(1, JSONObject.class).put("thirdlist", thirdlist1Map);
            }
//            else if ("审批历史".equals(tab.getString("tabname"))) {
//                ApproveHistoryVO flowRecord = getFlowRecord(moduleEnum.getCode(), workOrderId);
//                tab.put("secondlist", flowRecord.getSecondList());
//            }
        }
        return resultList;
    }

    /**
     * 标准工单详情
     * @param standardWorkOrderDetailVO
     * @param secondList
     */
    private void addStandardResourceToSecondList(StandardWorkOrderDetailVO standardWorkOrderDetailVO, List<Map> secondList) {
        String workOrderId = standardWorkOrderDetailVO.getId();
        List<StandardWorkOrderProductDTO> standardWorkOrderProductDTOS = standardWorkOrderProductManager.list(new StandardWorkOrderProductQuery().setOrderId(workOrderId));
        List<StandardWorkOrderProductDTO> masterStandardWorkOrder = standardWorkOrderProductDTOS.stream().filter(standardOrder -> 0 == standardOrder.getParentProductId()).collect(Collectors.toList());
        // 根据产品类型分组
        Map<String, List<StandardWorkOrderProductDTO>> productMap = masterStandardWorkOrder.stream().collect(Collectors.groupingBy(StandardWorkOrderProductDTO::getProductType));
        for (String productType : productMap.keySet()) {
            // 标题
            secondList.add(createCardTitle(ProductTypeEnum.getByCode(productType).getDesc()));
            List<StandardWorkOrderProductDTO> standardOrderList = productMap.get(productType);
            if (VM_PRODUCT.contains(productType)) {
                int totalVcpu = 0, totalMemoryGb = 0, totalStorageGb = 0, totalVgpu = 0, totalBandwidth = 0;

                for (StandardWorkOrderProductDTO dto : standardOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    CloudEcsResourceModel cloudEcsResourceModel = JSON.parseObject(snapshot, CloudEcsResourceModel.class);
                    // 1. 解析 spec: "1C1GB" / "4C32GB/T4"
                    String spec = cloudEcsResourceModel.getFlavorName();
                    if (spec != null) {
                        Pattern pattern = Pattern.compile("(\\d+)C(\\d+)(?:GB|G)?", Pattern.CASE_INSENSITIVE);
                        Matcher matcher = pattern.matcher(spec.trim());
                        if (matcher.lookingAt()) { // 使用 lookingAt() 允许后面有 /GPU 部分
                            totalVcpu += Integer.parseInt(matcher.group(1));
                            totalMemoryGb += Integer.parseInt(matcher.group(2));
                        }
                    }
                    // 2. 处理系统盘
                    Integer sysDisk = cloudEcsResourceModel.getSysDiskSize();
                    if (sysDisk != null) {
                        totalStorageGb += sysDisk;
                    }

                    // 3. 解析 evsModelList: JSON array
                    List<EvsModel> evsModelList = cloudEcsResourceModel.getMountDataDiskList();
                    if (evsModelList != null) {
                        for (EvsModel evsModel : evsModelList) {
                            totalStorageGb += evsModel.getSysDiskSize();
                        }
                    }
                    // 处理带宽
                    List<EipModel> eipModelList = cloudEcsResourceModel.getEipModelList();
                    if (CollectionUtil.isNotEmpty(eipModelList)) {
                        EipModel eipModel = eipModelList.get(0);
                        totalBandwidth += eipModel.getBandwidth();
                    }
                    if (spec != null && spec.contains("T")) {
                        totalVgpu += Integer.parseInt(StringUtils.substringBetween(spec, "/", "T"));
                    }
                }

                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", standardOrderList.size()));
                secondList.add(createShowItem("vcpuNumbers", "vCPU", totalVcpu));
                secondList.add(createShowItem("ramNumbers", "内存", totalMemoryGb + "GB"));
                secondList.add(createShowItem("storageNumbers", "存储", totalStorageGb + "GB"));
                secondList.add(createShowItem("bandWidthNumbers", "带宽", totalBandwidth + "M"));
                if (totalVgpu > 0) {
                    secondList.add(createShowItem("vgpuNumbers", "vGPU", totalVgpu + "核"));
                }
            } else if (ProductTypeEnum.OBS.getCode().equals(productType)) {
                int totalStorageGb = 0;
                for (StandardWorkOrderProductDTO dto : standardOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    ObsModel obsModel = JSON.parseObject(snapshot, ObsModel.class);
                    totalStorageGb += obsModel.getStorageDiskSize();
                }
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", standardOrderList.size()));
                secondList.add(createShowItem("storageNumbers", "存储大小", totalStorageGb + "GB"));
            } else if (ProductTypeEnum.EVS.getCode().equals(productType) || ProductTypeEnum.SHARE_EVS.getCode().equals(productType)) {
                int totalStorageGb = 0;
                for (StandardWorkOrderProductDTO dto : standardOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    EvsModel evsmodel = JSON.parseObject(snapshot, EvsModel.class);
                    totalStorageGb += evsmodel.getSysDiskSize();
                }
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", standardOrderList.size()));
                secondList.add(createShowItem("storageNumbers", "存储大小", totalStorageGb + "GB"));
            } else if (ProductTypeEnum.SLB.getCode().equals(productType)) {
                int totalBandwidth = 0;
                for (StandardWorkOrderProductDTO dto : standardOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    SlbModel slbModel = JSON.parseObject(snapshot, SlbModel.class);
                    List<EipModel> eipModelList = slbModel.getEipModelList();
                    if (CollectionUtil.isNotEmpty(eipModelList)) {
                        totalBandwidth += eipModelList.get(0).getBandwidth();
                    }
                }
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", standardOrderList.size()));
                if (totalBandwidth > 0) {
                    secondList.add(createShowItem("bandWidthNumbers", "带宽", totalBandwidth + "Mbps"));
                }
            } else if (ProductTypeEnum.NAT.getCode().equals(productType)) {
                int totalBandwidth = 0;
                for (StandardWorkOrderProductDTO dto : standardOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    NatGatwayModel natGatwayModel = JSON.parseObject(snapshot, NatGatwayModel.class);
                    List<EipModel> eipModelList = natGatwayModel.getEipModelList();
                    if (CollectionUtil.isNotEmpty(eipModelList)) {
                        totalBandwidth += eipModelList.get(0).getBandwidth();
                    }
                }
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", standardOrderList.size()));
                if (totalBandwidth > 0) {
                    secondList.add(createShowItem("bandWidthNumbers", "带宽", totalBandwidth + "Mbps"));
                }
            } else if (ProductTypeEnum.EIP.getCode().equals(productType)) {
                int totalBandwidth = 0;
                for (StandardWorkOrderProductDTO dto : standardOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    EipModel eipModel = JSON.parseObject(snapshot, EipModel.class);
                    totalBandwidth += eipModel.getBandwidth();
                }
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", standardOrderList.size()));
                secondList.add(createShowItem("bandWidthNumbers", "带宽", totalBandwidth + "Mbps"));
            } else if (ProductTypeEnum.CQ.getCode().equals(productType)) {
                int totalCpu = 0, totalMemory = 0, totalGpuRatio = 0, totalGpuMemory = 0, totalGpuCore = 0;
                for (StandardWorkOrderProductDTO dto : standardOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    CQModel cqModel = JSON.parseObject(snapshot, CQModel.class);
                    totalCpu += (cqModel.getVCpus() != null) ? cqModel.getVCpus() : 0;
                    totalMemory += (cqModel.getRam() != null) ? cqModel.getRam() : 0;
                    totalGpuRatio += (cqModel.getGpuRatio() != null) ? cqModel.getGpuRatio() : 0;
                    totalGpuMemory += (cqModel.getGpuVirtualMemory() != null) ? cqModel.getGpuVirtualMemory() : 0;
                    totalGpuCore += (cqModel.getGpuCore() != null) ? cqModel.getGpuCore() : 0;
                }
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", standardOrderList.size()));
                if (totalCpu > 0) {
                    secondList.add(createShowItem("cpuNumbers", "vCPU", totalCpu + "核"));
                }
                if (totalMemory > 0) {
                    secondList.add(createShowItem("memoryNumbers", "内存", totalMemory + "G"));
                }
                if (totalGpuRatio > 0) {
                    secondList.add(createShowItem("gpuRatioNumbersTmp", "GPU算力", totalGpuRatio));
                }
                if (totalGpuMemory > 0) {
                    secondList.add(createShowItem("gpuMemoryNumbers", "GPU显存", totalGpuMemory + "G"));
                }
                if (totalGpuCore > 0) {
                    secondList.add(createShowItem("gpuCoreNumbers", "GPU卡数量", totalGpuCore));
                }
            } else if (ProductTypeEnum.NAS.getCode().equals(productType)) {
                int totalStorageGb = 0;
                for (StandardWorkOrderProductDTO dto : standardOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    NasModel nasModel = JSON.parseObject(snapshot, NasModel.class);
                    totalStorageGb += nasModel.getStorageSize();
                }
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", standardOrderList.size()));
                secondList.add(createShowItem("storageNumbers", "存储大小", totalStorageGb + "GB"));
            } else if (ProductTypeEnum.VPN.getCode().equals(productType)) {
                int totalBandwidth = 0;
                for (StandardWorkOrderProductDTO dto : standardOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    VpnModel vpnModel = JSON.parseObject(snapshot, VpnModel.class);
                    totalBandwidth += vpnModel.getBandwidth();
                }
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", standardOrderList.size()));
                secondList.add(createShowItem("bandWidthNumbers", "带宽", totalBandwidth + "M"));
            } else if (ProductTypeEnum.BLD_REDIS.getCode().equals(productType)
                    || ProductTypeEnum.BACKUP.getCode().equals(productType)
                    || ProductTypeEnum.KAFKA.getCode().equals(productType)
                    || ProductTypeEnum.PHYSICAL_MACHINE.getCode().equals(productType)
                    || ProductTypeEnum.FLINK.getCode().equals(productType)
                    || ProductTypeEnum.ES.getCode().equals(productType)) {
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", standardOrderList.size()));
            }
        }
    }

    /**
     * 变更资源详情
     *
     * @param changeWorkOrderDetailVO
     * @param secondList
     */
    private void addChangeResourceToSecondList(ChangeWorkOrderDetailVO changeWorkOrderDetailVO, List<Map> secondList) {
        String workOrderId = changeWorkOrderDetailVO.getId();
        List<ChangeWorkOrderProductDTO> changeWorkOrderProductDTOS = changeWorkOrderProductManager.listByWorkOrderId(workOrderId);
        List<ChangeWorkOrderProductDTO> masterChangeWorkOrder = changeWorkOrderProductDTOS.stream().filter(changOrder -> 0 == changOrder.getParentProductId()).collect(Collectors.toList());
        // 根据变更类型分类
        List<ChangeWorkOrderProductDTO> specChanges = new ArrayList<>(), storageChanges = new ArrayList<>(),
                bandwidthChanges = new ArrayList<>(), delays = new ArrayList<>();
        for (ChangeWorkOrderProductDTO changeWorkOrderProductDTO : masterChangeWorkOrder) {
            if (changeWorkOrderProductDTO.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                specChanges.add(changeWorkOrderProductDTO);
            }
            if (changeWorkOrderProductDTO.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                storageChanges.add(changeWorkOrderProductDTO);
            }
            if (changeWorkOrderProductDTO.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                bandwidthChanges.add(changeWorkOrderProductDTO);
            }
            if (changeWorkOrderProductDTO.getChangeType().contains(ChangeTypeEnum.DELAY.getCode())) {
                delays.add(changeWorkOrderProductDTO);
            }
        }
        // 生成类型卡片
        if (CollectionUtil.isNotEmpty(specChanges)) {
            // 标题
            secondList.add(createCardTitle(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getDesc()));
            // 同变更类型根据产品类型分组
            Map<String, List<ChangeWorkOrderProductDTO>> productMap = specChanges.stream().collect(Collectors.groupingBy(ChangeWorkOrderProductDTO::getProductType));
            for (String productType : productMap.keySet()) {
                List<ChangeWorkOrderProductDTO> list = productMap.get(productType);
                // 云主机相关逻辑一致
                if (VM_PRODUCT.contains(productType)) {
                    Map<String, List<ChangeWorkOrderProductDTO>> changeMap = list.stream().collect(Collectors.groupingBy(changeOrder -> {
                        ChangeEcsModel changeEcsModel = JSONObject.parseObject(changeOrder.getPropertySnapshot(), ChangeEcsModel.class);
                        return changeEcsModel.getVmSpec() + "-" + changeEcsModel.getChangeFlavorName();
                    }));
                    for (String s : changeMap.keySet()) {
                        List<ChangeWorkOrderProductDTO> tempList = changeMap.get(s);
                        String[] split = s.split("-");
                        String itemValue = String.format("%d个从%s变更为%s", tempList.size(), split[0], split[1]);
                        secondList.add(addChangeItem(ProductTypeEnum.getByCode(productType).getDesc(), itemValue));
                    }
                } else if (ProductTypeEnum.SLB.getCode().equals(productType)) {
                    Map<String, List<ChangeWorkOrderProductDTO>> changeMap = list.stream().collect(Collectors.groupingBy(changeOrder -> {
                        ChangeSlbModel changeSlbModel = JSONObject.parseObject(changeOrder.getPropertySnapshot(), ChangeSlbModel.class);
                        return changeSlbModel.getSlbSpec() + "-" + changeSlbModel.getChangeFlavorName();
                    }));
                    for (String s : changeMap.keySet()) {
                        List<ChangeWorkOrderProductDTO> tempList = changeMap.get(s);
                        String[] split = s.split("-");
                        String itemValue = String.format("%d个从%s变更为%s", tempList.size(), split[0], split[1]);
                        secondList.add(addChangeItem(ProductTypeEnum.SLB.getDesc(), itemValue));
                    }
                }
            }
        }
        if (CollectionUtil.isNotEmpty(storageChanges)) {
            // 标题
            secondList.add(createCardTitle(ChangeTypeEnum.STORAGE_EXPAND.getDesc()));
            // 同变更类型根据产品类型分组
            Map<String, List<ChangeWorkOrderProductDTO>> productMap = storageChanges.stream().collect(Collectors.groupingBy(ChangeWorkOrderProductDTO::getProductType));
            for (String productType : productMap.keySet()) {
                List<ChangeWorkOrderProductDTO> list = productMap.get(productType);
                // 云主机相关逻辑一致
                if (VM_PRODUCT.contains(productType)) {
                    Map<String, List<ChangeWorkOrderProductDTO>> changeMap = list.stream().collect(Collectors.groupingBy(changeOrder -> {
                        ChangeEcsModel changeEcsModel = JSONObject.parseObject(changeOrder.getPropertySnapshot(), ChangeEcsModel.class);
                        List<ChangeEvsModel> evsModelList = changeEcsModel.getEvsModelList();
                        List<String> oldSpecs = evsModelList.stream()
                                .map(evs -> Optional.ofNullable(evs.getSpec()).orElse("0GB"))
                                .sorted()
                                .collect(Collectors.toList());
                        List<String> newSpecs = evsModelList.stream()
                                .map(evs -> Optional.ofNullable(evs.getChangeVolumeSize())
                                        .map(s -> s + "GB")
                                        .orElse("0GB"))
                                .sorted()
                                .collect(Collectors.toList());
                        return String.join(",", oldSpecs) + "-" + String.join(",", newSpecs);
                    }));
                    for (String s : changeMap.keySet()) {
                        List<ChangeWorkOrderProductDTO> tempList = changeMap.get(s);
                        String[] split = s.split("-");
                        String[] sp = split[0].split(" ");
                        String itemValue = String.format("%d个从%s扩容到%s %s", tempList.size(), split[0], sp[0], split[1]);
                        secondList.add(addChangeItem(ProductTypeEnum.ECS.getDesc(), itemValue));
                    }
                } else if (ProductTypeEnum.EVS.getCode().equals(productType)) {
                    Map<String, List<ChangeWorkOrderProductDTO>> changeMap = list.stream().collect(Collectors.groupingBy(changeOrder -> {
                        ChangeEvsModel changeEvsModel = JSONObject.parseObject(changeOrder.getPropertySnapshot(), ChangeEvsModel.class);
                        return changeEvsModel.getSpec() + "-" + changeEvsModel.getChangeVolumeSize();
                    }));
                    for (String s : changeMap.keySet()) {
                        List<ChangeWorkOrderProductDTO> tempList = changeMap.get(s);
                        String[] split = s.split("-");
                        String[] sp = split[0].split(" ");
                        String itemValue = String.format("%d个从%s扩容到%s %sGB", tempList.size(), split[0], sp[0], split[1]);
                        secondList.add(addChangeItem(ProductTypeEnum.EVS.getDesc(), itemValue));
                    }
                } else if (ProductTypeEnum.OBS.getCode().equals(productType)) {
                    Map<String, List<ChangeWorkOrderProductDTO>> changeMap = list.stream().collect(Collectors.groupingBy(changeOrder -> {
                        ChangeObsModel changeObsModel = JSONObject.parseObject(changeOrder.getPropertySnapshot(), ChangeObsModel.class);
                        return changeObsModel.getObsSpec() + "-" + changeObsModel.getChangeVolumeSize();
                    }));
                    for (String s : changeMap.keySet()) {
                        List<ChangeWorkOrderProductDTO> tempList = changeMap.get(s);
                        String[] split = s.split("-");
                        String itemValue = String.format("%d个从%s扩容到OSS %sGB", tempList.size(), split[0], split[1]);
                        secondList.add(addChangeItem(ProductTypeEnum.OBS.getDesc(), itemValue));
                    }
                }
            }
        }
        if (CollectionUtil.isNotEmpty(bandwidthChanges)) {
            // 标题
            secondList.add(createCardTitle(ChangeTypeEnum.BANDWIDTH_EXPAND.getDesc()));
            // 同变更类型根据产品类型分组
            Map<String, List<ChangeWorkOrderProductDTO>> productMap = bandwidthChanges.stream().collect(Collectors.groupingBy(ChangeWorkOrderProductDTO::getProductType));
            for (String productType : productMap.keySet()) {
                List<ChangeWorkOrderProductDTO> list = productMap.get(productType);
                // 云主机相关逻辑一致
                if (VM_PRODUCT.contains(productType)) {
                    Map<String, List<ChangeWorkOrderProductDTO>> changeMap = list.stream().collect(Collectors.groupingBy(changeOrder -> {
                        ChangeEcsModel changeEcsModel = JSONObject.parseObject(changeOrder.getPropertySnapshot(), ChangeEcsModel.class);
                        ChangeEipModel changeEipModel = changeEcsModel.getEipModel();
                        return changeEipModel.getEipBandwidth() + "-" + changeEipModel.getChangeBandwidth();
                    }));
                    for (String s : changeMap.keySet()) {
                        List<ChangeWorkOrderProductDTO> tempList = changeMap.get(s);
                        String[] split = s.split("-");
                        String itemValue = String.format("%d个从%sMbps扩容到%sMbps", tempList.size(), split[0], split[1]);
                        secondList.add(addChangeItem(ProductTypeEnum.getByCode(productType).getDesc(), itemValue));
                    }
                } else if (ProductTypeEnum.EIP.getCode().equals(productType)) {
                    Map<String, List<ChangeWorkOrderProductDTO>> changeMap = list.stream().collect(Collectors.groupingBy(changeOrder -> {
                        ChangeEipProductModel changeEipProductModel = JSONObject.parseObject(changeOrder.getPropertySnapshot(), ChangeEipProductModel.class);
                        ChangeEipModel eipModel = changeEipProductModel.getEipModel();
                        return eipModel.getEipBandwidth() + "-" + eipModel.getChangeBandwidth();
                    }));
                    for (String s : changeMap.keySet()) {
                        List<ChangeWorkOrderProductDTO> tempList = changeMap.get(s);
                        String[] split = s.split("-");
                        String itemValue = String.format("%d个从%sMbps扩容到%sMbps", tempList.size(), split[0], split[1]);
                        secondList.add(addChangeItem(ProductTypeEnum.EIP.getDesc(), itemValue));
                    }
                } else if (ProductTypeEnum.SLB.getCode().equals(productType)) {
                    Map<String, List<ChangeWorkOrderProductDTO>> changeMap = list.stream().collect(Collectors.groupingBy(changeOrder -> {
                        ChangeSlbModel changeSlbModel = JSONObject.parseObject(changeOrder.getPropertySnapshot(), ChangeSlbModel.class);
                        ChangeEipModel changeEipModel = changeSlbModel.getEipModel();
                        return changeEipModel.getEipBandwidth() + "-" + changeEipModel.getChangeBandwidth();
                    }));
                    for (String s : changeMap.keySet()) {
                        List<ChangeWorkOrderProductDTO> tempList = changeMap.get(s);
                        String[] split = s.split("-");
                        String itemValue = String.format("%d个从%sMbps扩容到%sMbps", tempList.size(), split[0], split[1]);
                        secondList.add(addChangeItem(ProductTypeEnum.SLB.getDesc(), itemValue));
                    }
                }
            }
        }
        if (CollectionUtil.isNotEmpty(delays)) {
            // 标题
            secondList.add(createCardTitle(ChangeTypeEnum.DELAY.getDesc()));
            // 同变更类型根据产品类型分组
            Map<String, List<ChangeWorkOrderProductDTO>> productMap = delays.stream().collect(Collectors.groupingBy(ChangeWorkOrderProductDTO::getProductType));
            for (String productType : productMap.keySet()) {
                List<ChangeWorkOrderProductDTO> list = productMap.get(productType);
                Map<String, List<ChangeWorkOrderProductDTO>> changeMap = list.stream().collect(Collectors.groupingBy(changeOrder -> {
                    ChangeEcsModel changeEcsModel = JSONObject.parseObject(changeOrder.getPropertySnapshot(), ChangeEcsModel.class);
                    return DateUtil.format(changeEcsModel.getExpireTime(), DatePattern.NORM_DATETIME_PATTERN) + "#"
                            + DateUtil.format(DateUtils.processGoodsExpireTime(changeEcsModel.getExpireTime(), changeEcsModel.getChangeTime()), DatePattern.NORM_DATETIME_PATTERN);
                }));
                for (String s : changeMap.keySet()) {
                    List<ChangeWorkOrderProductDTO> tempList = changeMap.get(s);
                    String[] split = s.split("#");
                    String itemValue = String.format("%d个从%s到期 延期到%s到期", tempList.size(), split[0], split[1]);
                    secondList.add(addChangeItem(ProductTypeEnum.getByCode(productType).getDesc(), itemValue));
                }
            }
        }
    }

    /**
     * 回收工单详情
     * @param recoveryWorkOrderDetailVO
     * @param secondList
     */
    private void addRecoveryResourceToSecondList(RecoveryWorkOrderDetailVO recoveryWorkOrderDetailVO, List<Map> secondList) {
        String workOrderId = recoveryWorkOrderDetailVO.getId();
        List<RecoveryWorkOrderProductDTO> recoveryWorkOrderProductDTOS = recoveryWorkOrderProductManager.listByWorkOrderId(workOrderId);
        List<RecoveryWorkOrderProductDTO> masterRecoveryWorkOrder = recoveryWorkOrderProductDTOS.stream().filter(recoveryOrder -> 0 == recoveryOrder.getParentProductId()).collect(Collectors.toList());
        // 根据产品类型分组
        Map<String, List<RecoveryWorkOrderProductDTO>> productMap = masterRecoveryWorkOrder.stream().collect(Collectors.groupingBy(RecoveryWorkOrderProductDTO::getProductType));
        for (String productType : productMap.keySet()) {
            // 标题
            secondList.add(createCardTitle(ProductTypeEnum.getByCode(productType).getDesc()));
            List<RecoveryWorkOrderProductDTO> recoveryOrderList = productMap.get(productType);
            if (VM_PRODUCT.contains(productType)) {
                int totalVcpu = 0, totalMemoryGb = 0, totalStorageGb = 0, totalVgpu = 0, totalBandwidth = 0;

                for (RecoveryWorkOrderProductDTO dto : recoveryOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    RecoveryEcsModel recoveryEcsModel = JSON.parseObject(snapshot, RecoveryEcsModel.class);
                    // 1. 解析 spec: "1C1GB"
                    String spec = recoveryEcsModel.getSpec();
                    if (spec != null) {
                        Pattern pattern = Pattern.compile("(\\d+)C(\\d+)GB", Pattern.CASE_INSENSITIVE);
                        Matcher matcher = pattern.matcher(spec.trim());
                        if (matcher.matches()) {
                            totalVcpu += Integer.parseInt(matcher.group(1));
                            totalMemoryGb += Integer.parseInt(matcher.group(2));
                        }
                    }

                    // 2. 解析 sysDisk: "HDD 50GB"
                    String sysDisk = recoveryEcsModel.getSysDisk();
                    if (sysDisk != null) {
                        totalStorageGb += extractDiskSizeGb(sysDisk);
                    }

                    // 3. 解析 evsModelList: JSON array
                    List<RecoveryEvsModel> evsModelList = recoveryEcsModel.getEvsModelList();
                    if (evsModelList != null) {
                        for (RecoveryEvsModel recoveryEvsModel : evsModelList) {
                            totalStorageGb += extractDiskSizeGb(recoveryEvsModel.getDataDisk());
                        }
                    }
                    // 解析 带宽: "1Mbps"
                    RecoveryEipModel eipModel = recoveryEcsModel.getEipModel();
                    if (eipModel != null && eipModel.getBandwidth() != null) {
                        totalBandwidth += extractBandwidthMbps(eipModel.getBandwidth());
                    }
                    if (spec != null && spec.contains("T")) {
                        totalVgpu += Integer.parseInt(StringUtils.substringBetween(spec, "/", "T"));
                    }

                }

                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", recoveryOrderList.size()));
                secondList.add(createShowItem("vcpuNumbers", "vCPU", totalVcpu));
                secondList.add(createShowItem("ramNumbers", "内存", totalMemoryGb + "GB"));
                if (totalStorageGb > 0) {
                    secondList.add(createShowItem("storageNumbers", "存储", totalStorageGb + "GB"));
                }
                if (totalBandwidth > 0) {
                    secondList.add(createShowItem("bandWidthNumbers", "带宽", totalBandwidth + "Mbps"));
                }
                if (totalVgpu > 0) {
                    secondList.add(createShowItem("vgpuNumbers", "vGPU", totalVgpu));
                }
            } else if (ProductTypeEnum.OBS.getCode().equals(productType)) {
                int totalStorageGb = 0;
                for (RecoveryWorkOrderProductDTO dto : recoveryOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    RecoveryObsModel recoveryObsModel = JSON.parseObject(snapshot, RecoveryObsModel.class);
                    // 1. 解析 spec: "OSS 10GB"
                    String spec = recoveryObsModel.getSpec();
                    if (spec != null) {
                        totalStorageGb += extractDiskSizeGb(spec);
                    }
                }
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", recoveryOrderList.size()));
                secondList.add(createShowItem("storageNumbers", "存储大小", totalStorageGb + "GB"));
            } else if (ProductTypeEnum.EVS.getCode().equals(productType) || ProductTypeEnum.SHARE_EVS.getCode().equals(productType)) {
                int totalStorageGb = 0;
                for (RecoveryWorkOrderProductDTO dto : recoveryOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    RecoveryEvsModel recoveryEvsModel = JSON.parseObject(snapshot, RecoveryEvsModel.class);
                    // 1. 解析 spec: "OSS 10GB"
                    String dataDisk = recoveryEvsModel.getDataDisk();
                    if (dataDisk != null) {
                        totalStorageGb += extractDiskSizeGb(dataDisk);
                    }
                }
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", recoveryOrderList.size()));
                secondList.add(createShowItem("storageNumbers", "存储大小", totalStorageGb + "GB"));
            } else if (ProductTypeEnum.SLB.getCode().equals(productType)) {
                int totalBandwidth = 0;
                for (RecoveryWorkOrderProductDTO dto : recoveryOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    RecoverySlbModel recoverySlbModel = JSON.parseObject(snapshot, RecoverySlbModel.class);
                    RecoveryEipModel eipModel = recoverySlbModel.getEipModel();
                    // 1. 解析 带宽: "1Mbps"
                    if (eipModel != null && eipModel.getBandwidth() != null) {
                        totalBandwidth += extractBandwidthMbps(eipModel.getBandwidth());
                    }
                }
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", recoveryOrderList.size()));
                if (totalBandwidth > 0) {
                    secondList.add(createShowItem("bandWidthNumbers", "带宽", totalBandwidth + "Mbps"));
                }
            } else if (ProductTypeEnum.NAT.getCode().equals(productType)) {
                int totalBandwidth = 0;
                for (RecoveryWorkOrderProductDTO dto : recoveryOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    RecoveryNatModel recoveryNatModel = JSON.parseObject(snapshot, RecoveryNatModel.class);
                    RecoveryEipModel eipModel = recoveryNatModel.getEipModel();
                    // 1. 解析 带宽: "1Mbps"
                    if (eipModel != null && eipModel.getBandwidth() != null) {
                        totalBandwidth += extractBandwidthMbps(eipModel.getBandwidth());
                    }
                }
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", recoveryOrderList.size()));
                if (totalBandwidth > 0) {
                    secondList.add(createShowItem("bandWidthNumbers", "带宽", totalBandwidth + "Mbps"));
                }
            } else if (ProductTypeEnum.EIP.getCode().equals(productType)) {
                int totalBandwidth = 0;
                for (RecoveryWorkOrderProductDTO dto : recoveryOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    RecoveryEipModel recoveryEipModel = JSON.parseObject(snapshot, RecoveryEipModel.class);
                    // 1. 解析 带宽: "1Mbps"
                    totalBandwidth += extractBandwidthMbps(recoveryEipModel.getBandwidth());
                }
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", recoveryOrderList.size()));
                secondList.add(createShowItem("bandWidthNumbers", "带宽", totalBandwidth + "Mbps"));
            } else if (ProductTypeEnum.CQ.getCode().equals(productType)) {
                int totalCpu = 0, totalMemory = 0, totalGpuRatio = 0, totalGpuMemory = 0, totalGpuCore = 0;
                for (RecoveryWorkOrderProductDTO dto : recoveryOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    RecoveryCqModel recoveryCqModel = JSON.parseObject(snapshot, RecoveryCqModel.class);
                    // 1. 解析 带宽: "1Mbps"
                    totalCpu += recoveryCqModel.getVCpus();
                    totalMemory += recoveryCqModel.getRam();
                    totalGpuRatio += recoveryCqModel.getGpuRatio();
                    totalGpuMemory += recoveryCqModel.getGpuVirtualMemory();
                    totalGpuCore += recoveryCqModel.getGpuCore();
                }
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", recoveryOrderList.size()));
                if (totalCpu > 0) {
                    secondList.add(createShowItem("cpuNumbers", "vCPU", totalCpu + "核"));
                }
                if (totalMemory > 0) {
                    secondList.add(createShowItem("memoryNumbers", "内存", totalMemory + "G"));
                }
                if (totalGpuRatio > 0) {
                    secondList.add(createShowItem("gpuRatioNumbersTmp", "GPU算力", totalGpuRatio));
                }
                if (totalGpuMemory > 0) {
                    secondList.add(createShowItem("gpuMemoryNumbers", "GPU显存", totalGpuMemory + "G"));
                }
                if (totalGpuCore > 0) {
                    secondList.add(createShowItem("gpuCoreNumbers", "GPU卡数量", totalGpuCore));
                }
            } else if (ProductTypeEnum.NAS.getCode().equals(productType)) {
                int totalStorageGb = 0;
                for (RecoveryWorkOrderProductDTO dto : recoveryOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    RecoveryNasModel recoveryNasModel = JSON.parseObject(snapshot, RecoveryNasModel.class);
                    // 1. 解析 spec: "10GB"
                    String dataDisk = recoveryNasModel.getStorageSize();
                    if (dataDisk != null) {
                        totalStorageGb += extractDiskSizeGb(dataDisk);
                    }
                }
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", recoveryOrderList.size()));
                secondList.add(createShowItem("storageNumbers", "存储大小", totalStorageGb + "GB"));
            } else if (ProductTypeEnum.VPN.getCode().equals(productType)) {
                int totalBandwidth = 0;
                for (RecoveryWorkOrderProductDTO dto : recoveryOrderList) {
                    String snapshot = dto.getPropertySnapshot();
                    RecoveryVpnModel recoveryVpnModel = JSON.parseObject(snapshot, RecoveryVpnModel.class);
                    totalBandwidth += recoveryVpnModel.getBandwidth();
                }
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", recoveryOrderList.size()));
                secondList.add(createShowItem("bandWidthNumbers", "带宽", totalBandwidth + "M"));
            } else if (ProductTypeEnum.BLD_REDIS.getCode().equals(productType)
                    || ProductTypeEnum.BACKUP.getCode().equals(productType)
                    || ProductTypeEnum.KAFKA.getCode().equals(productType)
                    || ProductTypeEnum.PHYSICAL_MACHINE.getCode().equals(productType)
                    || ProductTypeEnum.FLINK.getCode().equals(productType)
                    || ProductTypeEnum.ES.getCode().equals(productType)) {
                // 添加结果到 secondList
                secondList.add(createShowItem("resourceNumbers", "数量", recoveryOrderList.size()));
            }
        }
    }

    /**
     * 非标工单详情
     * @param nonStanderWorkOrderVO
     * @param secondList
     */
    private void addNonStandardResourceToSecondList(NonStanderWorkOrderVO nonStanderWorkOrderVO, List<Map> secondList) {
        String workOrderId = nonStanderWorkOrderVO.getId();
        List<NonStanderWorkOrderProductDTO> nonStanderWorkOrderProductDTOS = nonStandardWorkOrderProductManager.list(new NonStanderWorkOrderProductQuery().setOrderId(workOrderId));
        List<NonStanderWorkOrderProductDTO> masterNonStanderWorkOrder = nonStanderWorkOrderProductDTOS.stream().filter(nonStandardOrder -> 0 == nonStandardOrder.getParentProductId()).collect(Collectors.toList());
        // 根据产品类型分组
        Map<String, List<NonStanderWorkOrderProductDTO>> productMap = masterNonStanderWorkOrder.stream().collect(Collectors.groupingBy(NonStanderWorkOrderProductDTO::getProductType));
        for (String productType : productMap.keySet()) {
            // 标题
            secondList.add(createCardTitle(ProductTypeEnum.getByCode(productType).getDesc()));
            List<NonStanderWorkOrderProductDTO> nonStandardOrders = productMap.get(productType);
            // 添加结果到 secondList
            secondList.add(createShowItem("resourceNumbers", "数量", nonStandardOrders.size()));
        }
    }


    private Map<String, Object> createCardTitle(String title) {
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("ismore", "0");
        map.put("type", "cardTitle");
        map.put("cannull", "true");
        map.put("itemid", "$$formTitle3");
        map.put("itemvalue", title);
        map.put("itemname", title);
        map.put("itemText", title);
        return map;
    }

    private Map<String, Object> createShowItem(String fieldId, String fieldName, Object value) {
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("ismore", "0");
        map.put("type", "show");
        map.put("cannull", "true");
        map.put("itemid", fieldId);
        map.put("itemvalue", value != null ? value.toString() : "");
        map.put("itemname", fieldName);
        return map;
    }

    private Map<String, Object> addChangeItem(String itemName, String template) {
        Map<String, Object> map = new HashMap<>();
        map.put("ismore", "0");
        map.put("type", "show");
        map.put("cannull", "true");
        map.put("itemid", "changeItem"); // 统一 id
        map.put("itemvalue", template);
        map.put("itemname", itemName);
        map.put("editflag", "no");
        map.put("itemText", template);
        return map;
    }

    private int extractDiskSizeGb(String diskStr) {
        if (diskStr == null) {
            return 0;
        }
        String str = diskStr.trim();

        // 先尝试匹配带 GB 的（更精确）
        Pattern withGb = Pattern.compile("(\\d+)\\s*GB", Pattern.CASE_INSENSITIVE);
        Matcher m1 = withGb.matcher(str);
        if (m1.find()) {
            return Integer.parseInt(m1.group(1));
        }

        // 再尝试匹配纯数字（假设单位是 GB）
        Pattern pureNumber = Pattern.compile("^\\d+$");
        Matcher m2 = pureNumber.matcher(str);
        if (m2.matches()) {
            return Integer.parseInt(str);
        }

        return 0;
    }

    private int extractBandwidthMbps(String str) {
        if (str == null) return 0;
        // 匹配：数字（可含小数） + 可选空格 + Mbps（不区分大小写）
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("([\\d.]+)\\s*MBPS?", java.util.regex.Pattern.CASE_INSENSITIVE);
        java.util.regex.Matcher matcher = pattern.matcher(str.trim());
        if (matcher.find()) {
            try {
                double value = Double.parseDouble(matcher.group(1));
                return (int) Math.round(value); // 四舍五入转整数
                // 或用 (int) value 直接截断
            } catch (Exception e) {
                return 0;
            }
        }
        return 0;
    }
}
