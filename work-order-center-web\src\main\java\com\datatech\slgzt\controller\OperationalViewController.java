package com.datatech.slgzt.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.config.OperationalViewProperties;
import com.datatech.slgzt.convert.OperationalReportLogWebConvert;
import com.datatech.slgzt.convert.OperationalReportWebConvert;
import com.datatech.slgzt.convert.OperationalViewWebConvert;
import com.datatech.slgzt.convert.PerformanceAggregateWebConvert;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.PerformanceAggregateManager;
import com.datatech.slgzt.manager.RegionCodeManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.query.OperationalReportLogQuery;
import com.datatech.slgzt.model.query.OperationalReportQuery;
import com.datatech.slgzt.model.query.OperationalViewQuery;
import com.datatech.slgzt.model.req.GetEventListReq;
import com.datatech.slgzt.model.req.GetLogListReq;
import com.datatech.slgzt.model.req.operational.*;
import com.datatech.slgzt.model.vo.OperationalReportLogVO;
import com.datatech.slgzt.model.vo.OperationalReportVO;
import com.datatech.slgzt.model.vo.OperationalViewVO;
import com.datatech.slgzt.model.vo.operationalView.AuditLogVO;
import com.datatech.slgzt.service.OperationalReportLogService;
import com.datatech.slgzt.service.OperationalReportService;
import com.datatech.slgzt.service.OperationalViewService;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Array;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.HttpResult;
import com.ejlchina.okhttps.OkHttps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 运维控制器
 */
@Slf4j
@RestController
@RequestMapping("/operational")
public class OperationalViewController {

    @Resource
    private OperationalViewProperties properties;

    @Resource
    private OperationalViewService operationalViewService;
    @Resource
    private OperationalViewWebConvert convert;

    @Resource
    private OperationalReportService operationalReportService;

    @Resource
    private OperationalReportLogService operationalReportLogService;

    @Resource
    private OperationalReportWebConvert operationalReportWebConvert;

    @Resource
    private OperationalReportLogWebConvert operationalReportLogWebConvert;

    @Resource
    private OperationalViewWebConvert operationalViewWebConvert;

    @Resource
    private PerformanceAggregateManager performanceAggregateManager;

    @Resource
    private PerformanceAggregateWebConvert performanceAggregateWebConvert;

    @Resource
    private RegionCodeManager regionCodeManager;

    @RequestMapping("/getEventList")
    public CommonResult<PageResult<Object>> getEventList(@RequestBody GetEventListReq req) {
        // startTime不能为null
        Precondition.checkArgument(req.getStartTime(), "开始时间不能为空");

        // 请求头需携带以下参数：
        //timeStamp 时间戳 单位毫秒与服务器前后时间差不得超过1分钟
        //appId 应⽤ID我们提供
        //appSecret 应⽤秘钥 我们提供
        //sign 签名：MD5(timeStamp+appId+appSecret).toUpperCase
        String curTimestamp = String.valueOf(System.currentTimeMillis());
        String sign = getMD5(curTimestamp + properties.getEventAppId() + properties.getEventAppSecret()).toUpperCase();

        Mapper dataMapper = OkHttps.sync(properties.getEventUrl() + "/cloud/open/event/api/getEventList")
                .bodyType(OkHttps.JSON)
                .addHeader("timeStamp", curTimestamp)
                .addHeader("appId", properties.getEventAppId())
                .addHeader("appSecret", properties.getEventAppSecret())
                .addHeader("sign", sign)
                .setBodyPara(JSON.toJSONString(req))
                .post()
                .getBody()
                .toMapper();
        // {
        //    "success": true,
        //    "code": 0,
        //    "msg": null,
        //    "data": [{
        //        "eventName": "测试四级事件",
        //        "eventId": "2025121299001",
        //        "eventFromSource": "2",
        //        "eventType": "",
        //        "techstack": "安全设备",
        //        "createTime": "2025-12-12 16:09:49",
        //        "eventStatus": 4,
        //        "eventStatusName": "事件闭环",
        //        "eventCloseTime": "2025-12-12 16:18:17",
        //        "lastTake": "508000",
        //        "eventGroup": 50,
        //        "eventGroupName": "数据库",
        //        "resourcePools": "网络云-大区金华可信2",
        //        "city": null,
        //        "cloud": "网络云大区",
        //        "level": "四级"
        //    }],
        //    "decive": null
        //}

        String success = dataMapper.getString("success");
        Precondition.checkArgument("true".equals(success), "请求失败, " + dataMapper.getString("msg"));
        Array data = dataMapper.getArray("data");
        JSONArray dataArray = JSON.parseArray(data.toString());
        if (CollectionUtils.isEmpty(dataArray)) {
            return CommonResult.success(new PageResult<>());
        }
        // 分页
        int fromIndex = (req.getPageNum() - 1) * req.getPageSize();
        int toIndex = Math.min(fromIndex + req.getPageSize(), data.size());
        List<Object> dataArray1 = dataArray.subList(fromIndex, toIndex);
        PageResult<Object> pageResult = new PageResult<>(dataArray1, req.getPageNum(), req.getPageSize(), data.size());
        return CommonResult.success(pageResult);
    }

    private String getMD5(String str) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
            return byte2Hex(messageDigest.digest());
        } catch (Exception e) {
            log.error("认证计算签名失败, {}", ExceptionUtils.getStackTrace(e));
            throw new RuntimeException("认证计算签名失败" + e.getMessage());
        }
    }

    /**
     * 将byte转为16进制
     */
    private static String byte2Hex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        String temp;
        for (byte aByte : bytes) {
            temp = Integer.toHexString(aByte & 0xFF);
            if (temp.length() == 1) {
                // 1得到一位的进行补0操作
                sb.append("0");
            }
            sb.append(temp);
        }
        return sb.toString();
    }

    @PostMapping("/getLogs")
    public CommonResult<PageResult<AuditLogVO>> getLogs(@RequestBody GetLogListReq req) {
        // 参数校验
        Precondition.checkArgument(req.getResourceType(), "资源类型不能为空");
        //Precondition.checkArgument(req.getStartTime(), "开始时间不能为空");
        if (Objects.isNull(req.getStartTime())) {
            req.setStartTime(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -7), DatePattern.NORM_DATETIME_PATTERN));
        }

        DateTime startTime = DateUtil.parse(req.getStartTime(), DatePattern.NORM_DATETIME_PATTERN);
        Precondition.checkArgument(startTime.isAfter(DateUtil.offsetDay(DateUtil.date(), -7)), "开始时间不能早于7天前（当前仅支持查询最近7天日志）");

        // 执行查询
        return CommonResult.success(searchFromLogCenter(req));
    }

//    /**
//     * 获取告警信息
//     * @param allParams
//     * @param startTime
//     * @param endTime
//     * @param pageNum
//     * @param pageSize
//     * @return
//     */
//    @GetMapping("/getAlarms")
//    private CommonResult<PageResult<AlarmInfoVO>> getAlarms(@RequestParam Map<String, String> allParams,
//                                                            @RequestParam(required = false) String startTime,
//                                                            @RequestParam(required = false) String endTime,
//                                                            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
//                                                            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
//        PageResult<AlarmInfoDTO> pages = operationalViewService.getAlarms(allParams, startTime, endTime, pageNum, pageSize);
//        return CommonResult.success(PageWarppers.box(pages, convert::convert));
//    }

    private PageResult<AuditLogVO> searchFromLogCenter(GetLogListReq req) {
        // 分页处理
        int page = req.getPageNum() != null && req.getPageNum() >= 1 ? req.getPageNum() : 1;
        int size = req.getPageSize() != null && req.getPageSize() > 0 ? Math.min(req.getPageSize(), 100) : 10;
        int from = (page - 1) * size;

        // 结束时间默认为当前时间
        String endTime = req.getEndTime();
        if (endTime == null || endTime.trim().isEmpty()) {
            endTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        }

        String techStackZh = req.getResourceType();
        String startTime = req.getStartTime();
        String logContent = req.getLogContent();
        String deviceName = req.getDeviceName();
        // 技术栈映射
        String techStack = getTechStackEn(techStackZh);
        String vendorEn = getVendorEn(techStackZh);
        String indexPattern = "auditlog_" + techStack + "_" + vendorEn + "_*";

        // 时间格式转换
        String startIso = convertToIso8601(startTime);
        String endIso = convertToIso8601(endTime);

        // 构造 bool 查询
        List<String> mustClauses = new ArrayList<>();
//        mustClauses.add("{ \"match\": { \"tech_stack\": \"" + techStackZh + "\" } }");
        mustClauses.add("{ \"range\": { \"operation_time\": { \"gte\": \"" + startIso + "\", \"lte\": \"" + endIso + "\" } } }");

        if (StringUtils.isNotBlank(deviceName)) {
            mustClauses.add("{ \"match_phrase\": { \"device_name\": \"" + deviceName + "\" } }");
        }

        if (StringUtils.isNotBlank(logContent)) {
            mustClauses.add("{ \"match_phrase\": { \"operation_log\": \"" + logContent + "\" } }");
        }

        String queryJson = "{\n" +
                "  \"track_total_hits\": true,\n" +
                "  \"query\": {\n" +
                "    \"bool\": {\n" +
                "      \"must\": [\n" +
                String.join(",\n", mustClauses) + "\n" +
                "      ]\n" +
                "    }\n" +
                "  },\n" +
                "  \"from\": " + from + ",\n" +
                "  \"size\": " + size + ",\n" +
                "  \"sort\": [\n" +
                "    { \"operation_time\": { \"order\": \"desc\" } }\n" +
                "  ]\n" +
                "}";

        // 构造完整 URL
        String url = properties.getLogUrl() + indexPattern + "/_search";

        // 添加 Basic Auth（OkHttps 支持 header 设置）
        HttpResult result = OkHttps.sync(url)
                .addHeader("Authorization", "Basic " + Base64.getEncoder().encodeToString(
                        (properties.getLogUsername() + ":" + properties.getLogPassword()).getBytes(StandardCharsets.UTF_8)))
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.parseObject(queryJson))
                .post();


        // 检查状态码
        if (result.getStatus() >= 400) {
            String errorMsg = result.getBody().toString();
            throw new RuntimeException("日志中心返回错误 [HTTP " + result.getStatus() + "]: " + errorMsg);
        }

        // 解析字符串
        List<AuditLogVO> logs = new ArrayList<>();

        JSONObject root = JSON.parseObject(result.getBody().toString());
        JSONObject hitsObj = root.getJSONObject("hits");

        long total = 0;
        if (hitsObj != null) {
            JSONObject totalObj = hitsObj.getJSONObject("total");
            if (totalObj != null) {
                total = totalObj.getLongValue("value"); // 关键：获取 total.value
            }
            JSONArray hits = hitsObj.getJSONArray("hits");
            if (hits != null) {
                for (int i = 0; i < hits.size(); i++) {
                    JSONObject hit = hits.getJSONObject(i);
                    JSONObject source = hit.getJSONObject("_source");
                    AuditLogVO log = new AuditLogVO();
                    log.setOperationLog(source.getString("operation_log"));
                    log.setOperationTime(source.getString("operation_time"));
                    log.setDeviceType(source.getString("tech_stack"));
                    log.setDeviceName(source.getString("device_name"));
                    log.setCloudPlatform(source.getString("cloud"));
                    log.setResourcePool(source.getString("resource_pool"));
                    log.setLogVendor(source.getString("log_vendor"));
                    // 日志级别、模块
                    logs.add(log);
                }
            }
        }
        // 计算总页数（向上取整）
        long pages = (total + size - 1) / size;
        PageResult<AuditLogVO> pageResult = new PageResult<>();
        pageResult.setRecords(logs);
        pageResult.setPageNum(page);
        pageResult.setPageSize(size);
        pageResult.setTotal(total);
        pageResult.setPages(pages);
        return pageResult;
    }

    // ===== 工具方法 =====

    @SneakyThrows
    private String convertToIso8601(String input) {
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = inputFormat.parse(input);
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        return outputFormat.format(date);
    }

    private String getTechStackEn(String zh) {
        switch (zh) {
            case "云主机":
                return "virtualmachine";
            case "云电脑":
                return "cloudcompute";
            case "存储":
                return "storage";
            case "网络":
                return "network";
            case "云OS":
                return "cloudos";
            case "容器":
                return "docker";
            case "数据库":
                return "database";
            case "中间件":
                return "paas";
            case "物理机":
                return "physicalmachine";
            case "云管平台":
                return "cloudmanager";
            default:
                throw new IllegalArgumentException("不支持的资源类型: " + zh);
        }
    }

    private String getVendorEn(String zh) {
        switch (zh) {
            case "云主机":
            case "物理机":
            case "云OS":
            case "存储":
                return "h3c";
            case "云电脑":
            case "云管平台":
            case "中间件":
            case "数据库":
            case "容器":
                return "snc";
            case "网络":
                return "datatech";
            default:
                return "unknown";
        }
    }

    // ==================== 运维报表策略相关接口 ====================

    /**
     * 创建运维报表
     */
    @PostMapping("/report/create")
    public CommonResult<Long> createOperationalReport(@RequestBody OperationalReportCreateReq req) {
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        req.setUserId(currentUserId);
        OperationalReportDTO dto = operationalReportWebConvert.req2dto(req);
        Long id = operationalReportService.createOperationalReport(dto);
        return CommonResult.success(id);
    }

    /**
     * 删除运维报表
     */
    @PostMapping("/report/delete/{id}")
    public CommonResult<Void> deleteOperationalReport(@PathVariable Long id) {
        operationalReportService.deleteOperationalReportById(id);
        return CommonResult.success();
    }

    /**
     * 分页查询运维报表列表
     */
    @PostMapping("/report/page")
    public CommonResult<PageResult<OperationalReportVO>> pageOperationalReports(@RequestBody OperationalReportPageReq req) {
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        req.setUserId(currentUserId);
        OperationalReportQuery query = operationalReportWebConvert.pageReq2Query(req);
        PageResult<OperationalReportDTO> pageResult = operationalReportService.pageOperationalReports(query);
        PageResult<OperationalReportVO> voPageResult = new PageResult<>();
        BeanUtils.copyProperties(pageResult, voPageResult);
        voPageResult.setRecords(operationalReportWebConvert.dto2vo(pageResult.getRecords()));
        return CommonResult.success(voPageResult);
    }

    /**
     * 启用运维报表
     */
    @PostMapping("/report/enable/{id}")
    public CommonResult<Void> enableOperationalReport(@PathVariable Long id) {
        operationalReportService.enableOperationalReport(id);
        return CommonResult.success();
    }

    /**
     * 禁用运维报表
     */
    @PostMapping("/report/disable/{id}")
    public CommonResult<Void> disableOperationalReport(@PathVariable Long id) {
        operationalReportService.disableOperationalReport(id);
        return CommonResult.success();
    }

    // ==================== 运维报表日志相关接口 ====================

    /**
     * 分页查询运维报表日志列表
     */
    @PostMapping("/reportLog/page")
    public CommonResult<PageResult<OperationalReportLogVO>> pageOperationalReportLogs(@RequestBody OperationalReportLogPageReq req) {
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        req.setUserId(currentUserId);
        OperationalReportLogQuery query = operationalReportLogWebConvert.pageReq2Query(req);
        PageResult<OperationalReportLogDTO> pageResult = operationalReportLogService.pageOperationalReportLogs(query);
        PageResult<OperationalReportLogVO> voPageResult = new PageResult<>();
        BeanUtils.copyProperties(pageResult, voPageResult);
        voPageResult.setRecords(operationalReportLogWebConvert.dto2vo(pageResult.getRecords()));
        return CommonResult.success(voPageResult);
    }

    @RequestMapping("/reportLog/{id}/export")
    public void exportResourceDetail(@PathVariable Long id, HttpServletResponse response) {
        OperationalReportLogDTO dto = operationalReportLogService.getOperationalReportLogById(id);
        downloadFile(response, dto.getReportUrl(), dto.getName() + ".xlsx");
    }

    /**
     * 根据ID查询运维报表日志
     */
    @GetMapping("/reportLog/get/{id}")
    public CommonResult<OperationalReportLogVO> getOperationalReportLog(@PathVariable Long id) {
        OperationalReportLogDTO dto = operationalReportLogService.getOperationalReportLogById(id);
        if (dto == null) {
            return CommonResult.success(null);
        }
        OperationalReportLogVO vo = operationalReportLogWebConvert.dto2vo(dto);
        return CommonResult.success(vo);
    }

    /**
     * 删除运维报表日志
     */
    @PostMapping("/reportLog/delete/{id}")
    public CommonResult<Void> deleteOperationalReportLog(@PathVariable Long id) {
        operationalReportLogService.deleteOperationalReportLogById(id);
        return CommonResult.success();
    }

    @PostMapping("/reportLog/generationReportLog")
    public CommonResult<Void> generationReportLog(@RequestBody OperationalReportLogCreateReq req) {
        operationalReportService.generateOperationalReportManually(operationalReportWebConvert.createReq2Opm(req));
        return CommonResult.success();
    }

    @GetMapping("/reportLog/callReportLog")
    public CommonResult<Void> callReportLog() {
        operationalReportService.autoGenerateOperationalReport();
        return CommonResult.success();
    }

    @GetMapping("/getTaTaiUrlWithToken")
    public CommonResult<String> getTaTaiUrlWithToken() {
        return CommonResult.success(operationalReportService.getTaTaiUrlWithToken());
    }

    // ==================== 运维视图相关接口 ====================

    /**
     * 创建运维视图
     */
    @PostMapping("/view/create")
    public CommonResult<Long> createOperationalView(@RequestBody OperationalViewCreateReq req) {
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        req.setUserId(currentUserId);
        Precondition.checkArgument(req.getName(), "视图名称不能为空");
        OperationalViewDTO dto = operationalViewWebConvert.req2dto(req);
        Long id = operationalViewService.createOperationalView(dto);
        return CommonResult.success(id);
    }

    /**
     * 更新运维视图
     */
    @PostMapping("/view/update")
    public CommonResult<Void> updateOperationalView(@RequestBody OperationalViewUpdateReq req) {
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        req.setUserId(currentUserId);
        OperationalViewDTO dto = operationalViewWebConvert.req2dto(req);
        operationalViewService.updateOperationalView(dto);
        return CommonResult.success();
    }

    /**
     * 删除运维视图
     */
    @PostMapping("/view/delete/{id}")
    public CommonResult<Void> deleteOperationalView(@PathVariable Long id) {
        operationalViewService.deleteOperationalViewById(id);
        return CommonResult.success();
    }

    /**
     * 分页查询运维视图列表
     */
    @PostMapping("/view/page")
    public CommonResult<PageResult<OperationalViewVO>> pageOperationalViews(@RequestBody OperationalViewPageReq req) {
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        req.setUserId(currentUserId);
        OperationalViewQuery query = operationalViewWebConvert.pageReq2Query(req);
        PageResult<OperationalViewDTO> pageResult = operationalViewService.pageOperationalViews(query);
        PageResult<OperationalViewVO> voPageResult = new PageResult<>();
        BeanUtils.copyProperties(pageResult, voPageResult);
        voPageResult.setRecords(operationalViewWebConvert.dto2vo(pageResult.getRecords()));
        return CommonResult.success(voPageResult);
    }

    // ==================== 性能数据聚合相关接口 ====================

    /**
     * 聚合虚拟机性能数据
     */
    @PostMapping("/performance/vmAggregate")
    public CommonResult<List<VMResourcePerformanceDTO>> aggregateVmPerformance(@RequestBody PerformanceAggregateReq req) {
        if (CollectionUtils.isEmpty(req.getRegionCodes()) && StringUtils.isNotBlank(req.getDomainCode())) {
            List<RegionCodeDTO> list = regionCodeManager.list(req.getDomainCode(), null);
            req.setRegionCodes(list.stream().map(RegionCodeDTO::getRegionCode).collect(Collectors.toList()));
        }
        List<VMResourcePerformanceDTO> result = performanceAggregateManager.aggregateVmPerformance(
                req.getStartTime(), req.getEndTime(), req.getRegionCodes());
        return CommonResult.success(performanceAggregateWebConvert.vmDtos2Response(result));
    }

    /**
     * 聚合带宽性能数据
     */
    @PostMapping("/performance/bandwidthAggregate")
    public CommonResult<List<CKBandwidthPerformanceDTO>> aggregateBandwidthPerformance(@RequestBody PerformanceAggregateReq req) {
        if (CollectionUtils.isEmpty(req.getRegionCodes()) && StringUtils.isNotBlank(req.getDomainCode())) {
            List<RegionCodeDTO> list = regionCodeManager.list(req.getDomainCode(), null);
            req.setRegionCodes(list.stream().map(RegionCodeDTO::getRegionCode).collect(Collectors.toList()));
        }
        List<CKBandwidthPerformanceDTO> result = performanceAggregateManager.aggregateBandwidthPerformance(
                req.getStartTime(), req.getEndTime(), req.getRegionCodes());
        return CommonResult.success(performanceAggregateWebConvert.bandwidthDtos2Response(result));
    }

    /**
     * 分页查询虚拟机性能列表
     */
    @PostMapping("/performance/listVmPerformancePage")
    public CommonResult<PageResult<VMResourcePerformanceDTO>> listVmPerformancePage(@RequestBody PerformanceListPageReq req) {
        if (CollectionUtils.isEmpty(req.getRegionCodes()) && StringUtils.isNotBlank(req.getDomainCode())) {
            List<RegionCodeDTO> list = regionCodeManager.list(req.getDomainCode(), null);
            req.setRegionCodes(list.stream().map(RegionCodeDTO::getRegionCode).collect(Collectors.toList()));
        }
        PageResult<VMResourcePerformanceDTO> result = performanceAggregateManager.listVmPerformancePage(
                req.getPageNum(), req.getPageSize(), req.getStartTime(), req.getEndTime(), req.getRegionCodes());
        return CommonResult.success(result);
    }

    /**
     * 聚合虚拟机性能数据总计
     */
    @PostMapping("/performance/vmAggregateTotal")
    public CommonResult<VMResourcePerformanceStringDTO> aggregateVmPerformanceTotal(@RequestBody PerformanceAggregateReq req) {
        if (CollectionUtils.isEmpty(req.getRegionCodes()) && StringUtils.isNotBlank(req.getDomainCode())) {
            List<RegionCodeDTO> list = regionCodeManager.list(req.getDomainCode(), null);
            req.setRegionCodes(list.stream().map(RegionCodeDTO::getRegionCode).collect(Collectors.toList()));
        }
        VMResourcePerformanceStringDTO result = performanceAggregateManager.aggregateVmPerformanceTotal(
                req.getStartTime(), req.getEndTime(), req.getRegionCodes());
        return CommonResult.success(result);
    }

    /**
     * 聚合带宽性能数据总计
     */
    @PostMapping("/performance/bandwidthAggregateTotal")
    public CommonResult<CKBandwidthPerformanceDTO> aggregateBandwidthPerformanceTotal(@RequestBody PerformanceAggregateReq req) {
        if (CollectionUtils.isEmpty(req.getRegionCodes()) && StringUtils.isNotBlank(req.getDomainCode())) {
            List<RegionCodeDTO> list = regionCodeManager.list(req.getDomainCode(), null);
            req.setRegionCodes(list.stream().map(RegionCodeDTO::getRegionCode).collect(Collectors.toList()));
        }
        CKBandwidthPerformanceDTO result = performanceAggregateManager.aggregateBandwidthPerformanceTotal(
                req.getStartTime(), req.getEndTime(), req.getRegionCodes());
        return CommonResult.success(result);
    }


    private void downloadFile(HttpServletResponse response, String exportPath, String fileName) {
        try {
            // 以流的形式下载文件。
            InputStream fis = new BufferedInputStream(Files.newInputStream(Paths.get(exportPath)));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            //清空response
            response.reset();
            //设置response响应头
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            // 添加Content-Length
            File file = new File(exportPath);
            response.setContentLength((int) file.length());
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(buffer);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("文件下载失败：{}", ExceptionUtils.getStackTrace(e));
        }
    }

}
