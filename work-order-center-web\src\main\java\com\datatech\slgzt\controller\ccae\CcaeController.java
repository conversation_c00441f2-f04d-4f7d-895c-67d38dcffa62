package com.datatech.slgzt.controller.ccae;

import com.alibaba.fastjson.JSONArray;
import com.datatech.slgzt.config.CcaeProperties;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.CcaeManager;
import com.datatech.slgzt.manager.CcaeTaskManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.CcaeTaskDTO;
import com.datatech.slgzt.model.query.CcaeTaskQuery;
import com.datatech.slgzt.model.req.ccae.IdReq;
import com.datatech.slgzt.model.req.ccae.CcaeResIdReq;
import com.datatech.slgzt.model.req.ccae.CcaeTaskCreateReq;
import com.datatech.slgzt.model.to.CcaeComputeDeviceTO;
import com.datatech.slgzt.model.to.CcaeNetworkDeviceTO;
import com.datatech.slgzt.model.to.CcaeTemplateTO;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.utils.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务系统列表
 *
 * @Author: liupeihan
 * @Date: 2025/3/13
 */

@RestController
@RequestMapping("/ccae")
public class CcaeController {
    @Resource
    private CcaeProperties ccaeProperties;
    @Resource
    private CcaeManager ccaeManager;
    @Resource
    private CcaeTaskManager ccaeTaskManager;

    /**
     * 模板-获取所有检查项
     *
     * @return 检查项
     */
    @PostMapping("/template/checkItems/getAll")
    public CommonResult<JSONArray> getCheckItems() throws NoSuchAlgorithmException, KeyManagementException {
        return CommonResult.success(ccaeManager.listCheckItem());
    }

    /**
     * 模板-获取所有
     *
     * @return 模板
     */
    @PostMapping("/template/getAll")
    public CommonResult<List<CcaeTemplateTO>> getTemplate() throws NoSuchAlgorithmException, KeyManagementException {
        return CommonResult.success(ccaeManager.listTemplate());
    }

    @PostMapping("/template/get")
    public CommonResult<CcaeTemplateTO> getTemplate(@RequestBody CcaeResIdReq req) throws NoSuchAlgorithmException, KeyManagementException {
        return CommonResult.success(ccaeManager.getTemplateByResId(req.getResId()));
    }

    /**
     * 设备-计算域
     */
    @PostMapping("/device/computeGetAll")
    public CommonResult<List<CcaeComputeDeviceTO>> getComputeDeviceList() throws NoSuchAlgorithmException, KeyManagementException {
        return CommonResult.success(ccaeManager.listComputeDevice());
    }

    /**
     * 设备-网络域
     */
    @PostMapping("/device/networkGetAll")
    public CommonResult<List<CcaeNetworkDeviceTO>> getNetworkDeviceList() throws NoSuchAlgorithmException, KeyManagementException {
        return CommonResult.success(ccaeManager.listNetworkDevice());
    }

    /**
     * 任务-创建
     *
     * @return 评估
     */
    @PostMapping("/task/create")
    public CommonResult createTask(@RequestBody CcaeTaskCreateReq req) throws NoSuchAlgorithmException, KeyManagementException {
        // 获取模板
        CcaeTemplateTO template = ccaeManager.getTemplateByResId(req.getTemplateResId());
        CcaeTaskDTO ccaeTaskDTO = new CcaeTaskDTO();
        if (req.getComputeList() != null) {
            ccaeTaskDTO.setDeviceScope("主机");
            ccaeTaskDTO.setCcaeId(ccaeManager.createComputeTask(req.getTaskName(), template.getName(), "SerialNumber", req.getComputeList()));
        } else {
            ccaeTaskDTO.setDeviceScope("网络");
            ccaeTaskDTO.setCcaeId(ccaeManager.createNetworkTask(req.getTaskName(), template.getItems(), req.getNetworkIpList()));
        }
        ccaeTaskDTO.setPassItemCount(0);
        ccaeTaskDTO.setCheckItemCount(template.getItems().size());
        ccaeTaskDTO.setTemplateCategory(template.getCategory());
        ccaeTaskDTO.setExecuteMethod(0);
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        ccaeTaskDTO.setCreatorId(currentUser.getId());
        ccaeTaskDTO.setCreatorName(currentUser.getUserName());
        ccaeTaskDTO.setComputeList(req.getComputeList());
        ccaeTaskDTO.setNetworkIpList(req.getNetworkIpList());

        ccaeTaskManager.save(ccaeTaskDTO);
        return CommonResult.success();
    }

    /**
     * 任务-详情-巡检列表
     *
     * @return 设备列表
     */
    @PostMapping("/task/detail/getDeviceList")
    public CommonResult<List> getDeviceList(@RequestBody IdReq req) throws Exception {
        CcaeTaskDTO byId = ccaeTaskManager.getById(req.getId());
        if (byId.getDeviceScope().equals("主机")) {
            return CommonResult.success(ccaeManager.listComputeNeDevices().stream()
                    .filter(item -> byId.getComputeList().contains(item.getSn()))
                    .collect(Collectors.toList())
            );
        } else {
            return CommonResult.success(ccaeManager.listNetworkDevice().stream()
                    .filter(item -> byId.getNetworkIpList().contains(item.getIpAddress()))
                    .collect(Collectors.toList())
            );
        }
    }

    /**
     * 任务-分页查询
     */
    @PostMapping("/task/page")
    public CommonResult<PageResult<CcaeTaskDTO>> pageTasks(@RequestBody CcaeTaskQuery req) {
        return CommonResult.success(ccaeTaskManager.page(req));
    }

    /**
     * 任务-详情-报告
     * /ccae/task/detail/getReport
     */
    @PostMapping("/task/detail/getReport")
    public CommonResult<String> getReport(@RequestBody IdReq req) throws NoSuchAlgorithmException, KeyManagementException {
        CcaeTaskDTO taskDTO = ccaeTaskManager.getById(req.getId());
//        ccaeManager.getReport(taskDTO.getCcaeId());
        return CommonResult.success();
    }


}

