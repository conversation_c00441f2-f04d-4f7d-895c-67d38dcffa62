package com.datatech.slgzt.controller.chanage;

import cn.hutool.core.collection.CollectionUtil;
import com.datatech.slgzt.annotation.OperationLog;
import com.datatech.slgzt.convert.ChangeWorkOrderWebConvert;
import com.datatech.slgzt.enums.AuthorityCodeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum;
import com.datatech.slgzt.enums.bpmn.ActivityEnum;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.bpmn.ActivityTaskVo;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.dto.ChangeWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.TenantDTO;
import com.datatech.slgzt.model.dto.WorkOrderAuthLogDTO;
import com.datatech.slgzt.model.dto.business.BusinessService;
import com.datatech.slgzt.model.dto.change.ChangeAuditWorkOrderDTO;
import com.datatech.slgzt.model.dto.change.ChangeWorkOrderDTO;
import com.datatech.slgzt.model.home.AuditCountVo;
import com.datatech.slgzt.model.opm.ChangeWorkOrderCreateOpm;
import com.datatech.slgzt.model.query.ChangeWorkOrderQuery;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.query.WorkOrderAuthLogQuery;
import com.datatech.slgzt.model.req.change.*;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.change.ChangeWorkOrderDetailVO;
import com.datatech.slgzt.model.vo.change.ChangeWorkOrderVO;
import com.datatech.slgzt.service.bpmn.BaseActivity;
import com.datatech.slgzt.service.change.ChangeWorkOrderService;
import com.datatech.slgzt.service.usercenter.IUserSelectStrategy;
import com.datatech.slgzt.utils.*;
import com.datatech.slgzt.warpper.PageWarppers;
import com.google.common.collect.ArrayListMultimap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 更变工单控制器
 *
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月30日 13:47:40
 */
@Slf4j
@RestController
@RequestMapping("/changeWorkOrder")
public class ChangeWorkOrderController {

    @Resource
    private ChangeWorkOrderWebConvert convert;

    @Resource
    private ChangeWorkOrderService changeWorkOrderService;

    @Resource
    private BusinessService businessService;

    @Resource
    private TenantManager tenantManager;

    @Resource
    private BaseActivity baseActivity;

    @Resource
    private ChangeWorkOrderManager changeWorkOrderManager;

    @Resource
    private ChangeWorkOrderProductManager changeWorkOrderProductManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private VpcOrderManager vpcOrderManager;

    @Resource
    private NetworkOrderManager networkOrderManager;

    @Resource
    private WorkOrderAuthLogManager workOrderAuthLogManager;

    @Resource
    private IUserSelectStrategy iUserSelectStrategy;

    /**
     * 草稿
     */
    @RequestMapping(value = "/draft", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> draft(@RequestBody ChangeWorkOrderCreateReq req) {
        //基础校验
        Precondition.checkArgument(req.getOrderTitle(), "工单标题不能为空");
        CmpAppDTO cmpAppDTO = businessService.getById(req.getBusinessSystemId());
        Precondition.checkArgument(cmpAppDTO, "找不到对应的业务系统");
        Precondition.checkArgument(cmpAppDTO.getTenantId(), "找不到对应的业务系统");
        TenantDTO tenantDTO = tenantManager.getById(cmpAppDTO.getTenantId());
        Precondition.checkArgument(tenantDTO, "找不到对应的租户");
        Precondition.checkArgument(tenantDTO.getCustomNo(), "找不到对应的客户号");
        Precondition.checkArgument(tenantDTO.getBillId(), "找不到对应的计费号");
        //校验基础入参
        ChangeWorkOrderCreateOpm opm = convert.convert(req);
        opm.setTenantId(cmpAppDTO.getTenantId());
        opm.setTenantName(tenantDTO.getName());
        opm.setBusinessSystemCode(cmpAppDTO.getSystemCode());
        opm.setBillId(tenantDTO.getBillId());
        opm.setCustomNo(tenantDTO.getCustomNo());
        opm.setCanDraft(true);
        changeWorkOrderService.checkDraftChangeOrderCanCreate(opm);
        String orderId = changeWorkOrderService.createChangeWorkOrder(opm);
        return CommonResult.success(orderId);
    }
    /**
     * 创建更变工单
     */
    @RequestMapping("/create")
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(description = "创建更变工单", operationType = "CREATE")
    public CommonResult<String> create(@RequestBody ChangeWorkOrderCreateReq req) {
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        //先判断该用户与该业务系统关联的租户是否存在绑定关系
        Boolean isBound = businessService.checkUserAndBusinessSystemIsBound(req.getBusinessSystemId(), currentUserId);
        Precondition.checkArgument(isBound, "当前用户与该业务系统所关联租户不存在绑定关系！");
        //存在绑定关系，再判断该用户是否是租户管理员
        Boolean b = iUserSelectStrategy.checkOperateRole(currentUserId);
        Precondition.checkArgument(!b, "该用户为维护组角色，无法发起资源变更！");
        Precondition.checkArgument(req.getOrderTitle(), "工单标题不能为空");
        Precondition.checkArgument(req.getDepartmentName(), "所属部门名称不能为空");
        Precondition.checkArgument(req.getManufacturer(), "厂家名称不能为空");
        Precondition.checkArgument(req.getManufacturerContacts(), "厂家联系人不能为空");
        Precondition.checkArgument(req.getManufacturerMobile(), "厂家联系电话不能为空");
        Precondition.checkArgument(req.getLevelThreeLeaderName(), "三级业务部门领导名称不能为空");
        Precondition.checkArgument(req.getLevelThreeLeaderId(), "三级业务部门领导Id不能为空");
        Precondition.checkArgument(req.getBusinessDepartLeaderId(), "二级业务部门领导Id不能为空");
        Precondition.checkArgument(req.getBusinessDepartLeaderName(), "二级业务部门领导名称不能为空");
        Precondition.checkArgument(req.getOrderDesc(), "订单描述不能为空");
        Precondition.checkArgument(req.getBusinessSystemId(), "业务系统id不能为空");
        Precondition.checkArgument(req.getBusinessSystemName(), "业务系统名称不能为空");
        Precondition.checkArgument(req.getModuleId(), "业务系统所属模块id不能为空");
        Precondition.checkArgument(req.getModuleName(), "业务系统所属模块名称不能为空");
        //Precondition.checkArgument(req.getBureauUserName(), "局方负责人名称不能为空");
        //Precondition.checkArgument(req.getResourceApplyFiles(), "资源上云说明书不能为空");

        CmpAppDTO cmpAppDTO = businessService.getById(req.getBusinessSystemId());
        Precondition.checkArgument(cmpAppDTO, "找不到对应的业务系统");
        Precondition.checkArgument(cmpAppDTO.getTenantId(), "找不到对应的业务系统");
        TenantDTO tenantDTO = tenantManager.getById(cmpAppDTO.getTenantId());
        Precondition.checkArgument(tenantDTO, "找不到对应的租户");
        Precondition.checkArgument(tenantDTO.getCustomNo(), "找不到对应的客户号");
        Precondition.checkArgument(tenantDTO.getBillId(), "找不到对应的计费号");
        //校验基础入参
        ChangeWorkOrderCreateOpm opm = convert.convert(req);
        opm.setTenantId(cmpAppDTO.getTenantId());
        opm.setTenantName(tenantDTO.getName());
        opm.setBusinessSystemCode(cmpAppDTO.getSystemCode());
        opm.setBillId(tenantDTO.getBillId());
        opm.setCustomNo(tenantDTO.getCustomNo());
        // 对变更的工单进行校验，是否允许变更创建
        changeWorkOrderService.checkChangeOrderCanCreate(opm);
        String orderId = changeWorkOrderService.createChangeWorkOrder(opm);
        return CommonResult.success(orderId);
    }

    /**
     * 变更列表查询
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public CommonResult<PageResult<ChangeWorkOrderVO>> page(@RequestBody ChangeWorkOrderPageReq req) {
        Precondition.checkArgument(req.getApprovalCode(), "查询审批节点的类型不能为空");
        ChangeWorkOrderQuery orderQuery = convert.pageReq2Query(req);
        Long userId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(userId, "当前用户未登录");
        PageResult<ChangeWorkOrderDTO> page = changeWorkOrderService.page(orderQuery, userId);
        PageResult<ChangeWorkOrderVO> box = PageWarppers.box(page, convert::dto2vo);
        List<ChangeWorkOrderVO> records = box.getRecords();
        fillOrderRevoke(records, userId);
        return CommonResult.success(box);
    }

    private void fillOrderRevoke(List<ChangeWorkOrderVO> records, Long userId) {
        records.forEach(item -> {
            //判断当前用户是否是工单的创建者 并且工单到达 资源回收环节
            if (!item.getCreatedBy().equals(userId)) {
                item.setCanRevoke(false);
                return;
            }
            //获取当前节点
            ActivityTaskVo taskNodes = baseActivity.taskNodes(item.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_CHANGE_PROCESS);
            if (null == taskNodes.getCurrentTask()) {
                item.setCanRevoke(false);
            }
            if ("autodit end".equals(taskNodes.getCurrentTask())) {
                item.setCanRevoke(false);
            }
            //如果当前节点是网络创建或者资源开通就不能撤了
            List<String> codeList = Arrays.asList(
                    ActivitiStatusEnum.ALARM_SUPPRESSION.getNode(),
                    ActivitiStatusEnum.SHUTDOWN.getNode(),
                    ActivitiStatusEnum.RESOURCE_CHANGE.getNode(),
                    ActivitiStatusEnum.TENANT_TASK.getNode());
            if (codeList.contains(taskNodes.getCurrentTask())) {
                item.setCanRevoke(false);
            }
        });
    }

    /**
     * 列表数量统计
     */
    @RequestMapping(value = "/auditCount", method = RequestMethod.POST)
    public CommonResult<AuditCountVo> auditCount(@RequestBody ChangeWorkOrderQuery query) {
        Long userId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(userId, "获取当前用户信息失败");
        query.setUserId(String.valueOf(userId));
        AuditCountVo countVo = changeWorkOrderService.orderCount(query);
        return CommonResult.success(countVo);
    }

    /**
     * 撤回
     */
    @RequestMapping("/cancel")
    public CommonResult<String> cancel(@RequestBody ChangeWorkOrderCancelReq req) {
        Precondition.checkArgument(req.getWorkOrderId(), "工单ID不能为空");
        changeWorkOrderService.cancel(req.getWorkOrderId());
        return CommonResult.success(req.getWorkOrderId());
    }

    /**
     * 详情
     */
    @RequestMapping("/detail")
    public CommonResult<ChangeWorkOrderDetailVO> detail(@RequestBody ChangeWorkOrderDetailReq req) {
        Precondition.checkArgument(req.getWorkOrderId(), "工单ID不能为空");
        //查询用户是否存在草稿缓存
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        ChangeWorkOrderDTO dto = changeWorkOrderManager.getById(req.getWorkOrderId());
        Precondition.checkArgument(dto, "工单异常不存在");
        ChangeWorkOrderDetailVO vo = convert.convertDetail(dto);
        List<ChangeWorkOrderProductDTO> productDTOS = changeWorkOrderProductManager.listByWorkOrderId(req.getWorkOrderId());
        //productDTOS 直接过滤掉子产品
        productDTOS = productDTOS.stream().filter(productDTO -> productDTO.getParentProductId() == 0).collect(Collectors.toList());
        ArrayListMultimap<String, ChangeWorkOrderProductDTO> type2product =
                StreamUtils.toArrayListMultimap(productDTOS, ChangeWorkOrderProductDTO::getProductType);
        //获取资源表里的要调用的就是排除掉网络和vpc的
        List<Long> resDatilsIds = productDTOS.stream()
                .filter(productDTO->!productDTO.getProductType().equals(ProductTypeEnum.VPC.getCode()))
                .filter(productDTO->!productDTO.getProductType().equals(ProductTypeEnum.NETWORK.getCode()))
                .filter(productDTO->productDTO.getParentProductId()==0)
                .filter(productDTO -> ObjNullUtils.isNotNull(productDTO.getResourceDetailId()))
                .map(ChangeWorkOrderProductDTO::getResourceDetailId)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        //获取资源
        List<ResourceDetailDTO> resourceDetailDTOS=new ArrayList<>();
        if (CollectionUtil.isNotEmpty(resDatilsIds)) {
            resourceDetailDTOS = resourceDetailManager.list(new ResourceDetailQuery().setIds(resDatilsIds));
        }
        convert.fillProductDetail(vo, resourceDetailDTOS, type2product, dto.getCreatedBy(), currentUser);
        // 获取审批记录
        List<WorkOrderAuthLogDTO> authLogDTOS = workOrderAuthLogManager.list(new WorkOrderAuthLogQuery().setWorkOrderId(req.getWorkOrderId()));
        // 获取流程节点数据
        ActivityTaskVo activityTaskVo = changeWorkOrderService.getTaskNodes(dto.getId());
        changeWorkOrderService.getNextTaskNodes(dto.getId(), authLogDTOS, activityTaskVo.getCurrentTaskName());
        vo.setActivityTask(activityTaskVo);
        vo.setApproveRecordList(authLogDTOS);

        if (dto.getCurrentNodeCode() != null && !dto.getCurrentNodeCode().equals("autodit end")) {
            ChangeAuditWorkOrderDTO schemaInfo = changeWorkOrderService.getSchemaInfo(req.getWorkOrderId());
            convert.mergeSchemaInfo(vo, schemaInfo);
        }
        return CommonResult.success(vo);
    }

    /**
     * 告警屏蔽
     */
    @RequestMapping("/alarmSuppress")
    public CommonResult<String> alarmSuppress(@RequestBody AlarmSuppressReq req) {
        Precondition.checkArgument(req.getProductIds(), "产品不能为空");
        changeWorkOrderService.alarmSuppress(req.getProductIds());
        return CommonResult.success();
    }

    /**
     * 租户确认
     */
    @RequestMapping("/tenantConfirm")
    public CommonResult<String> tenantConfirm(@RequestBody AlarmSuppressReq req) {
        Precondition.checkArgument(req.getProductIds(), "产品不能为空");
        changeWorkOrderService.tenantConfirm(req.getProductIds());
        return CommonResult.success();
    }

    /**
     * 审核
     */
    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> approve(@RequestBody @Validated ChangeWorkOrderAuditReq req) {
        Precondition.checkArgument(req.getOrderId(), "工单ID不能为空");
        Precondition.checkArgument(req.getActiviteStatus(), "审核操作状态不能为空");
        if (req.getActiviteStatus().equals(ActivityEnum.ActivityStatusEnum.REJECT.getCode())) {
            Precondition.checkArgument(req.getNodeCode(), "驳回时请选择驳回指定节点标识");
        }
        //判断当前节点是哪个如果是第一个节点需要额外
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        ChangeAuditWorkOrderDTO auditDTO = convert.req2DTO(req);
        changeWorkOrderService.audit(auditDTO, currentUser.getId());
        return CommonResult.success("操作成功");
    }

    /**
     * 导出
     */
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public void export(@RequestBody ChangeWorkOrderPageReq req, HttpServletResponse response) {
        Precondition.checkArgument(req.getApprovalCode(), "查询审批节点的类型不能为空");
        ChangeWorkOrderQuery orderQuery = convert.pageReq2Query(req);
        orderQuery.setPageSize(10000);
        Long userId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(userId, "当前用户未登录");
        PageResult<ChangeWorkOrderDTO> page = changeWorkOrderService.page(orderQuery, userId);
        PageResult<ChangeWorkOrderVO> box = PageWarppers.box(page, convert::dto2vo);
        List<ChangeWorkOrderVO> records = box.getRecords();
        String filePath = System.getProperty("user.dir") + "/export/" + UUID.randomUUID().toString().replaceAll("-", "") + ".xlsx";
        FileUtils.doExport(records, ChangeWorkOrderVO.class, filePath);
        downloadFile(response, filePath, "变更工单列表.xlsx");
    }

    private void downloadFile(HttpServletResponse response, String exportPath, String fileName) {
        try {
            // 以流的形式下载文件。
            InputStream fis = new BufferedInputStream(Files.newInputStream(Paths.get(exportPath)));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            //清空response
            response.reset();
            //设置response响应头
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            // 添加Content-Length
            File file = new File(exportPath);
            response.setContentLength((int) file.length());
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(buffer);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("文件下载失败：" + e);
        }
    }
}
