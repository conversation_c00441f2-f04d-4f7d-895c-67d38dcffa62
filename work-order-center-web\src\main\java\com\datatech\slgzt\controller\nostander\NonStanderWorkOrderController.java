package com.datatech.slgzt.controller.nostander;

import com.datatech.slgzt.annotation.OperationLog;
import com.datatech.slgzt.convert.NonStanderWorkOrderWebConvert;
import com.datatech.slgzt.enums.MethodPathEnum;
import com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum;
import com.datatech.slgzt.enums.bpmn.ActivityEnum;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.NonStanderWorkOrderProductManager;
import com.datatech.slgzt.manager.NonStanderWorkOrderManager;
import com.datatech.slgzt.manager.WorkOrderAuthLogManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.bpmn.ActivityTaskTreeVo;
import com.datatech.slgzt.model.bpmn.TaskTreeNodeDTO;
import com.datatech.slgzt.model.dto.NonStanderWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.NonStanderWorkOrderDTO;
import com.datatech.slgzt.model.dto.WorkOrderAuthLogDTO;
import com.datatech.slgzt.model.opm.FlavorCreateOpm;
import com.datatech.slgzt.model.opm.ProductGeneralCheckOpm;
import com.datatech.slgzt.model.query.NonStanderWorkOrderProductQuery;
import com.datatech.slgzt.model.dto.nonstandard.NonStanderAuditWorkOrderDTO;
import com.datatech.slgzt.model.home.AuditCountVo;
import com.datatech.slgzt.model.query.NonStanderWorkOrderQuery;
import com.datatech.slgzt.model.query.WorkOrderAuthLogQuery;
import com.datatech.slgzt.model.req.nonstander.NonStanderWorkOrderDetailReq;
import com.datatech.slgzt.model.req.nonstander.NonStanderWorkOrderAuditReq;
import com.datatech.slgzt.model.req.nonstander.NonStanderWorkOrderCancelReq;
import com.datatech.slgzt.model.req.nonstander.NonStanderWorkOrderCreateReq;
import com.datatech.slgzt.model.req.nonstander.NonStanderWorkOrderPageReq;
import com.datatech.slgzt.model.resourcce.ResourceShowInfoDTO;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.change.ChangeWorkOrderVO;
import com.datatech.slgzt.model.vo.nonstander.NonStanderWorkOrderVO;
import com.datatech.slgzt.service.ProductGeneralCheckService;
import com.datatech.slgzt.service.UserService;
import com.datatech.slgzt.service.bpmn.BaseActivity;
import com.datatech.slgzt.service.nostander.NonStanderWorkOrderService;
import com.datatech.slgzt.utils.*;
import com.datatech.slgzt.warpper.PageWarppers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.Set;
import java.util.function.Function;

/**
 * 非标订工单-控制层
 *
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月03日 18:19:03
 */
@RestController
@RequestMapping("/nonStanderOrder")
@Slf4j
public class NonStanderWorkOrderController {


    @Resource
    private NonStanderWorkOrderWebConvert convert;

    @Resource
    private NonStanderWorkOrderManager nonStanderWorkOrderManager;

    @Resource
    private NonStanderWorkOrderService nonStanderWorkOrderService;

    @Resource
    private NonStanderWorkOrderProductManager nonStandardWorkOrderProductManager;

    @Resource
    private WorkOrderAuthLogManager workOrderAuthLogManager;

    @Resource
    private UserHelper userHelper;

    @Resource
    private UserService userService;

    @Resource
    private ProductGeneralCheckService productGeneralCheckService;

    @Resource
    private BaseActivity baseActivity;

    /**
     * 创建非标工单
     */
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(description = "创建非标工单", operationType = "CREATE")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public CommonResult<String> createNonStandardOrder(@RequestBody NonStanderWorkOrderCreateReq req) {
        preCheckWorkOrderReq(req);
        NonStanderWorkOrderDTO dto = convert.convert(req);
        String requestPath = MethodPathEnum.RESOURCE_OPEN.getPath();
        if (StringUtils.isNotEmpty(req.getId())) {
            // 重新提交
            requestPath = MethodPathEnum.RESOURCE_RESUBMIT_OPEN.getPath();
            // 重新提交将部分字段设置为null
            // nonStanderWorkOrderManager.delete(req.getId());
        } else {
            //预创建获取id
            String standerWorkOrderId = nonStanderWorkOrderManager.createNoStanderWorkOrder(new NonStanderWorkOrderDTO());
            dto.setId(standerWorkOrderId);
        }
        processOrderProduct(dto);
        UserCenterUserDTO oacUserInfo = userHelper.getCurrentUser();
        nonStanderWorkOrderService.createNonStandardOrderAndStartProcess(dto, requestPath, oacUserInfo);
        return CommonResult.success(dto.getId());
    }

    /**
     * 非标工单存量校验接口
     */
    @RequestMapping(value = "/capacityCheck", method = RequestMethod.POST)
    public CommonResult<Set<String>> capacityCheck(@RequestBody NonStanderWorkOrderCreateReq req) {

        //转换成校验用的Opm
        ProductGeneralCheckOpm opm = convert.convertCheckOpm(req);
        opm.setCheckOpenNum(Boolean.FALSE);
        //存量校验
        productGeneralCheckService.checkProductInResourcePool(opm);
        productGeneralCheckService.checkProductInResourcePoolCapacity(opm);
        return CommonResult.success(StreamUtils.toSet(opm.getErrorMessages()));
    }

    /**
     * 非标工单,创建时，根据process key获取tree节点
     */
    @RequestMapping(value = "/treeNode", method = RequestMethod.POST)
    public CommonResult<TaskTreeNodeDTO> selectTreeNodeByKey() {
        return CommonResult.success(nonStanderWorkOrderService.selectTreeNodeByKey());
    }
    /**
     * 非标工单详情
     *
     * @param req
     * @return
     */
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public CommonResult<NonStanderWorkOrderVO> orderDetail(@RequestBody NonStanderWorkOrderDetailReq req) {
        Precondition.checkArgument(req.getWorkOrderId(), "工单ID不能为空");
        //查询用户是否存在草稿缓存
        UserCenterUserDTO currentUser = userHelper.getCurrentUser();
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        NonStanderWorkOrderDTO dto = nonStanderWorkOrderManager.getById(req.getWorkOrderId());
        Precondition.checkArgument(dto, "工单异常不存在");
        Precondition.checkArgument(dto.getActivitiId(), "工单异常不存在流程id");
        NonStanderWorkOrderVO vo = convert.convertDetail(dto);
        //获取资源的产品详情
        List<NonStanderWorkOrderProductDTO> productDTOS = nonStandardWorkOrderProductManager
                .list(new NonStanderWorkOrderProductQuery().setOrderId(req.getWorkOrderId()));
        convert.fillDetail(vo, productDTOS,req.getAggregation());
        //获取审批记录
        List<WorkOrderAuthLogDTO> authLogDTOS = workOrderAuthLogManager.list(new WorkOrderAuthLogQuery().setWorkOrderId(req.getWorkOrderId()));
        ActivityTaskTreeVo activityTaskVo = nonStanderWorkOrderService.getTaskNodesTree(dto.getId());
        nonStanderWorkOrderService.getNextTaskNodes(dto.getId(), authLogDTOS, activityTaskVo.getCurrentTaskDisplayName());
        nonStanderWorkOrderService.processLogRoleNames(authLogDTOS);
        vo.setActivityTask(activityTaskVo);
        vo.setApproveRecordList(authLogDTOS);
        // 获取资源概览
        ResourceShowInfoDTO showInfoDTO = nonStandardWorkOrderProductManager.selectResourceOverview(new NonStanderWorkOrderProductQuery().setOrderId(req.getWorkOrderId()));
        vo.setShowInfo(showInfoDTO);

        return CommonResult.success(vo);
    }


    private void processOrderProduct(NonStanderWorkOrderDTO dto) {
        //创建前把所有product先删除
        String standerWorkOrderId = dto.getId();
        // todo 清理资源
        nonStandardWorkOrderProductManager.deleteByWorkOrderId(standerWorkOrderId);
        //校验填充资源
        if (dto.getOfflineOpen()){
            StreamUtils.mapArray(dto.getGcsModelList(), Function.identity()).forEach(item ->
                    nonStanderWorkOrderService.checkFillEscResourceOffline(item, standerWorkOrderId));
            StreamUtils.mapArray(dto.getEcsModelList(), Function.identity()).forEach(item ->
                    nonStanderWorkOrderService.checkFillEscResourceOffline(item, standerWorkOrderId));
            StreamUtils.mapArray(dto.getMysqlModelList(), Function.identity()).forEach(item ->
                    nonStanderWorkOrderService.checkFillMysqlResource(item, standerWorkOrderId));
            StreamUtils.mapArray(dto.getRedisModelList(), Function.identity()).forEach(item ->
                    nonStanderWorkOrderService.checkFillRedisResource(item, standerWorkOrderId));
            StreamUtils.mapArray(dto.getBmsModelList(), Function.identity()).forEach(item ->
                    nonStanderWorkOrderService.checkFillBmsResource(item, standerWorkOrderId));
            StreamUtils.mapArray(dto.getGmsModelList(), Function.identity()).forEach(item ->
                    nonStanderWorkOrderService.checkFillGmsResource(item, standerWorkOrderId));
            StreamUtils.mapArray(dto.getPostgreSqlModelList(), Function.identity()).forEach(item ->
                    nonStanderWorkOrderService.checkFillPostgreSqlResource(item, standerWorkOrderId));
        }else {
            StreamUtils.mapArray(dto.getGcsModelList(), Function.identity()).forEach(item ->
                    nonStanderWorkOrderService.checkFillEscResource(item, standerWorkOrderId));
            StreamUtils.mapArray(dto.getEcsModelList(), Function.identity()).forEach(item ->
                    nonStanderWorkOrderService.checkFillEscResource(item, standerWorkOrderId));
        }

    }


    /**
     * 非标工单流程审批
     */
    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    public CommonResult<String> audit(@RequestBody NonStanderWorkOrderAuditReq req) {
        Precondition.checkArgument(req.getOrderId(), "工单ID不能为空");
        Precondition.checkArgument(req.getActiviteStatus(), "审核操作状态不能为空");
        if (req.getActiviteStatus().equals(ActivityEnum.ActivityStatusEnum.REJECT.getCode())) {
            Precondition.checkArgument(req.getNodeCode(), "驳回时请选择驳回指定节点标识");
        }
        // 判断当前节点是哪个如果是第一个节点需要额外
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        NonStanderAuditWorkOrderDTO auditDTO = convert.convert(req);
        nonStanderWorkOrderService.audit(auditDTO, currentUser.getId());
        return CommonResult.success("操作成功");
    }

    /**
     * 撤回
     */
    @RequestMapping("/cancel")
    public CommonResult<String> cancel(@RequestBody NonStanderWorkOrderCancelReq req) {
        Precondition.checkArgument(req.getWorkOrderId(), "工单ID不能为空");
        nonStanderWorkOrderService.cancel(req.getWorkOrderId());
        return CommonResult.success(req.getWorkOrderId());
    }

    /**
     * 分页查询
     * 需要根据条件判断当前查询的是待审批 还是已审批 还是驳回的工单
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public CommonResult<PageResult<NonStanderWorkOrderVO>> page(@RequestBody NonStanderWorkOrderPageReq req) {
        NonStanderWorkOrderQuery query = convert.convert(req);
        //判断查询的是待审批 还是已审批 还是驳回的工单
        Precondition.checkArgument(req.getApprovalCode());
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(currentUserId, "当前用户未登录");

        PageResult<NonStanderWorkOrderDTO> page = nonStanderWorkOrderService.page(query,currentUserId);
        PageResult<NonStanderWorkOrderVO> box = PageWarppers.box(page, convert::convert);
        List<NonStanderWorkOrderVO> records = box.getRecords();
        fillOrderRevoke(records, currentUserId);
        return CommonResult.success(box);
    }


    /**
     * 列表数量统计
     */
    @RequestMapping(value = "/auditCount", method = RequestMethod.POST)
    public CommonResult<AuditCountVo> auditCount(@RequestBody NonStanderWorkOrderQuery query) {
        Long userId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(userId, "获取当前用户信息失败");
        query.setUserId(String.valueOf(userId));
        AuditCountVo countVo = nonStanderWorkOrderService.orderCount(query);
        return CommonResult.success(countVo);
    }

    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public void export(@RequestBody NonStanderWorkOrderPageReq req, HttpServletResponse response) {
        Precondition.checkArgument(req.getApprovalCode(), "查询审批节点的类型不能为空");
        NonStanderWorkOrderQuery orderQuery = convert.convert(req);
        orderQuery.setPageSize(10000);
        Long userId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(userId, "当前用户未登录");
        PageResult<NonStanderWorkOrderDTO> page = nonStanderWorkOrderService.page(orderQuery,userId);
        PageResult<NonStanderWorkOrderVO> box = PageWarppers.box(page, convert::convert);
        List<NonStanderWorkOrderVO> records = box.getRecords();
        String filePath = System.getProperty("user.dir") + "/export/" + UUID.randomUUID().toString().replaceAll("-", "") + ".xlsx";
        FileUtils.doExport(records, ChangeWorkOrderVO.class, filePath);
        downloadFile(response, filePath, "非标工单列表.xlsx");
    }

    /**
     * 非标开通自定义规格
     * @param opm opm
     * @return string
     */
    @RequestMapping(value = "/createFlavor", method = RequestMethod.POST)
    public CommonResult<String> createFlavor(@RequestBody FlavorCreateOpm opm) {
        Long userId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(userId, "获取当前用户信息失败");
        Precondition.checkArgument(opm.getRam(), "内存大小不能为空");
        Precondition.checkArgument(opm.getVcpus(), "cpu个数不能为空");
        return nonStanderWorkOrderService.createFlavor(opm);
    }

    private void downloadFile(HttpServletResponse response, String exportPath, String fileName) {
        try {
            // 以流的形式下载文件。
            InputStream fis = new BufferedInputStream(Files.newInputStream(Paths.get(exportPath)));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            //清空response
            response.reset();
            //设置response响应头
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            // 添加Content-Length
            File file = new File(exportPath);
            response.setContentLength((int) file.length());
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(buffer);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("文件下载失败：" + e);
        }
    }

    private void fillOrderRevoke(List<NonStanderWorkOrderVO> records, Long userId) {
        records.forEach(item -> {
            //判断当前用户是否是工单的创建者 并且工单到达 资源回收环节
            if (!item.getCreatedBy().equals(userId)) {
                item.setCanRevoke(false);
                return;
            }
            //获取当前节点
            ActivityTaskTreeVo taskNodes = baseActivity.taskNodesTree(item.getActivitiId(), ActivityEnum.ActivityProcessEnum.NON_STANDARD_PROCESS);
            if (null == taskNodes.getCurrentTaskBpmnName()) {
                item.setCanRevoke(false);
            }
            if ("autodit end".equals(taskNodes.getCurrentTaskBpmnName())) {
                item.setCanRevoke(false);
            }
            //如果当前节点是网络创建或者资源开通就不能撤了
            List<String> codeList = Arrays.asList(
                    ActivitiStatusEnum.OFFLINE_OPEN_H.getNode(),
                    ActivitiStatusEnum.INFORMATION_ARCHIVE_H.getNode(),
                    ActivitiStatusEnum.NETWORK_PROVISIONING_H.getNode(),
                    ActivitiStatusEnum.RESOURCE_CREATION_H.getNode(),
                    ActivitiStatusEnum.OFFLINE_OPEN_L.getNode(),
                    ActivitiStatusEnum.INFORMATION_ARCHIVE_L.getNode(),
                    ActivitiStatusEnum.NETWORK_PROVISIONING_L.getNode(),
                    ActivitiStatusEnum.RESOURCE_CREATION_L.getNode());
            if (codeList.contains(taskNodes.getCurrentTaskBpmnName())) {
                item.setCanRevoke(false);
            }
        });
    }


    private void preCheckWorkOrderReq(NonStanderWorkOrderCreateReq req) {
        Precondition.checkArgument(req.getBillId(), "e55计费号不能为空");
        Precondition.checkArgument(req.getOrderTitle(), "title不能为空");
        // Precondition.checkArgument(req.getOrderDesc(), "orderDesc不能为空");
        //Precondition.checkArgument(req.getResourceApplyFiles(), "resourceApplyFile不能为空");
    }


}
