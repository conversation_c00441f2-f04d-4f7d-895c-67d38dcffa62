package com.datatech.slgzt.controller.recovery;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.annotation.OperationLog;
import com.datatech.slgzt.convert.RecoveryWorkOrderAuditConvert;
import com.datatech.slgzt.convert.RecoveryWorkOrderWebConvert;
import com.datatech.slgzt.enums.AuthorityCodeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.enums.bpmn.ActivityEnum;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.bpmn.ActivityTaskVo;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.dto.business.BusinessService;
import com.datatech.slgzt.model.dto.order.RecoveryAuditWorkOrderDTO;
import com.datatech.slgzt.model.dto.recovery.RecoveryWorkOrderDetailDTO;
import com.datatech.slgzt.model.home.AuditCountVo;
import com.datatech.slgzt.model.opm.RecoveryWorkOrderCreateOpm;
import com.datatech.slgzt.model.query.ContainerQuotaQuery;
import com.datatech.slgzt.model.query.RecoveryWorkOrderQuery;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.query.WorkOrderAuthLogQuery;
import com.datatech.slgzt.model.recovery.*;
import com.datatech.slgzt.model.req.recovery.*;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.network.NetworkOrderResult;
import com.datatech.slgzt.model.vo.network.NetworkTableVo;
import com.datatech.slgzt.model.vo.recovery.RecoveryWorkOrderDetailVO;
import com.datatech.slgzt.model.vo.recovery.export.*;
import com.datatech.slgzt.model.vo.standard.RecoveryWorkOrderVO;
import com.datatech.slgzt.model.vo.vpc.VpcOrderResult;
import com.datatech.slgzt.model.vo.vpc.VpcTableVo;
import com.datatech.slgzt.service.ResourceDetailService;
import com.datatech.slgzt.service.bpmn.BaseActivity;
import com.datatech.slgzt.service.recovery.RecoveryWorkOrderService;
import com.datatech.slgzt.service.usercenter.IUserSelectStrategy;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.*;
import com.datatech.slgzt.warpper.PageWarppers;
import com.google.common.collect.ArrayListMultimap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 回收工单控制器
 *
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月24日 13:39:28
 */
@RestController
@RequestMapping("/recovery/workOrder")
@Slf4j
public class RecoveryWorkOrderController {


    @Resource
    private BusinessService businessService;

    @Resource
    private TenantManager tenantManager;

    @Resource
    private RecoveryWorkOrderService recoveryWorkOrderService;

    @Resource
    private RecoveryWorkOrderWebConvert convert;

    @Resource
    private RecoveryWorkOrderAuditConvert auditConvert;

    @Resource
    private BaseActivity baseActivity;

    @Resource
    private RecoveryWorkOrderManager recoveryWorkOrderManager;


    @Resource
    private WorkOrderAuthLogManager workOrderAuthLogManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private VpcOrderManager vpcOrderManager;

    @Resource
    private NetworkOrderManager networkOrderManager;

    @Resource
    private RecoveryWorkOrderProductManager recoveryWorkOrderProductManager;

     @Resource
     private ContainerQuotaManager containerQuotaManager;

    @Resource
    private IUserSelectStrategy iUserSelectStrategy;

    /**
     * 分页查询
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public CommonResult<PageResult<RecoveryWorkOrderVO>> page(@RequestBody RecoveryWorkOrderPageReq req) {
        Precondition.checkArgument(req.getApprovalCode(), "查询审批节点的类型不能为空");
        RecoveryWorkOrderQuery query = convert.pageReq2Query(req);
        Long userId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(userId, "当前用户未登录");
        PageResult<RecoveryWorkOrderDTO> page = recoveryWorkOrderService.page(query, userId);
        PageResult<RecoveryWorkOrderVO> box = PageWarppers.box(page, convert::dto2vo);
        List<RecoveryWorkOrderVO> records = box.getRecords();
        fillOrderRevoke(records, userId);
        return CommonResult.success(box);
    }

    private void fillOrderRevoke(List<RecoveryWorkOrderVO> records, Long userId) {
        records.forEach(item -> {
            //判断当前用户是否是工单的创建者 并且工单到达 资源回收环节
            if (!item.getCreatedBy().equals(userId)) {
                item.setCanRevoke(false);
                return;
            }
            //获取当前节点
            ActivityTaskVo taskNodes = baseActivity.taskNodes(item.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_RECOVERY_PROCESS);
            //如果当前节点是网络创建或者资源开通就不能撤了
            List<String> codeList = Arrays.asList(AuthorityCodeEnum.USER_TASK.code(), AuthorityCodeEnum.BUSINESS_DEPART_LEADER2.code());
            if (!codeList.contains(taskNodes.getCurrentTask())) {
                item.setCanRevoke(false);
            }
        });
    }

    /**
     * 创建回收工单
     */
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @OperationLog(description = "创建回收工单", operationType = "CREATE")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> create(@RequestBody RecoveryWorkOrderCreateReq req) {
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        //先判断该用户与该业务系统关联的租户是否存在绑定关系
        Boolean isBound = businessService.checkUserAndBusinessSystemIsBound(req.getBusinessSystemId(), currentUserId);
        Precondition.checkArgument(isBound, "当前用户与该业务系统所关联租户不存在绑定关系！");
        //存在绑定关系，再判断该用户是否是租户管理员
        Boolean b = iUserSelectStrategy.checkOperateRole(currentUserId);
        Precondition.checkArgument(!b, "该用户为维护组角色，无法发起资源回收！");
        //基础校验
        Precondition.checkArgument(req.getOrderTitle(), "工单标题不能为空");
        Precondition.checkArgument(req.getDepartmentName(), "所属部门名称不能为空");
        Precondition.checkArgument(req.getManufacturer(), "厂家名称不能为空");
        Precondition.checkArgument(req.getManufacturerContacts(), "厂家联系人不能为空");
        Precondition.checkArgument(req.getManufacturerMobile(), "厂家联系电话不能为空");
        Precondition.checkArgument(req.getLevelThreeLeaderName(), "三级业务部门领导名称不能为空");
        Precondition.checkArgument(req.getLevelThreeLeaderId(), "三级业务部门领导Id不能为空");
        Precondition.checkArgument(req.getOrderDesc(), "订单描述不能为空");

        //如果怕并发可以用租户维度去加锁，上面还缺少不是自己的设备 不能回收的校验 现在前端控制先
        //校验完毕可以传入对应的id去创建了
        RecoveryWorkOrderCreateOpm opm = convert.convert(req);
        //查询 计费号和客户号
        CmpAppDTO cmpAppDTO = businessService.getById(req.getBusinessSystemId());
        Precondition.checkArgument(cmpAppDTO, "找不到对应的业务系统");
        Precondition.checkArgument(cmpAppDTO.getTenantId(), "找不到对应的业务系统");
        TenantDTO tenantDTO = tenantManager.getById(cmpAppDTO.getTenantId());
        Precondition.checkArgument(tenantDTO, "找不到对应的租户");
        Precondition.checkArgument(tenantDTO.getCustomNo(), "找不到对应的客户号");
        Precondition.checkArgument(tenantDTO.getBillId(), "找不到对应的计费号");
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        opm.setCreatedBy(currentUser.getId());
        opm.setCreatedUserName(currentUser.getUserName());
        opm.setCreateUserId(currentUser.getId());
        opm.setTenantId(cmpAppDTO.getTenantId());
        opm.setTenantName(tenantDTO.getName());
        opm.setBusinessSystemName(cmpAppDTO.getSystemName());
        opm.setBusinessSystemCode(cmpAppDTO.getSystemCode());
        opm.setBillId(tenantDTO.getBillId());
        opm.setCustomNo(tenantDTO.getCustomNo());
        if(ObjNullUtils.isNull(opm.getId())){
            recoveryWorkOrderService.fillCheckCreate(opm);
        }
        String orderId = recoveryWorkOrderService.createRecoveryWorkOrder(opm);
        return CommonResult.success(orderId);

    }
    /**
     * 草稿
     */
    @RequestMapping(value = "/draft", method = RequestMethod.POST)
    @OperationLog(description = "创建回收工单草稿", operationType = "CREATE")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> draft(@RequestBody RecoveryWorkOrderCreateReq req) {
        //基础校验
        Precondition.checkArgument(req.getOrderTitle(), "工单标题不能为空");
        RecoveryWorkOrderCreateOpm opm = convert.convert(req);
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        opm.setCreatedBy(currentUser.getId());
        opm.setCreatedUserName(currentUser.getUserName());
        opm.setCreateUserId(currentUser.getId());
        recoveryWorkOrderService.fillCheckCreate(opm);
        opm.setCanDraft(true);
        String orderId = recoveryWorkOrderService.createRecoveryWorkOrder(opm);
        return CommonResult.success(orderId);
    }


    /**
     * 撤回工单
     */
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public CommonResult<String> revoke(@RequestBody RecoveryWorkOrderCancelReq req) {
        Precondition.checkArgument(req.getWorkOrderId(), "工单ID不能为空");
        recoveryWorkOrderService.cancel(req.getWorkOrderId());
        return CommonResult.success(req.getWorkOrderId());
    }

    /**
     * 审批
     */
    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    public CommonResult<String> approve(@RequestBody RecoveryWorkOrderAuditReq req) {
        Precondition.checkArgument(req.getOrderId(), "工单ID不能为空");
        Precondition.checkArgument(req.getActiviteStatus(), "审核操作状态不能为空");
        if (req.getActiviteStatus().equals(ActivityEnum.ActivityStatusEnum.REJECT.getCode())) {
            Precondition.checkArgument(req.getNodeCode(), "驳回时请选择驳回指定节点标识");
        }

        RecoveryAuditWorkOrderDTO auditDTO = auditConvert.req2DTO(req);
        recoveryWorkOrderService.audit(auditDTO, UserHelper.INSTANCE.getCurrentUserId());
        return CommonResult.success("操作成功");
    }

    /**
     * 详情
     */
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public CommonResult<RecoveryWorkOrderDetailVO> detail(@RequestBody RecoveryWorkOrderDetailReq req) {
        Precondition.checkArgument(req.getWorkOrderId(), "工单ID不能为空");
        //查询用户是否存在草稿缓存
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        RecoveryWorkOrderDTO dto = recoveryWorkOrderManager.getById(req.getWorkOrderId());
        Precondition.checkArgument(dto, "工单异常不存在");
        RecoveryWorkOrderDetailVO vo = convert.convertDetail(dto);
        List<RecoveryWorkOrderProductDTO> productDTOS = recoveryWorkOrderProductManager.listByWorkOrderId(req.getWorkOrderId());
        //productDTOS 直接过滤掉子产品
        productDTOS = productDTOS.stream().filter(productDTO -> productDTO.getParentProductId() == 0).collect(Collectors.toList());
        ArrayListMultimap<String, RecoveryWorkOrderProductDTO> type2product =
                StreamUtils.toArrayListMultimap(productDTOS, RecoveryWorkOrderProductDTO::getProductType);
        //获取网络资源和vpc
        //获取网络id列表
        List<String> vpcIdList = StreamUtils.mapArrayFilterNull(type2product.get(ProductTypeEnum.VPC.getCode()), RecoveryWorkOrderProductDTO::getResourceDetailId);
        List<String> networkIdList = StreamUtils.mapArrayFilterNull(type2product.get(ProductTypeEnum.NETWORK.getCode()), RecoveryWorkOrderProductDTO::getResourceDetailId);
        List<String> cqIdList = StreamUtils.mapArrayFilterNull(type2product.get(ProductTypeEnum.CQ.getCode()), RecoveryWorkOrderProductDTO::getResourceDetailId);
        //获取资源表里的要调用的就是排除掉网络和vpc的
        List<Long> resDatilsIds = productDTOS.stream()
                .filter(productDTO->!productDTO.getProductType().equals(ProductTypeEnum.VPC.getCode()))
                .filter(productDTO->!productDTO.getProductType().equals(ProductTypeEnum.NETWORK.getCode()))
                .filter(productDTO->productDTO.getParentProductId()==0)
                .filter(productDTO -> ObjNullUtils.isNotNull(productDTO.getResourceDetailId()))
                .map(RecoveryWorkOrderProductDTO::getResourceDetailId)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        //获取资源
        List<ResourceDetailDTO> resourceDetailDTOS=new ArrayList<>();
        if (CollectionUtil.isNotEmpty(resDatilsIds)) {
            resourceDetailDTOS = resourceDetailManager.list(new ResourceDetailQuery().setIds(resDatilsIds));
        }
        //获取vpc
        List<VpcOrderResult> vpcOrderResultList=new ArrayList<>();
        if (ObjNullUtils.isNotNull(vpcIdList)) {
            vpcOrderResultList = vpcOrderManager.listByIdList(vpcIdList);
        }
        //获取网络
        List<NetworkOrderResult> networkOrderResultList=new ArrayList<>();
        if (ObjNullUtils.isNotNull(networkIdList)) {
            networkOrderResultList = networkOrderManager.selectNetworkRecoveryList(networkIdList);
        }
        //获取容器配额
        List<ContainerQuotaDTO> cqList=new ArrayList<>();
        if (ObjNullUtils.isNotNull(cqIdList)) {
            cqList = containerQuotaManager.queryList(new ContainerQuotaQuery().setContainerIds(StreamUtils.toLong(cqIdList)));
        }

        convert.fillProductDetail(vo,resourceDetailDTOS,vpcOrderResultList,networkOrderResultList,cqList,type2product);
        // 获取审批记录
        List<WorkOrderAuthLogDTO> authLogDTOS = workOrderAuthLogManager.list(new WorkOrderAuthLogQuery().setWorkOrderId(req.getWorkOrderId()));
        // 获取流程节点数据
        ActivityTaskVo activityTaskVo = recoveryWorkOrderService.getTaskNodes(dto.getId());
        recoveryWorkOrderService.getNextTaskNodes(dto.getId(), authLogDTOS, activityTaskVo.getCurrentTaskName());
        vo.setActivityTask(activityTaskVo);
        vo.setApproveRecordList(authLogDTOS);

        return CommonResult.success(vo);
    }

    /**
     * 回收列表数量统计
     */
    @RequestMapping(value = "/auditCount", method = RequestMethod.POST)
    public CommonResult<AuditCountVo> auditCount(@RequestBody RecoveryWorkOrderQuery orderQuery) {
        Long userId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(userId, "获取当前用户信息失败");
        orderQuery.setUserId(String.valueOf(userId));
        AuditCountVo countVo = recoveryWorkOrderService.orderCount(orderQuery);
        return CommonResult.success(countVo);
    }


    /**
     * 导出
     */
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public void export(@RequestBody RecoveryWorkOrderPageReq req, HttpServletResponse response) {
        Precondition.checkArgument(req.getApprovalCode(), "查询审批节点的类型不能为空");
        RecoveryWorkOrderQuery query = convert.pageReq2Query(req);
        query.setPageSize(10000);
        Long userId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(userId, "当前用户未登录");
        PageResult<RecoveryWorkOrderDTO> page = recoveryWorkOrderService.page(query, userId);
        PageResult<RecoveryWorkOrderVO> box = PageWarppers.box(page, convert::dto2vo);
        List<RecoveryWorkOrderVO> records = box.getRecords();
        //exportPath 变成项目路径
        // 获取项目根路径
        String filePath = System.getProperty("user.dir")+"/export/" + UUID.randomUUID().toString().replaceAll("-", "") + ".xlsx";
        FileUtils.doExport(records, RecoveryWorkOrderVO.class, filePath);
        downloadFile(response, filePath, "回收工单详情.xlsx");

    }

    @RequestMapping(value = "/resourceExport", method = RequestMethod.GET)
    public void resourceExport(@ModelAttribute RecoveryWorkOrderResourceReq req, HttpServletResponse response) {
        Precondition.checkArgument(req.getWorkOrderId(), "工单ID不能为空");
        Precondition.checkArgument(req.getProductType(), "资源产品类型不能为空");
        List<RecoveryWorkOrderProductDTO> productDTOS = recoveryWorkOrderProductManager.listByWorkOrderId(req.getWorkOrderId());
        productDTOS = productDTOS.stream()
                .filter(ObjNullUtils::isNotNull)
                .filter(productDTO -> productDTO.getParentProductId() == 0)
                .filter(productDTO -> req.getProductType().equalsIgnoreCase(productDTO.getProductType()))
                .collect(Collectors.toList());
        Precondition.checkArgument(productDTOS, String.format("工单中有关于产品：[%s]在产品表中找不到对应的记录,请确认", req.getProductType()));

        ResourceExport resourceExport = convert.fillResourceExportDetail(productDTOS, req.getProductType());
        productExport(resourceExport, req.getProductType(), response);
    }

    private void productExport(ResourceExport vo, String productType, HttpServletResponse response) {
        // 获取项目根路径
        String filePath = System.getProperty("user.dir")+"/export/" + UUID.randomUUID().toString().replaceAll("-", "") + ".xlsx";
        String fileName = "";
        switch (productType) {
            case "ecs":
                // 宽带、弹性公网ip、数据盘、需要进行转换
                FileUtils.doExport(vo.getEcsExportVOS(), EcsExportVO.class, filePath);
                fileName = "云主机回收产品列表.xlsx";
                break;
            case "gcs":
                FileUtils.doExport(vo.getGcsExportVOS(), EcsExportVO.class, filePath);
                fileName = "GPU云主机回收产品列表.xlsx";
                break;
            case "redis":
                FileUtils.doExport(vo.getRedisExportVOS(), EcsExportVO.class, filePath);
                fileName = "Redis中间件产品列表.xlsx";
                break;
            case "postgreSql":
                FileUtils.doExport(vo.getPostgreSqlExportVOS(), EcsExportVO.class, filePath);
                fileName = "PostgreSQL云数据库回收产品列表.xlsx";
                break;
            case "mysql":
                FileUtils.doExport(vo.getMysqlExportVOS(), EcsExportVO.class, filePath);
                fileName = "MySQL云数据库回收产品列表.xlsx";
                break;
            case "evs":
                FileUtils.doExport(vo.getEvsExportVOS(), EvsExportVO.class, filePath);
                fileName = "云硬盘回收产品列表.xlsx";
                break;
            case "shareEvs":
                FileUtils.doExport(vo.getShareEvsExportVOS(), EvsExportVO.class, filePath);
                fileName = "共享云硬盘回收产品列表.xlsx";
                break;
            case "eip":
                FileUtils.doExport(vo.getEipExportVOS(), EipExportVO.class, filePath);
                fileName = "弹性公网回收产品列表.xlsx";
                break;
            case "slb":
                FileUtils.doExport(vo.getSlbExportVOS(), SlbExportVO.class, filePath);
                fileName = "负载均衡回收产品列表.xlsx";
                break;
            case "obs":
                FileUtils.doExport(vo.getObsExportVOS(), ObsExportVO.class, filePath);
                fileName = "对象存储回收产品列表.xlsx";
                break;
            case "nat":
                // 子网需要从planeNetworkModel中subnets获取subnetName，多个时进行拼接
                FileUtils.doExport(vo.getNatExportVOS(), NatExportVO.class, filePath);
                fileName = "nat网关回收产品列表.xlsx";
                break;
            case "vpc":
                FileUtils.doExport(vo.getVpcExportVOS(), VpcExportVO.class, filePath);
                fileName = "vpc回收产品列表.xlsx";
                break;
            case "network":
                FileUtils.doExport(vo.getNetworkExportVOS(), NetworkExportVO.class, filePath);
                fileName = "network回收产品列表.xlsx";
                break;
            case "backup":
                FileUtils.doExport(vo.getBackupExportVOS(), BackupExportVO.class, filePath);
                fileName = "备份策略回收产品列表.xlsx";
                break;
            case "vpn":
                FileUtils.doExport(vo.getVpnExportVOS(), VpnExportVO.class, filePath);
                fileName = "vpn回收产品列表.xlsx";
                break;
            case "nas":
                FileUtils.doExport(vo.getNasExportVOS(), NasExportVO.class, filePath);
                fileName = "nas回收产品列表.xlsx";
                break;
            case "pm":
                FileUtils.doExport(vo.getPmExportVOS(), PmExportVO.class, filePath);
                fileName = "裸金属回收产品列表.xlsx";
                break;
            case "kafka":
                FileUtils.doExport(vo.getKafkaExportVOS(), KafkaExportVO.class, filePath);
                fileName = "kafka回收产品列表.xlsx";
                break;
            case "flink":
                FileUtils.doExport(vo.getFlinkExportVOS(), FlinkExportVO.class, filePath);
                fileName = "flink回收产品列表.xlsx";
                break;
            case "es":
                FileUtils.doExport(vo.getEsExportVOS(), EsExportVO.class, filePath);
                fileName = "es回收产品列表.xlsx";
                break;
            case "bldRedis":
                FileUtils.doExport(vo.getBldRedisExportVOS(), BldRedisExportVO.class, filePath);
                fileName = "宝兰德redis回收产品列表.xlsx";
                break;
            default:
        }

        downloadFile(response, filePath, fileName);
    }

    private void downloadFile(HttpServletResponse response, String exportPath, String fileName) {
        try {
            // 以流的形式下载文件。
            InputStream fis = new BufferedInputStream(Files.newInputStream(Paths.get(exportPath)));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            //清空response
            response.reset();
            //设置response响应头
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            // 添加Content-Length
            File file = new File(exportPath);
            response.setContentLength((int) file.length());
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(buffer);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            // log.error("文件下载失败：" + e);
        }
    }




}