package com.datatech.slgzt.controller.zs;

import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.convert.Zs2MapWebConvert;
import com.datatech.slgzt.manager.DeviceCardMetricsManager;
import com.datatech.slgzt.manager.DeviceGpuInfoManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.model.req.zs2map.BusinessComputeRankingReq;
import com.datatech.slgzt.model.req.zs2map.BusinessSystemRankingReq;
import com.datatech.slgzt.model.req.zs2map.CardUtilizationReq;
import com.datatech.slgzt.model.vo.zs2map.BusinessComputeRankingVO;
import com.datatech.slgzt.model.vo.zs2map.BusinessSystemRankingVO;
import com.datatech.slgzt.model.vo.zs2map.CardUtilizationVO;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import com.datatech.slgzt.enums.DeviceModelTFEnum;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Zs2Map二级页面控制器
 *
 * <AUTHOR>
 * @description 基于ZsMapController的二级页面相关接口，包括卡类型利用率查询等功能
 * @date 2025年 01月27日
 */
@Slf4j
@RestController
@RequestMapping("/zs2map")
public class Zs2MapController {

    @Resource
    private DeviceCardMetricsManager deviceCardMetricsManager;

    @Resource
    private DeviceGpuInfoManager deviceGpuInfoManager;

    @Resource
    private Zs2MapWebConvert zs2MapWebConvert;

    /**
     * 获取卡类型的显存利用率和算力利用率
     * 支持两种聚合类型：
     * - day：按天聚合，每小时查询一次数据
     * - month：按月聚合，每天查询一次数据
     *
     * @param req 请求参数，包含开始时间、结束时间、聚合类型和卡类型
     * @return 卡类型利用率数据列表
     */
    @PostMapping("/cardUtilization")
    public CommonResult<List<CardUtilizationVO>> getCardUtilization(@RequestBody CardUtilizationReq req) {
        log.info("查询卡类型利用率，开始时间：{}，结束时间：{}，聚合类型：{}，卡类型：{}",
                req.getStartTime(), req.getEndTime(), req.getAggregationType(), req.getModelName());
        log.info("聚合说明 - day:每小时查询, month:每天查询");

        // 参数验证
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getStartTime()), "开始时间不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getEndTime()), "结束时间不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getAggregationType()), "聚合类型不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getModelName()), "卡类型不能为空");

        // 验证聚合类型
        Precondition.checkArgument("day".equals(req.getAggregationType()) || "month".equals(req.getAggregationType()),
                "聚合类型只能是 day 或 month");
        // 验证时间范围
        Precondition.checkArgument(!req.getStartTime().isAfter(req.getEndTime()), "开始时间不能晚于结束时间");

//        if (req.getModelName().equals("910B2") || req.getModelName().equals("910B4")) {
//            List<DeviceCardMetricsDTO> metricsData = deviceCardMetricsManager.queryCardUtilizationMetrics2(
//                    req.getAreaCode(),
//                    req.getModelName(),
//                    req.getStartTime(),
//                    req.getEndTime(),
//                    req.getAggregationType()
//            );
//            DeviceInfoQuery query = new DeviceInfoQuery();
//            query.setSubModelName(req.getModelName());
//            List<DeviceGpuInfoDTO> deviceGpuInfoDTOS = deviceGpuInfoManager.selectDeviceGpuInfoList(query);
//            Map<String, List<CardUtilizationVO>> resultMap = new HashMap<>();
//            for (DeviceCardMetricsDTO dto : metricsData) {
//                if (deviceGpuInfoDTOS.stream().anyMatch(i -> dto.getDeviceId().equals(i.getDeviceId()))) {
//                    String aggregationType = req.getAggregationType();
//                    CardUtilizationVO vo = new CardUtilizationVO();
//                    // 解析时间点
//                    try {
//                        if (ObjNullUtils.isNotNull(dto.getGpuTime())) {
//                            if ("day".equals(aggregationType)) {
//                                // day聚合：精确到小时 YYYYMMDDHH，补00分钟
//                                vo.setTimePoint(LocalDateTime.parse(dto.getGpuTime() + "00", DateTimeFormatter.ofPattern("yyyyMMddHHmm")));
//                            } else {
//                                // month聚合：精确到天 YYYYMMDD，补0000时分
//                                vo.setTimePoint(LocalDateTime.parse(dto.getGpuTime() + "0000", DateTimeFormatter.ofPattern("yyyyMMddHHmm")));
//                            }
//                        }
//                    } catch (Exception e) {
//                        log.warn("解析时间失败：{}", dto.getGpuTime());
//                        continue;
//                    }
//
//                    vo.setCardType(req.getModelName());
//                    vo.setAggregationType(aggregationType);
//
//                    // 转换显存利用率
//                    if (ObjNullUtils.isNotNull(dto.getMemUtilpercent())) {
//                        vo.setMemoryUtilization(BigDecimal.valueOf(dto.getMemUtilpercent())
//                                .setScale(2, RoundingMode.HALF_UP));
//                    }
//
//                    // 转换算力利用率
//                    if (ObjNullUtils.isNotNull(dto.getGpuUtilPercent())) {
//                        vo.setComputeUtilization(BigDecimal.valueOf(dto.getGpuUtilPercent())
//                                .setScale(2, RoundingMode.HALF_UP));
//                    }
//
//                    List<CardUtilizationVO> cardUtilizationVOS = resultMap.get(dto.getGpuTime());
//                    if (cardUtilizationVOS == null) {
//                        cardUtilizationVOS = new ArrayList<>();
//                        resultMap.put(dto.getGpuTime(), cardUtilizationVOS);
//                    }
//                    cardUtilizationVOS.add(vo);
//                }
//            }
//
//            List<CardUtilizationVO> result = new ArrayList<>();
//            for (Map.Entry<String, List<CardUtilizationVO>> entry : resultMap.entrySet()) {
//                // memoryUtilization和computeUtilization 取集合所有的平均值，只保留一个
//                List<CardUtilizationVO> cardUtilizationVOS = entry.getValue();
//                if (cardUtilizationVOS.size() > 1) {
//                    BigDecimal memoryUtilization = cardUtilizationVOS.stream()
//                            .map(CardUtilizationVO::getMemoryUtilization)
//                            .filter(Objects::nonNull)
//                            .reduce(BigDecimal.ZERO, BigDecimal::add)
//                            .divide(new BigDecimal(cardUtilizationVOS.size()), 2, RoundingMode.HALF_UP);
//                    BigDecimal computeUtilization = cardUtilizationVOS.stream()
//                            .map(CardUtilizationVO::getComputeUtilization)
//                            .filter(Objects::nonNull)
//                            .reduce(BigDecimal.ZERO, BigDecimal::add)
//                            .divide(new BigDecimal(cardUtilizationVOS.size()), 2, RoundingMode.HALF_UP);
//                    cardUtilizationVOS.get(0).setMemoryUtilization(memoryUtilization);
//                    cardUtilizationVOS.get(0).setComputeUtilization(computeUtilization);
//                    result.add(cardUtilizationVOS.get(0));
//                }else{
//                    result.add(cardUtilizationVOS.get(0));
//                }
//            }
//            // 补全缺失的时间数据
//            result = fillMissingTimeData(result, req.getStartTime(), req.getEndTime(), req.getAggregationType(), req.getModelName());
//            return CommonResult.success(result);
//        } else {
//            // 直接调用新的聚合查询方法，在数据库层面完成聚合
//            List<DeviceCardMetricsDTO> metricsData = deviceCardMetricsManager.queryCardUtilizationMetrics(
//                    req.getAreaCode(),
//                    req.getModelName(),
//                    req.getStartTime(),
//                    req.getEndTime(),
//                    req.getAggregationType()
//            );
//
//            if (ObjNullUtils.isNull(metricsData) || metricsData.isEmpty()) {
//                log.info("未查询到卡类型利用率数据");
//                return CommonResult.success(Collections.emptyList());
//            }
//
//            // 转换为VO对象
//            List<CardUtilizationVO> result = convertToCardUtilizationVOs(metricsData, req.getAggregationType());
//
//            // 补全缺失的时间数据
//            result = fillMissingTimeData(result, req.getStartTime(), req.getEndTime(), req.getAggregationType(), req.getModelName());
//
//            log.info("查询卡类型利用率成功，补全后返回数据条数：{}", result.size());
//            return CommonResult.success(result);
//        }

        //如果部门不为空就查询对应的deviceId
        List<String> deviceIds = new ArrayList<>();
        if (ObjNullUtils.isNotNull(req.getDeptNameList())) {
            List<DeviceGpuInfoDTO> deviceGpuInfoDTOS = deviceGpuInfoManager.selectDeviceGpuInfoList(new DeviceInfoQuery()
                    .setDeptNameList(req.getDeptNameList())
            );
            //过滤掉deviceId为空的数据
            deviceIds = deviceGpuInfoDTOS.stream()
                    .filter(ObjNullUtils::isNotNull)
                    .map(DeviceGpuInfoDTO::getDeviceId)
                    .collect(Collectors.toList());

        }
        // 直接调用新的聚合查询方法，在数据库层面完成聚合
        List<DeviceCardMetricsDTO> metricsData = deviceCardMetricsManager.queryCardUtilizationMetrics(
                req.getAreaCode(),
                req.getModelName(),
                req.getStartTime(),
                req.getEndTime(),
                req.getAggregationType(),
                deviceIds
        );

        if (ObjNullUtils.isNull(metricsData) || metricsData.isEmpty()) {
            log.info("未查询到卡类型利用率数据");
            return CommonResult.success(Collections.emptyList());
        }

        // 转换为VO对象
        List<CardUtilizationVO> result = convertToCardUtilizationVOs(metricsData, req.getAggregationType());

        // 补全缺失的时间数据
        result = fillMissingTimeData(result, req.getStartTime(), req.getEndTime(), req.getAggregationType(), req.getModelName());

        log.info("查询卡类型利用率成功，补全后返回数据条数：{}", result.size());
        return CommonResult.success(result);
    }


    /**
     * 获取业务系统利用率排行
     * 返回显存利用率和算力利用率排行前5的业务系统
     *
     * @param req 请求参数，包含区域编码、卡类型、时间范围等过滤条件
     * @return 业务系统利用率排行数据
     */
    @PostMapping("/businessSystemRanking")
    public CommonResult<BusinessSystemRankingVO> getBusinessSystemRanking(@RequestBody BusinessSystemRankingReq req) {
        log.info("查询业务系统利用率排行，区域编码：{}，卡类型：{}，开始时间：{}，结束时间：{}",
                req.getAreaCode(), req.getModelName(), req.getStartTime(), req.getEndTime());
        String modelName=req.getModelName();
//        if ("910B2".equals(req.getModelName())||"910B4".equals(req.getModelName())){
//            modelName="910B";
//        }

        // 参数校验
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getStartTime()), "开始时间不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getEndTime()), "结束时间不能为空");

        // 第一步：查询历史指标数据（按设备ID分组聚合）
        List<DeviceCardMetricsDTO> historyMetrics = deviceCardMetricsManager.queryBusinessSystemHistoryMetrics(
                req.getAreaCode(), modelName, req.getStartTime(), req.getEndTime());


        if (ObjNullUtils.isNull(historyMetrics) || historyMetrics.isEmpty()) {
            log.info("未查询到历史指标数据");
            return CommonResult.success(new BusinessSystemRankingVO());
        }

        // 第二步：提取设备ID列表，查询设备信息获取业务系统信息
        List<String> deviceIds = StreamUtils.mapArray(historyMetrics, DeviceCardMetricsDTO::getDeviceId);
        DeviceInfoQuery deviceQuery = new DeviceInfoQuery();
        deviceQuery.setDeviceIds(deviceIds);
        deviceQuery.setDeptNameList(req.getDeptNameList());
        List<DeviceGpuInfoDTO> deviceInfoList = deviceGpuInfoManager.selectDeviceGpuInfoList(deviceQuery);
        
        if (ObjNullUtils.isNull(deviceInfoList) || deviceInfoList.isEmpty()) {
            log.info("未查询到设备信息数据");
            return CommonResult.success(new BusinessSystemRankingVO());
        }

        // 第三步：在内存中关联数据并聚合计算业务系统利用率
        BusinessSystemRankingVO result = calculateBusinessSystemRankingWithDeviceInfo(historyMetrics, deviceInfoList,req.getModelName());

        log.info("查询业务系统利用率排行成功，显存利用率排行数量：{}，算力利用率排行数量：{}", 
                result.getMemoryUtilizationRanking().size(), result.getComputeUtilizationRanking().size());
        return CommonResult.success(result);
    }

    /**
     * 获取业务系统算力排名
     * 计算每种卡类型的TFLOPS，按业务系统进行分组和排序，返回前10名
     *
     * @param req 请求参数，包含区域编码、卡类型等过滤条件
     * @return 业务系统算力排名数据（前10名）
     */
    @PostMapping("/businessComputeRanking")
    public CommonResult<BusinessComputeRankingVO> getBusinessComputeRanking(@RequestBody BusinessComputeRankingReq req) {
        log.info("查询业务系统算力排名，区域编码：{}，卡类型：{}", 
                req.getAreaCode(), req.getModelName());

        // 转换查询条件
        DeviceInfoQuery query = zs2MapWebConvert.convert(req);
        
        // 查询所有设备信息
        List<DeviceGpuInfoDTO> deviceInfoList = deviceGpuInfoManager.selectDeviceGpuInfoList(query);
        
        if (ObjNullUtils.isNull(deviceInfoList) || deviceInfoList.isEmpty()) {
            log.info("未查询到设备信息数据");
            return CommonResult.success(new BusinessComputeRankingVO());
        }

        // 计算业务系统算力排名
        BusinessComputeRankingVO result = calculateBusinessComputeRanking(deviceInfoList);

        log.info("查询业务系统算力排名成功，返回排名数量：{}", 
                result.getComputeRanking().size());
        return CommonResult.success(result);
    }

    /**
     * 将DeviceCardMetricsDTO列表转换为CardUtilizationVO列表
     */
    private List<CardUtilizationVO> convertToCardUtilizationVOs(List<DeviceCardMetricsDTO> metricsData, String aggregationType) {
        List<CardUtilizationVO> result = new ArrayList<>();
        
        if (ObjNullUtils.isNull(metricsData)) {
            return result;
        }
        
        for (DeviceCardMetricsDTO dto : metricsData) {
            CardUtilizationVO vo = new CardUtilizationVO();
            
            // 解析时间点
            try {
                if (ObjNullUtils.isNotNull(dto.getGpuTime())) {
                    if ("day".equals(aggregationType)) {
                        // day聚合：精确到小时 YYYYMMDDHH，补00分钟
                        vo.setTimePoint(LocalDateTime.parse(dto.getGpuTime() + "00", DateTimeFormatter.ofPattern("yyyyMMddHHmm")));
                    } else {
                        // month聚合：精确到天 YYYYMMDD，补0000时分
                        vo.setTimePoint(LocalDateTime.parse(dto.getGpuTime() + "0000", DateTimeFormatter.ofPattern("yyyyMMddHHmm")));
                    }
                }
            } catch (Exception e) {
                log.warn("解析时间失败：{}", dto.getGpuTime());
                continue;
            }
            
            vo.setCardType(dto.getModelName());
            vo.setAggregationType(aggregationType);

            // 转换显存利用率
            if (ObjNullUtils.isNotNull(dto.getMemUtilpercent())) {
                vo.setMemoryUtilization(BigDecimal.valueOf(dto.getMemUtilpercent())
                        .setScale(2, RoundingMode.HALF_UP));
            }

            // 转换算力利用率
            if (ObjNullUtils.isNotNull(dto.getGpuUtilPercent())) {
                vo.setComputeUtilization(BigDecimal.valueOf(dto.getGpuUtilPercent())
                        .setScale(2, RoundingMode.HALF_UP));
            }

            result.add(vo);
        }
        
        return result;
    }

    /**
     * 补全缺失的时间数据
     * 根据聚合类型生成完整的时间序列，对于没有数据的时间点补充为0
     */
    private List<CardUtilizationVO> fillMissingTimeData(List<CardUtilizationVO> existingData, 
                                                       LocalDateTime startTime, LocalDateTime endTime, 
                                                       String aggregationType, String cardType) {
        // 生成完整的时间序列
        List<LocalDateTime> fullTimeRange = generateFullTimeRange(startTime, endTime, aggregationType);
        
        // 将现有数据转换为Map，便于查找
        Map<LocalDateTime, CardUtilizationVO> existingDataMap = existingData.stream()
                .collect(Collectors.toMap(CardUtilizationVO::getTimePoint, vo -> vo));
        
        // 生成补全后的结果
        List<CardUtilizationVO> result = new ArrayList<>();
        
        for (LocalDateTime timePoint : fullTimeRange) {
            CardUtilizationVO vo = existingDataMap.get(timePoint);
            if (vo != null) {
                // 有数据，直接使用
                result.add(vo);
            } else {
                // 没有数据，补充为0
                CardUtilizationVO emptyVO = new CardUtilizationVO();
                emptyVO.setTimePoint(timePoint);
                emptyVO.setCardType(cardType);
                emptyVO.setAggregationType(aggregationType);
                emptyVO.setMemoryUtilization(null);
                emptyVO.setComputeUtilization(null);
                result.add(emptyVO);
            }
        }
        
        // 按时间排序
        result.sort(Comparator.comparing(CardUtilizationVO::getTimePoint));
        
        return result;
    }

    /**
     * 根据聚合类型生成完整的时间范围
     */
    private List<LocalDateTime> generateFullTimeRange(LocalDateTime startTime, LocalDateTime endTime, String aggregationType) {
        List<LocalDateTime> timeRange = new ArrayList<>();
        LocalDateTime current = startTime;
        
        if ("day".equals(aggregationType)) {
            // day聚合：按小时生成时间序列
            while (!current.isAfter(endTime)) {
                timeRange.add(current);
                current = current.plusHours(1);
            }
        } else if ("month".equals(aggregationType)) {
            // month聚合：按天生成时间序列
            // 调整到当天的00:00:00
            current = current.toLocalDate().atStartOfDay();
            LocalDateTime adjustedEndTime = endTime.toLocalDate().atStartOfDay();
            
            while (!current.isAfter(adjustedEndTime)) {
                timeRange.add(current);
                current = current.plusDays(1);
            }
        }
        
        return timeRange;
    }



    /**
     * 计算业务系统算力排名
     */
    private BusinessComputeRankingVO calculateBusinessComputeRanking(List<DeviceGpuInfoDTO> deviceInfoList) {
        BusinessComputeRankingVO result = new BusinessComputeRankingVO();
        
        // 过滤掉业务系统为空的数据，按业务系统分组
        Map<String, List<DeviceGpuInfoDTO>> businessSystemGroups = deviceInfoList.stream()
                .filter(device -> ObjNullUtils.isNotNull(device.getBusinessSystemName()) && 
                                 !device.getBusinessSystemName().trim().isEmpty())
                .collect(Collectors.groupingBy(DeviceGpuInfoDTO::getBusinessSystemName));

        // 计算每个业务系统的算力数据
        List<BusinessComputeRankingVO.BusinessComputeRankingItem> businessComputeStats = new ArrayList<>();
        
        for (Map.Entry<String, List<DeviceGpuInfoDTO>> entry : businessSystemGroups.entrySet()) {
            String businessSystemName = entry.getKey();
            List<DeviceGpuInfoDTO> devices = entry.getValue();
            
            BusinessComputeRankingVO.BusinessComputeRankingItem item = 
                    calculateBusinessComputeStats(businessSystemName, devices);
            
            if (item != null) {
                businessComputeStats.add(item);
            }
        }

        // 按总算力排序并取前10名
        List<BusinessComputeRankingVO.BusinessComputeRankingItem> computeRanking = businessComputeStats.stream()
                .filter(item -> item.getTotalCompute() != null && !item.getBusinessSystemName().equals("/"))
                .sorted((a, b) -> b.getAllocatedDeviceCount().compareTo(a.getAllocatedDeviceCount()))
                .limit(10)
                .collect(Collectors.toList());
        
        // 设置算力排名
        for (int i = 0; i < computeRanking.size(); i++) {
            computeRanking.get(i).setRanking(i + 1);
        }

        result.setComputeRanking(computeRanking);
        
        return result;
    }

    /**
     * 计算单个业务系统的算力统计数据
     */
    private BusinessComputeRankingVO.BusinessComputeRankingItem calculateBusinessComputeStats(
            String businessSystemName, List<DeviceGpuInfoDTO> devices) {
        
        // 按显卡型号分组统计
        Map<String, List<DeviceGpuInfoDTO>> modelGroups = devices.stream()
                .collect(Collectors.groupingBy(DeviceGpuInfoDTO::getModelName));

        BigDecimal totalCompute = BigDecimal.ZERO;
        BigDecimal allocatedCompute = BigDecimal.ZERO;
        int totalDeviceCount = 0;
        int allocatedDeviceCount = 0;
        Set<String> deptNames = new HashSet<>();
        
        // 计算每种型号的算力
        for (Map.Entry<String, List<DeviceGpuInfoDTO>> modelEntry : modelGroups.entrySet()) {
            String modelName = modelEntry.getKey();
            List<DeviceGpuInfoDTO> modelDevices = modelEntry.getValue();
            
            // 获取该型号的TFLOPS系数
            DeviceModelTFEnum modelTFEnum = DeviceModelTFEnum.getByModelName(modelName);
            Double tflops = modelTFEnum.getTflops();
            
            int modelTotalCount = modelDevices.size();
            long modelAllocatedCount = modelDevices.stream()
                    .filter(device -> "1".equalsIgnoreCase(device.getInUsed()))
                    .count();
            
            // 计算该型号的算力
            BigDecimal modelTotalCompute = BigDecimal.valueOf(modelTotalCount * tflops)
                    .setScale(2, RoundingMode.HALF_UP);
            BigDecimal modelAllocatedCompute = BigDecimal.valueOf(modelAllocatedCount * tflops)
                    .setScale(2, RoundingMode.HALF_UP);
            
            // 累加到总算力
            totalCompute = totalCompute.add(modelTotalCompute);
            allocatedCompute = allocatedCompute.add(modelAllocatedCompute);
            totalDeviceCount += modelTotalCount;
            allocatedDeviceCount += (int) modelAllocatedCount;
            
            // 收集部门信息
            modelDevices.stream()
                    .filter(device -> ObjNullUtils.isNotNull(device.getDeptName()))
                    .forEach(device -> deptNames.add(device.getDeptName()));
        }

        //去掉斜杠/
        deptNames.removeIf("/"::equals);
        //如果为空了就返回空
        if (deptNames.isEmpty()) {
            return null;
        }
        // 计算算力利用率（基于lastPeriod数据）
        BigDecimal computeUtilization = calculateComputeUtilization(devices);

        BusinessComputeRankingVO.BusinessComputeRankingItem item = 
                new BusinessComputeRankingVO.BusinessComputeRankingItem();
        item.setBusinessSystemName(businessSystemName);
        item.setTotalCompute(totalCompute);
        item.setAllocatedCompute(allocatedCompute);
        item.setAvailableCompute(totalCompute.subtract(allocatedCompute));
        item.setTotalDeviceCount(totalDeviceCount);
        item.setAllocatedDeviceCount(allocatedDeviceCount);
        item.setAvailableDeviceCount(totalDeviceCount - allocatedDeviceCount);
        item.setDeptName(String.join(", ", deptNames));
        item.setComputeUtilization(computeUtilization);

        return item;
    }

    /**
     * 计算业务系统的算力利用率
     */
    private BigDecimal calculateComputeUtilization(List<DeviceGpuInfoDTO> devices) {
        List<Double> validUtilizations = new ArrayList<>();
        
        for (DeviceGpuInfoDTO device : devices) {
            if (ObjNullUtils.isNotNull(device.getLastPeriod())) {
                try {
                    DeviceCardMetricsDTO metrics = JSONObject.parseObject(device.getLastPeriod(), DeviceCardMetricsDTO.class);
                    if (metrics != null && ObjNullUtils.isNotNull(metrics.getGpuUtilPercent())) {
                        validUtilizations.add(metrics.getGpuUtilPercent());
                    }
                } catch (Exception e) {
                    log.warn("解析设备{}的lastPeriod数据失败: {}", device.getDeviceId(), e.getMessage());
                }
            }
        }
        
        if (validUtilizations.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        Double avgUtilization = validUtilizations.stream()
                .mapToDouble(Double::doubleValue)
                .average()
                .orElse(0.0);
        
        return BigDecimal.valueOf(avgUtilization).setScale(2, RoundingMode.HALF_UP);
    }


    /**
     * 基于历史数据计算单个业务系统的统计数据
     */
    private BusinessSystemRankingVO.BusinessSystemRankingItem calculateBusinessSystemStatsFromHistory(
            String businessSystemName, List<DeviceCardMetricsDTO> metrics) {
        
        if (metrics.isEmpty()) {
            return null;
        }

        // 计算平均利用率
        Double avgMemoryUtilization = metrics.stream()
                .filter(m -> ObjNullUtils.isNotNull(m.getMemUtilpercent()))
                .mapToDouble(DeviceCardMetricsDTO::getMemUtilpercent)
                .average()
                .orElse(0.0);

        Double avgComputeUtilization = metrics.stream()
                .filter(m -> ObjNullUtils.isNotNull(m.getGpuUtilPercent()))
                .mapToDouble(DeviceCardMetricsDTO::getGpuUtilPercent)
                .average()
                .orElse(0.0);

        // 计算总任务数（取平均值）
        int totalTaskNum = (int) metrics.stream()
                .filter(m -> ObjNullUtils.isNotNull(m.getAllocationCount()))
                .mapToInt(DeviceCardMetricsDTO::getAllocationCount)
                .average()
                .orElse(0.0);

        // 获取设备数量（去重）
        long deviceCount = metrics.stream()
                .filter(m -> ObjNullUtils.isNotNull(m.getDeviceId()))
                .map(DeviceCardMetricsDTO::getDeviceId)
                .distinct()
                .count();

        // 收集部门名称
        Set<String> deptNames = new HashSet<>();
        for (DeviceCardMetricsDTO metric : metrics) {
            if (ObjNullUtils.isNotNull(metric.getDeptName())) {
                deptNames.add(metric.getDeptName());
            }
        }
        //去掉斜杠/
        deptNames.removeIf("/"::equals);
        //如果为空了就返回空
        if (deptNames.isEmpty()) {
            return null;
        }

        BusinessSystemRankingVO.BusinessSystemRankingItem item = 
                new BusinessSystemRankingVO.BusinessSystemRankingItem();
        item.setBusinessSystemName(businessSystemName);
        item.setMemoryUtilization(BigDecimal.valueOf(avgMemoryUtilization).setScale(2, RoundingMode.HALF_UP));
        item.setComputeUtilization(BigDecimal.valueOf(avgComputeUtilization).setScale(2, RoundingMode.HALF_UP));
        item.setTaskNum(totalTaskNum);
        item.setDeviceCount((int) deviceCount);
        item.setDeptName(String.join(", ", deptNames));

        return item;
    }

    /**
     * 在内存中关联设备信息和历史指标数据，计算业务系统利用率排行
     */
    private BusinessSystemRankingVO calculateBusinessSystemRankingWithDeviceInfo(
            List<DeviceCardMetricsDTO> historyMetrics, List<DeviceGpuInfoDTO> deviceInfoList,String modelName) {
        
        BusinessSystemRankingVO result = new BusinessSystemRankingVO();
//        if ("910B2".equals(modelName)||"910B4".equals(modelName)){
//            //需要特殊处理吧deviceInfoList 里的对应类型subModelName的设备ID集合拿出来，然后过滤historyMetrics
//            List<String> validDeviceIds = deviceInfoList.stream()
//                    .filter(device -> modelName.equals(device.getSubModelName()))
//                    .map(DeviceGpuInfoDTO::getDeviceId)
//                    .collect(Collectors.toList());
//            historyMetrics = historyMetrics.stream()
//                    .filter(metric -> validDeviceIds.contains(metric.getDeviceId()))
//                    .collect(Collectors.toList());
//        }

        // 构建设备ID到设备信息的映射
        Map<String, DeviceGpuInfoDTO> deviceInfoMap = StreamUtils.toMap(deviceInfoList, DeviceGpuInfoDTO::getDeviceId);
        
        // 为历史指标数据关联设备信息
        List<DeviceCardMetricsDTO> enrichedMetrics = new ArrayList<>();
        for (DeviceCardMetricsDTO metric : historyMetrics) {
            DeviceGpuInfoDTO deviceInfo = deviceInfoMap.get(metric.getDeviceId());
            if (deviceInfo != null && ObjNullUtils.isNotNull(deviceInfo.getBusinessSystemName()) 
                && !deviceInfo.getBusinessSystemName().trim().isEmpty()) {
                // 设置业务系统信息到指标数据中
                metric.setBusinessSystemName(deviceInfo.getBusinessSystemName());
                metric.setDeptName(deviceInfo.getDeptName());
                enrichedMetrics.add(metric);
            }
        }
        
        if (enrichedMetrics.isEmpty()) {
            log.info("关联后未找到有效的业务系统数据");
            return result;
        }
        
        // 按业务系统分组统计数据
        Map<String, List<DeviceCardMetricsDTO>> businessSystemGroups = enrichedMetrics.stream()
                .collect(Collectors.groupingBy(DeviceCardMetricsDTO::getBusinessSystemName));

        // 计算每个业务系统的平均利用率
        List<BusinessSystemRankingVO.BusinessSystemRankingItem> businessSystemStats = new ArrayList<>();
        
        for (Map.Entry<String, List<DeviceCardMetricsDTO>> entry : businessSystemGroups.entrySet()) {
            String businessSystemName = entry.getKey();
            List<DeviceCardMetricsDTO> metrics = entry.getValue();
            
            BusinessSystemRankingVO.BusinessSystemRankingItem item = 
                    calculateBusinessSystemStatsFromHistory(businessSystemName, metrics);
            
            if (item != null) {
                businessSystemStats.add(item);
            }
        }

        // 按显存利用率排序并取前5
        List<BusinessSystemRankingVO.BusinessSystemRankingItem> memoryRanking = businessSystemStats.stream()
                .filter(item -> item.getMemoryUtilization() != null && !item.getBusinessSystemName().equals("/"))
                .sorted((a, b) -> b.getMemoryUtilization().compareTo(a.getMemoryUtilization()))
                .limit(10000)
                .collect(Collectors.toList());
        
        // 设置显存利用率排名
        for (int i = 0; i < memoryRanking.size(); i++) {
            memoryRanking.get(i).setRanking(i + 1);
        }

        // 按算力利用率排序并取前5
        List<BusinessSystemRankingVO.BusinessSystemRankingItem> computeRanking = businessSystemStats.stream()
                .filter(item -> item.getComputeUtilization() != null && !item.getBusinessSystemName().equals("/"))
                .sorted((a, b) -> b.getComputeUtilization().compareTo(a.getComputeUtilization()))
                .limit(10000)
                .collect(Collectors.toList());
        
        // 设置算力利用率排名
        for (int i = 0; i < computeRanking.size(); i++) {
            computeRanking.get(i).setRanking(i + 1);
        }

        result.setMemoryUtilizationRanking(memoryRanking);
        result.setComputeUtilizationRanking(computeRanking);
        
        return result;
    }
}