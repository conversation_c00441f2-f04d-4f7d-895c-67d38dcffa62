package com.datatech.slgzt.controller.zs;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.config.DeptMapperProperties;
import com.datatech.slgzt.convert.ZsMapWebConvert;
import com.datatech.slgzt.enums.DeviceMetricSourceEnum;
import com.datatech.slgzt.manager.DeviceCardMetricsManager;
import com.datatech.slgzt.manager.DeviceGpuInfoManager;
import com.datatech.slgzt.manager.DeviceVirtualInfoManager;
import com.datatech.slgzt.manager.HashrateResInfoManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.dto.DeviceVirtualInfoDTO;
import com.datatech.slgzt.model.dto.HashrateResInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.model.query.DeviceMetricQuery;
import com.datatech.slgzt.model.query.HashrateResInfoQuery;
import com.datatech.slgzt.model.req.device.DeviceDomainPageReq;
import com.datatech.slgzt.model.req.device.DeviceGpuInfoExportReq;
import com.datatech.slgzt.model.vo.device.*;
import com.datatech.slgzt.utils.*;
import com.datatech.slgzt.warpper.PageWarppers;
import com.google.common.base.Splitter;
import com.google.common.collect.ArrayListMultimap;
import org.apache.commons.compress.utils.Lists;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 智算地图统一控制器
 *
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月16日 11:16:59
 */
@RestController
@RequestMapping("/zsMap")
public class ZsMapController {

    @Resource
    private DeviceGpuInfoManager deviceGupInfoManager;

    @Resource
    private DeviceCardMetricsManager deviceCardMetricsManager;

    @Resource
    private DeviceVirtualInfoManager deviceVirtualInfoManager;

    @Resource
    private HashrateResInfoManager hashrateResInfoManager;

    @Resource
    private DeptMapperProperties deptMapperProperties;

    @Resource
    private ZsMapWebConvert zsMapWebConvert;


    /**
     * 物理卡分布情况
     */
    @PostMapping("/devicePhysicalTypeAreaGroup")
    public CommonResult<List<DevicePhysicalTypeAreaGroupVO>> devicePhysicalTypeAreaGroup(@RequestBody DeviceDomainPageReq req) {
        ArrayList<DevicePhysicalTypeAreaGroupVO> rList = Lists.newArrayList();
        //查询所有物理卡
        List<DeviceGpuInfoDTO> deviceGpuInfoDTOS = deviceGupInfoManager.selectDeviceGpuInfoList(new DeviceInfoQuery()
                .setAreaCode(req.getAreaCode())
                .setDeptNameList(req.getDeptNameList())
        );
        //先按照区划分组
        ArrayListMultimap<String, DeviceGpuInfoDTO> areaCode2Data = StreamUtils.toArrayListMultimap(deviceGpuInfoDTOS, DeviceGpuInfoDTO::getAreaCode);
        for (String areaCode : areaCode2Data.keySet()) {
            DevicePhysicalTypeAreaGroupVO vo = new DevicePhysicalTypeAreaGroupVO();
            vo.setAreaCode(areaCode);
            vo.setModelName(areaCode2Data.get(areaCode)
                                         .stream()
                                         .map(DeviceGpuInfoDTO::getModelName)
                                         .filter(ObjNullUtils::isNotNull)
                                         .distinct()
                                         .collect(Collectors.toList()));
            rList.add(vo);
        }
        return CommonResult.success(rList);

    }

    @PostMapping("/totalPhysicalSlice")
    public CommonResult<List<DeviceTotalPhysicalVO>> totalPhysicalSlice(@RequestBody DeviceDomainPageReq req) {
        //获取所有物理卡的信息
        List<DeviceGpuInfoDTO> deviceGpuInfoDTOS = deviceGupInfoManager.selectDeviceGpuInfoList(new DeviceInfoQuery()
                .setAreaCode(req.getAreaCode())
                .setSliceStatus("1") //只查询物理卡
        );
        //按照modelName分组
        ArrayListMultimap<String, DeviceGpuInfoDTO> modelName2Data = StreamUtils.toArrayListMultimap(deviceGpuInfoDTOS, DeviceGpuInfoDTO::getModelName);
        List<DeviceTotalPhysicalVO> rList = new ArrayList<>();
        for (String modelname : modelName2Data.keySet()) {
            DeviceTotalPhysicalVO vo = new DeviceTotalPhysicalVO();
            List<DeviceGpuInfoDTO> deviceGpuInfoDTOS1 = modelName2Data.get(modelname);
            //把设备id整理出来
            List<String> deviceIdList = deviceGpuInfoDTOS1.stream()
                                                          .map(DeviceGpuInfoDTO::getDeviceId)
                                                          .filter(ObjNullUtils::isNotNull)
                                                          .distinct()
                                                          .collect(Collectors.toList());
            vo.setModelName(modelname);
            vo.setTotalCount(deviceGpuInfoDTOS1.size());
            List<DeviceVirtualInfoDTO> deviceVirtualInfoDTOS = deviceVirtualInfoManager.selectDeviceVirtualInfoList(new DeviceInfoQuery()
                    .setPhysicalDeviceIdList(deviceIdList)
                    .setAreaCode(req.getAreaCode())
            );
            vo.setAllocatedCount(deviceVirtualInfoDTOS.size());
            rList.add(vo);
        }
        return CommonResult.success(rList);
    }


    //获取部门映射关系
    @PostMapping("/departments/hierarchy")
    public CommonResult<JSONArray> deptMapper(@RequestBody DeviceDomainPageReq req) {
        String mapperStr = deptMapperProperties.getMapperStr();
        return CommonResult.success(JSONObject.parseArray(mapperStr));
    }



//    /**
//     * 获取数量详情按照类型分组
//     *
//     * @param
//     * @return
//     */
//    @PostMapping("/totalPhysical")
//    public CommonResult<List<DeviceTotalPhysicalVO>> queryDeviceComputeMetricPage(@RequestBody DeviceDomainPageReq deviceDomainPageReq) {
//        //获取所有物理卡的信息
//        List<DeviceGpuInfoDTO> deviceGpuInfoDTOS = deviceGupInfoManager.selectDeviceGpuInfoList(new DeviceInfoQuery()
//                .setAreaCode(deviceDomainPageReq.getAreaCode())
//                .setDeptNameList(deviceDomainPageReq.getDeptNameList())
//        );
//        //dataList还需按照类型再次分组
//        ArrayListMultimap<String, DeviceGpuInfoDTO> modelName2Data = StreamUtils.toArrayListMultimap(deviceGpuInfoDTOS, DeviceGpuInfoDTO::getModelName);
//        List<DeviceTotalPhysicalVO> modelList = Lists.newArrayList();
//        Set<String> modelNameSet = modelName2Data.keySet();
//        modelNameSet.removeIf(ObjNullUtils::isNull);
//        for (String modelName : modelNameSet) {
//            DeviceTotalPhysicalVO model = new DeviceTotalPhysicalVO();
//            List<DeviceGpuInfoDTO> dataList1 = modelName2Data.get(modelName);
//            int totalCount = dataList1.size();
//            long inUsedCount = dataList1.stream()
//                                        .filter(i -> i.getInUsed().equalsIgnoreCase("1"))
//                                        .count();
//            model.setModelName(modelName);
//            model.setTotalCount(totalCount);
//            model.setAllocatedCount((int) inUsedCount);
//            model.setAvailableCount(totalCount - (int) inUsedCount);
//            modelList.add(model);
//        }
//        return CommonResult.success(modelList);
//    }

    /**
     * 获取数量详情按照类型分组
     *
     * @param
     * @return
     */
    @PostMapping("/totalPhysical")
    public CommonResult<List<DeviceTotalPhysicalVO>> queryDeviceComputeMetricPage(@RequestBody DeviceDomainPageReq deviceDomainPageReq) {
        //获取所有物理卡的信息
        List<HashrateResInfoDTO> hashrateResInfoDTOS = hashrateResInfoManager.list(new HashrateResInfoQuery()
                .setModelName(deviceDomainPageReq.getModelName())
                .setAreaCode(deviceDomainPageReq.getAreaCode())
                .setDeptNameList(deviceDomainPageReq.getDeptNameList()));
        //dataList还需按照类型再次分组
        ArrayListMultimap<String, HashrateResInfoDTO> modelName2Data = StreamUtils.toArrayListMultimap(hashrateResInfoDTOS, HashrateResInfoDTO::getModel);
        List<DeviceTotalPhysicalVO> modelList = Lists.newArrayList();
        Set<String> modelNameSet = modelName2Data.keySet();
        modelNameSet.removeIf(ObjNullUtils::isNull);
        for (String modelName : modelNameSet) {
            DeviceTotalPhysicalVO model = new DeviceTotalPhysicalVO();
            List<HashrateResInfoDTO> dataList1 = modelName2Data.get(modelName);
            int totalCount = dataList1.size();
            long inUsedCount = dataList1.stream()
                                        .filter(i -> i.getAllocationStatus().equalsIgnoreCase("已分配"))
                                        .count();
            model.setModelName(modelName);
            model.setTotalCount(totalCount);
            model.setAllocatedCount((int) inUsedCount);
            model.setAvailableCount(totalCount - (int) inUsedCount);
            modelList.add(model);
        }
        return CommonResult.success(modelList);
    }


//    /**
//     * 总算力详情 按照类型分组的
//     *
//     * @return
//     */
//    @PostMapping("/totalCompute")
//    public CommonResult<DeviceComputeBaseInfoVO> totalCompute(@RequestBody DeviceInfoQuery query) {
//        DeviceComputeBaseInfoVO resultVO = new DeviceComputeBaseInfoVO();
//        List<DeviceComputeBaseInfoVO.Model> rModelList = Lists.newArrayList();
//        //获取所有物理卡的信息
//        List<DeviceGpuInfoDTO> deviceGpuInfoDTOS = deviceGupInfoManager.selectDeviceGpuInfoList(new DeviceInfoQuery()
//                .setAreaCode(query.getAreaCode())
//                .setDeptNameList(query.getDeptNameList())
//        );
//        //按照显卡类型区分
//        ArrayListMultimap<String, DeviceGpuInfoDTO> modelName2Data = StreamUtils.toArrayListMultimap(deviceGpuInfoDTOS, DeviceGpuInfoDTO::getModelName);
//        //区分出每个类型下的分配数
//        Set<String> modelNameSet = modelName2Data.keySet();
//        modelNameSet.removeIf(ObjNullUtils::isNull);
//        for (String modelName : modelNameSet) {
//            DeviceComputeBaseInfoVO.Model model = new DeviceComputeBaseInfoVO.Model();
//            List<DeviceGpuInfoDTO> dataList = modelName2Data.get(modelName);
//            int totalCount = dataList.size();
//            long inUsedCount = dataList.stream()
//                                       .filter(i -> i.getInUsed().equalsIgnoreCase("1"))
//                                       .count();
//            model.setModelName(modelName);
//            model.setTotalCount(totalCount);
//            model.setAllocatedCount((int) inUsedCount);
//            model.setAvailableCount(totalCount - (int) inUsedCount);
//            rModelList.add(model);
//        }
//        resultVO.setModelList(rModelList);
//        //获取所有的指标信息
//        return CommonResult.success(resultVO);
//    }

    /**
     * 总算力详情 按照类型分组的
     *
     * @return
     */
    @PostMapping("/totalCompute")
    public CommonResult<DeviceComputeBaseInfoVO> totalCompute(@RequestBody DeviceInfoQuery query) {
        DeviceComputeBaseInfoVO resultVO = new DeviceComputeBaseInfoVO();
        List<DeviceComputeBaseInfoVO.Model> rModelList = Lists.newArrayList();
        //获取所有物理卡的信息
        List<HashrateResInfoDTO> hashrateResInfoDTOS = hashrateResInfoManager.list(new HashrateResInfoQuery()
                .setModelName(query.getModelName())
                .setAreaCode(query.getAreaCode())
                .setDeptNameList(query.getDeptNameList()));
        //按照显卡类型区分
        ArrayListMultimap<String, HashrateResInfoDTO> modelName2Data = StreamUtils.toArrayListMultimap(hashrateResInfoDTOS, HashrateResInfoDTO::getModel);
        //区分出每个类型下的分配数
        Set<String> modelNameSet = modelName2Data.keySet();
        modelNameSet.removeIf(ObjNullUtils::isNull);
        for (String modelName : modelNameSet) {
            DeviceComputeBaseInfoVO.Model model = new DeviceComputeBaseInfoVO.Model();
            List<HashrateResInfoDTO> dataList = modelName2Data.get(modelName);
            int totalCount = dataList.size();
            long inUsedCount = dataList.stream()
                                       .filter(i -> i.getAllocationStatus().equalsIgnoreCase("已分配"))
                                       .count();
            model.setModelName(modelName);
            model.setTotalCount(totalCount);
            model.setAllocatedCount((int) inUsedCount);
            model.setAvailableCount(totalCount - (int) inUsedCount);
            rModelList.add(model);
        }
        resultVO.setModelList(rModelList);
        //获取所有的指标信息
        return CommonResult.success(resultVO);
    }


    /**
     * 获取算力业务排序对象
     * @param query
     * @return
     */
    @PostMapping("/queryDeviceRunSort")
    public CommonResult<List<DeviceRunSortVO>> queryDeviceRunSort(@RequestBody DeviceInfoQuery query) {
        //查询所有已经有数据的物理卡
        List<DeviceGpuInfoDTO> deviceGpuInfoDTOS = deviceGupInfoManager.selectDeviceGpuInfoList(new DeviceInfoQuery()
                .setAreaCode(query.getAreaCode())
                .setDeptNameList(query.getDeptNameList())
                .setGpuSort(true));
        //按照模块类型分组
        ArrayListMultimap<String, DeviceGpuInfoDTO> modelName2Data = StreamUtils.toArrayListMultimap(deviceGpuInfoDTOS, DeviceGpuInfoDTO::getModelName);
        List<DeviceRunSortVO> resultList = Lists.newArrayList();
        Set<String> modelNameSet = modelName2Data.keySet();
        modelNameSet.removeIf(ObjNullUtils::isNull);
        for (String modelName : modelNameSet) {
            DeviceRunSortVO deviceRunSortVO = new DeviceRunSortVO();
            deviceRunSortVO.setModelName(modelName);
            List<DeviceGpuInfoDTO> dataList = modelName2Data.get(modelName);
            //提取出DeviceGpuInfoDTO 里的lastPeriod字段 转成DeviceCardMetricsDTO 列表
            //取出第一个条 如果存在的话
            List<DeviceCardMetricsDTO> deviceCardMetricsDTOS = dataList.stream()
                                                                       .map(DeviceGpuInfoDTO::getLastPeriod)
                                                                       .filter(ObjNullUtils::isNotNull)
                                                                       .map(i -> JSONObject.parseObject(i, DeviceCardMetricsDTO.class))
                                                                       .filter(i -> ObjNullUtils.isNotNull(i.getGpuUtilPercent()))
                                                                       .sorted(Comparator.comparing(DeviceCardMetricsDTO::getGpuUtilPercent,Comparator.nullsLast(Comparator.naturalOrder()))
                                                                                         .thenComparing(DeviceCardMetricsDTO::getMemUtilpercent,Comparator.nullsLast(Comparator.naturalOrder()))
                                                                                         .reversed())
                                                                       .collect(Collectors.toList());
            //如果没有数据 则直接跳过
            if (deviceCardMetricsDTOS.isEmpty()){
                continue;
            }
            //按照显存利用率排序
            DeviceCardMetricsDTO deviceCardMetricsDTO = deviceCardMetricsDTOS.get(0);
            deviceRunSortVO.setMemUtilpercent(BigDecimal.valueOf(deviceCardMetricsDTO.getMemUtilpercent()==null?0:deviceCardMetricsDTO.getMemUtilpercent())
                                                        .setScale(2, RoundingMode.HALF_UP));
            deviceRunSortVO.setGpuUtilPercent(BigDecimal.valueOf(deviceCardMetricsDTO.getGpuUtilPercent()==null?0:deviceCardMetricsDTO.getGpuUtilPercent())
                                                        .setScale(2, RoundingMode.HALF_UP));
            DeviceGpuInfoDTO deviceGpuInfoDTO = deviceGupInfoManager.getByDeviceId(deviceCardMetricsDTO.getDeviceId());
            deviceRunSortVO.setBusinessName(deviceGpuInfoDTO.getBusinessSystemName());
            resultList.add(deviceRunSortVO);
        }
        //获取
        List<String> modelNameList = deviceGupInfoManager.groupModelName();
        modelNameList.removeIf(ObjNullUtils::isNull);
        //循环modelNameSet 如果没有给个默认的对象补齐
        for (String modelName : modelNameList) {
            if (!modelNameSet.contains(modelName)) {
                DeviceRunSortVO deviceRunSortVO = new DeviceRunSortVO();
                deviceRunSortVO.setModelName(modelName);
                deviceRunSortVO.setMemUtilpercent(null);
                deviceRunSortVO.setGpuUtilPercent(null);
                List<DeviceGpuInfoDTO> deviceGpuInfoDTOS1 = deviceGupInfoManager.selectDeviceGpuInfoList(new DeviceInfoQuery()
                        .setModelName(modelName)
                        .setInUsed("1")
                        .setAreaCode(query.getAreaCode()));
                DeviceGpuInfoDTO any = StreamUtils.findAny(deviceGpuInfoDTOS1, DeviceGpuInfoDTO::new);
                deviceRunSortVO.setBusinessName(any.getBusinessSystemName());
                resultList.add(deviceRunSortVO);
            }
        }
        return CommonResult.success(resultList);
    }


    /**
     * 智算显存、算力利用率 按照区划归类
     */
    @PostMapping("/queryDevicesMetricPercent")
    public CommonResult<List<DevicePhysicalRunStatusVO>> queryDevicesMetricPercent(@RequestBody DeviceInfoQuery query) {
        //如果部门不为空查询部门下的卡ID集合
        List<String> deviceIds = Lists.newArrayList();
        if (ObjNullUtils.isNotNull(query.getDeptNameList())) {
            List<DeviceGpuInfoDTO> deviceGpuInfoDTOS = deviceGupInfoManager.selectDeviceGpuInfoList(new DeviceInfoQuery()
                    .setDeptNameList(query.getDeptNameList()));
            deviceIds = deviceGpuInfoDTOS.stream().map(DeviceGpuInfoDTO::getDeviceId).filter(ObjNullUtils::isNotNull).collect(Collectors.toList());
        }
        //查询对象按照4个小时 区域分好
        List<DeviceCardMetricsDTO> deviceCardMetricsDTOS = deviceCardMetricsManager.queryAvgDeviceMetrics(new DeviceMetricQuery()
                .setStartTime(LocalDateTime.now().minusHours(4))
                .setEndTime(LocalDateTime.now())
                .setDeviceIds(deviceIds)
                .setAreaCode(query.getAreaCode())
                .setMetricSources(Arrays.asList(DeviceMetricSourceEnum.QD_PHYSICAL_SOURCE.getCode(), DeviceMetricSourceEnum.NPU_CHIP_SOURCE.getCode()))
        );
        //把区划空的过滤掉
        deviceCardMetricsDTOS = deviceCardMetricsDTOS.stream()
                                                     .filter(i -> ObjNullUtils.isNotNull(i.getAreaCode()))
                                                     .collect(Collectors.toList());
        //按照区分分组
        ArrayListMultimap<String, DeviceCardMetricsDTO> arrayListMultimap = StreamUtils.toArrayListMultimap(deviceCardMetricsDTOS, DeviceCardMetricsDTO::getAreaCode);
        ArrayList<DevicePhysicalRunStatusVO> resultList = Lists.newArrayList();
        Set<String> areaCodeSet = arrayListMultimap.keySet();
        for (String areaCode : areaCodeSet) {
            //这里取出来的包含这个区划下所有卡的指标
            List<DeviceCardMetricsDTO> metricsDTOS = arrayListMultimap.get(areaCode);
            //需要按照时间来划开 按照体统的GPUTime来分组计算
            ArrayListMultimap<String, DeviceCardMetricsDTO> gpuTime2Data = StreamUtils.toArrayListMultimap(metricsDTOS, DeviceCardMetricsDTO::getGpuTime);
            //计算平均的时间点
            DevicePhysicalRunStatusVO devicePhysicalRunStatusVO = new DevicePhysicalRunStatusVO();
            devicePhysicalRunStatusVO.setAreaCode(areaCode);
            List<DevicePhysicalRunStatusVO.Model> modelList = Lists.newArrayList();
            //计算每个时间点的平均值
            gpuTime2Data.keySet().forEach(gpuTime -> {
                List<DeviceCardMetricsDTO> deviceCardMetricsDTOS1 = gpuTime2Data.get(gpuTime);
                //计算每个时间点的平均值
                DevicePhysicalRunStatusVO.Model model = new DevicePhysicalRunStatusVO.Model();
                //计算平均算力利用率
                double gpuUtilPercent = deviceCardMetricsDTOS1.stream()
                                                              .mapToDouble(DeviceCardMetricsDTO::getGpuUtilPercent)
                                                              .average()
                                                              .orElse(0);
                //计算平均显存利用率
                double memUtilpercent = deviceCardMetricsDTOS1.stream()
                                                              .mapToDouble(DeviceCardMetricsDTO::getMemUtilpercent)
                                                              .average()
                                                              .orElse(0);
                //计算GPUTime对应的时间
                model.setTime(DateUtils.toLocalDateTime(gpuTime, "yyyyMMddHHmm"));
                model.setGpuUtilPercent(BigDecimal.valueOf(gpuUtilPercent).setScale(2, RoundingMode.HALF_UP));
                model.setMemUtilpercent(BigDecimal.valueOf(memUtilpercent).setScale(2, RoundingMode.HALF_UP));
                modelList.add(model);
            });
            //modeList需要按照时间排序
            modelList.sort(Comparator.comparing(DevicePhysicalRunStatusVO.Model::getTime));
            devicePhysicalRunStatusVO.setModelList(modelList);
            resultList.add(devicePhysicalRunStatusVO);
        }
        return CommonResult.success(resultList);
    }


    //业务查询物理卡列表数据
    @PostMapping("/pageDeviceGpuInfoList")
    public CommonResult<PageResult<DevicePagePhysicalVO>> pageDeviceGpuInfoList(@RequestBody DeviceInfoQuery query) {
        String modelName = query.getModelName();
        if (ObjNullUtils.isNotNull(modelName) && modelName.equals(query.getSubModelName())) {
            query.setSubModelName(null);
        }
        PageResult<DeviceGpuInfoDTO> page = deviceGupInfoManager.queryDeviceGupInfoPage(new DeviceInfoQuery()
                .setPageNum(query.getPageNum())
                .setGpuSort(true)
                .setAreaCode(query.getAreaCode())
                .setPageSize(query.getPageSize())
                .setModelName(query.getModelName())
                .setInUsed(query.getIsUsed())
                .setDeptNameList(query.getDeptNameList())
                .setBusinessSystemName(query.getBusinessSystemName())
                .setSubModelName(query.getSubModelName())
        );
        PageResult<DevicePagePhysicalVO> box = PageWarppers.box(page, zsMapWebConvert::convert);
        List<DevicePagePhysicalVO> records = box.getRecords();
        records.forEach(i -> {
            //查询当前时间的数据
            String lastPeriod = i.getLastPeriod();
            if (ObjNullUtils.isNull(lastPeriod)) {
                return;
            }
            //按照创建时间排序
            DeviceCardMetricsDTO deviceCardMetricsDTO = JSONObject.parseObject(lastPeriod, DeviceCardMetricsDTO.class);
            i.setGpuUtilPercent(ObjNullUtils.isNull(deviceCardMetricsDTO.getGpuUtilPercent()) ? null : BigDecimal.valueOf(deviceCardMetricsDTO.getGpuUtilPercent())
                                                                                                                 .setScale(2, RoundingMode.HALF_UP));
            i.setMemUtilpercent(ObjNullUtils.isNull(deviceCardMetricsDTO.getMemUtilpercent()) ? null : BigDecimal.valueOf(deviceCardMetricsDTO.getMemUtilpercent())
                                                                                                                 .setScale(2, RoundingMode.HALF_UP));
            i.setTaskNum(deviceCardMetricsDTO.getAllocationCount());
            i.setTemperature(ObjNullUtils.isNull(deviceCardMetricsDTO.getDevGpuTemp()) ? null : BigDecimal.valueOf(deviceCardMetricsDTO.getDevGpuTemp())
                                                                                                          .setScale(2, RoundingMode.HALF_UP));
        });
        return CommonResult.success(box);
    }

    //查询全量的卡包括未纳管的业务查询物理卡列表数据
    @PostMapping("/pageDeviceGpuInfoListAll")
    public CommonResult<PageResult<DeviceGpuInfoDTO>> pageDeviceGpuInfoListAll(@RequestBody DeviceInfoQuery query) {
//        List<String> subModelNames = ListUtil.toList("910B2", "910B4");
//        List<String> modelNames = query.getModelNames();
//        if (CollectionUtil.isNotEmpty(modelNames) && !subModelNames.contains(query.getSubModelName())) {
//            query.setSubModelName(null);
//        }
        PageResult<DeviceGpuInfoDTO> page = deviceGupInfoManager.queryDeviceGupInfoPage(new DeviceInfoQuery()
                .setPageNum(query.getPageNum())
                .setAreaCode(query.getAreaCode())
                .setAreaCodes(query.getAreaCodes())
                .setPageSize(query.getPageSize())
                .setInManage(query.getInManage())
                .setInUsed(query.getInUsed())
                .setModelNames(query.getModelNames())
                .setDeptNameList(query.getDeptNameList())
                .setBusinessSystemName(query.getBusinessSystemName())
                .setSubModelName(query.getSubModelName())
        );
//        PageResult<DevicePagePhysicalVO> box = PageWarppers.box(page, zsMapWebConvert::convert);
        return CommonResult.success(page);
    }

    @GetMapping("/deviceGpuDic")
    public CommonResult<List<String>> deviceGpuDic(@RequestParam String type,
                                                   String modelName,
                                                   String subModelName) {
        switch (type) {
            case "dept":
                return CommonResult.success(deviceGupInfoManager.groupDeptName(modelName, subModelName));
            case "business":
                return CommonResult.success(deviceGupInfoManager.groupBusinessSystemName(modelName, subModelName));
            default:
                return null;
        }
    }

    //查询数据指标列表
    @PostMapping("/deviceMetricsList")
    public CommonResult<Map<String, List<DeviceMetricsListVO>>> deviceMetricsList(@RequestBody DeviceInfoQuery query) {
        //检查开始时间和结束时间不能为空
        //如果开始时间为空强制是指为最近4个小时
        if (ObjNullUtils.isNull(query.getStartTime())) {
            query.setStartTime(LocalDateTime.now().minusHours(4));
            query.setEndTime(LocalDateTime.now());
        }
        Precondition.checkArgument(query.getDeviceId(), "deviceId不能为空");
        //设备id逗号拆分分批查询
        List<String> deviceIdList = Arrays.stream(query.getDeviceId().split(","))
                                          .map(String::trim)
                                          .collect(Collectors.toList());
        Map<String, List<DeviceMetricsListVO>> deviceId2Data = new HashMap<>();
        deviceIdList.forEach(i -> {
            List<DeviceCardMetricsDTO> deviceCardMetricsDTOS = deviceCardMetricsManager.selectGpuMetricsDTO(new DeviceMetricQuery()
                    .setStartTime(query.getStartTime())
                    .setEndTime(query.getEndTime())
                    .setDeviceId(i)
            );
            List<DeviceMetricsListVO> deviceMetricsListVOS = StreamUtils.mapArray(deviceCardMetricsDTOS, zsMapWebConvert::convert);
            //按照gpuTime排序
            deviceMetricsListVOS.sort(Comparator.comparing(DeviceMetricsListVO::getGpuTime));
            deviceId2Data.put(i, deviceMetricsListVOS);
        });
        return CommonResult.success(deviceId2Data);
    }


    //分页查询虚拟卡
    @PostMapping("/pageDeviceVirtualGpuInfoList")
    public CommonResult<PageResult<DevicePageVirtualVO>> pageDeviceVirtualGpuInfoList(@RequestBody DeviceInfoQuery query) {
        PageResult<DeviceVirtualInfoDTO> page = deviceVirtualInfoManager.queryVirtualDeviceInfoPage(new DeviceInfoQuery()
                .setPageNum(query.getPageNum())
                .setPageSize(query.getPageSize())
                .setGpuSort(true)
                .setModelName(query.getModelName())
        );
        PageResult<DevicePageVirtualVO> box = PageWarppers.box(page, zsMapWebConvert::convert);
        List<DevicePageVirtualVO> records = box.getRecords();
        records.forEach(i -> {
            //查询当前时间的数据
            String lastPeriod = i.getLastPeriod();
            if (ObjNullUtils.isNull(lastPeriod)) {
                return;
            }
            i.setBusinessSystemName("智能视频");
            //按照创建时间排序
            DeviceCardMetricsDTO deviceCardMetricsDTO = JSONObject.parseObject(lastPeriod, DeviceCardMetricsDTO.class);
            i.setGpuUtilPercent(ObjNullUtils.isNull(deviceCardMetricsDTO.getGpuUtilPercent()) ? null : BigDecimal.valueOf(deviceCardMetricsDTO.getGpuUtilPercent())
                                                                                                                 .setScale(2, RoundingMode.HALF_UP));
            i.setMemUtilpercent(ObjNullUtils.isNull(deviceCardMetricsDTO.getGpuUtilPercent()) ? null : BigDecimal.valueOf(deviceCardMetricsDTO.getMemUtilpercent())
                                                                                                                 .setScale(2, RoundingMode.HALF_UP));
            i.setTaskNum(deviceCardMetricsDTO.getAllocationCount());
        });
        return CommonResult.success(box);
    }


    /**
     * 通过Ip查询指标数据
     */
    @PostMapping("/queryDeviceMetricsByIp")
    public CommonResult<Map<String, List<DeviceMetricsListVO>>> queryDeviceMetricsByIp(@RequestBody DeviceInfoQuery query) {
        Precondition.checkArgument(query.getDncIp());
        List<DeviceGpuInfoDTO> deviceGpuInfoDTOS = deviceGupInfoManager.selectDeviceGpuInfoList(new DeviceInfoQuery()
                .setDncIp(query.getDncIp())
        );
        //过滤掉deviceId为空
        deviceGpuInfoDTOS = deviceGpuInfoDTOS.stream()
                                             .filter(Objects::nonNull)
                                             .filter(i -> ObjNullUtils.isNotNull(i.getDeviceId()))
                                             .collect(Collectors.toList());
        if (ObjNullUtils.isNull(deviceGpuInfoDTOS)) {
            return CommonResult.success(Collections.emptyMap());
        }
        List<String> deviceIdList = new ArrayList<>();
        for (DeviceGpuInfoDTO deviceGpuInfoDTO : deviceGpuInfoDTOS) {
            String deviceId = deviceGpuInfoDTO.getDeviceId();
            deviceIdList.addAll(Splitter.on(",").splitToList(deviceId));
        }
        if (ObjNullUtils.isNull(query.getStartTime())) {
            query.setStartTime(LocalDateTime.now().minusHours(4));
            query.setEndTime(LocalDateTime.now());
        }
        Map<String, List<DeviceMetricsListVO>> deviceId2Data = new HashMap<>();
        deviceIdList.forEach(i -> {
            List<DeviceCardMetricsDTO> deviceCardMetricsDTOS = deviceCardMetricsManager.selectGpuMetricsDTO(new DeviceMetricQuery()
                    .setStartTime(query.getStartTime())
                    .setEndTime(query.getEndTime())
                    .setDeviceId(i)
            );
            List<DeviceMetricsListVO> deviceMetricsListVOS = StreamUtils.mapArray(deviceCardMetricsDTOS, zsMapWebConvert::convert);
            deviceMetricsListVOS.sort(Comparator.comparing(DeviceMetricsListVO::getGpuTime));
            deviceId2Data.put(i, deviceMetricsListVOS);
        });
        return CommonResult.success(deviceId2Data);

    }

    /**
     * 导出GPU设备信息
     */
    @PostMapping("/exportDeviceGpuInfo")
    public void exportDeviceGpuInfo(@RequestBody DeviceGpuInfoExportReq req, HttpServletResponse response) {
        // 转换查询条件
        DeviceInfoQuery query = zsMapWebConvert.convert(req);
        query.setPageSize(99999); // 设置大页面大小以获取所有数据
        query.setPageNum(1);
        query.setGpuSort(true);
        query.setDeptNameList(req.getDeptNameList());

        // 查询设备数据
        List<DeviceGpuInfoDTO> deviceList = deviceGupInfoManager.selectDeviceGpuInfoList(query);
        // 处理实时指标数据
        List<DeviceGpuInfoExportVO> exportList = new ArrayList<>();
        deviceList.forEach(device -> {
            DeviceGpuInfoExportVO exportVO = zsMapWebConvert.convertToExportVO(device);
            // 处理lastPeriod中的实时数据
            String lastPeriod = device.getLastPeriod();
            if (ObjNullUtils.isNotNull(lastPeriod)) {
                try {
                    DeviceCardMetricsDTO metricsDTO = JSONObject.parseObject(lastPeriod, DeviceCardMetricsDTO.class);
                    if (metricsDTO != null) {
                        // 设置算力利用率
                        if (ObjNullUtils.isNotNull(metricsDTO.getGpuUtilPercent())) {
                            exportVO.setGpuUtilPercent(BigDecimal.valueOf(metricsDTO.getGpuUtilPercent())
                                                                 .setScale(2, RoundingMode.HALF_UP));
                        }
                        // 设置显存利用率
                        if (ObjNullUtils.isNotNull(metricsDTO.getMemUtilpercent())) {
                            exportVO.setMemUtilpercent(BigDecimal.valueOf(metricsDTO.getMemUtilpercent())
                                                                 .setScale(2, RoundingMode.HALF_UP));
                        }
                        // 设置任务数
                        exportVO.setTaskNum(metricsDTO.getAllocationCount());
                        // 设置温度
                        if (ObjNullUtils.isNotNull(metricsDTO.getDevGpuTemp())) {
                            exportVO.setTemperature(BigDecimal.valueOf(metricsDTO.getDevGpuTemp())
                                                              .setScale(2, RoundingMode.HALF_UP));
                        }
                    }
                } catch (Exception e) {
                    // 解析失败时忽略实时数据
                }
            }
            exportList.add(exportVO);
        });
        // 生成文件路径
        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/export/" + UUID.randomUUID().toString().replaceAll("-", "") + ".xlsx";
        // 确保导出目录存在
        File exportDir = new File(projectPath + "/export/");
        if (!exportDir.exists()) {
            exportDir.mkdirs();
        }
        // 导出Excel
        FileUtils.doExport(exportList, DeviceGpuInfoExportVO.class, filePath);
        // 下载文件
        downloadFile(response, filePath, "GPU设备信息列表.xlsx");
    }

    /**
     * 导出全量GPU设备信息（包括未纳管的）
     */
    @PostMapping("/exportDeviceGpuInfoAll")
    public void exportDeviceGpuInfoAll(@RequestBody DeviceGpuInfoExportReq req, HttpServletResponse response) {
        // 转换查询条件
        DeviceInfoQuery query = zsMapWebConvert.convert(req);
        query.setPageSize(99999); // 设置大页面大小以获取所有数据
        query.setPageNum(1);
        query.setGpuSort(true);
        query.setDeptNameList(req.getDeptNameList());
        // 注意：这里不设置gpuSort，因为pageDeviceGpuInfoListAll没有使用gpuSort

        // 设置全量查询特有的参数
        if (ObjNullUtils.isNotNull(req.getInUsed())) {
            query.setInUsed(req.getInUsed());
        }
        if (ObjNullUtils.isNotNull(req.getInManage())) {
            query.setInManage(req.getInManage());
        }

        // 查询设备数据（包括未纳管的）
        List<DeviceGpuInfoDTO> deviceList = deviceGupInfoManager.selectDeviceGpuInfoList(query);

        // 处理导出数据
        List<DeviceGpuInfoExportAllVO> exportList = new ArrayList<>();
        deviceList.forEach(device -> {
            DeviceGpuInfoExportAllVO exportVO = zsMapWebConvert.convertToExportAllVO(device);

            // 注意：pageDeviceGpuInfoListAll没有处理lastPeriod数据
            // 这里保持与原接口逻辑一致，不处理实时指标数据

            exportList.add(exportVO);
        });

        // 生成文件路径
        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/export/" + UUID.randomUUID().toString().replaceAll("-", "") + ".xlsx";

        // 确保导出目录存在
        File exportDir = new File(projectPath + "/export/");
        if (!exportDir.exists()) {
            exportDir.mkdirs();
        }

        // 导出Excel
        FileUtils.doExport(exportList, DeviceGpuInfoExportAllVO.class, filePath);

        // 下载文件
        downloadFile(response, filePath, "全量GPU设备信息列表.xlsx");
    }

    @PostMapping("/exportDeviceVirtualGpuInfo")
    public void exportDeviceVirtualGpuInfo(@RequestBody DeviceGpuInfoExportReq req, HttpServletResponse response) {
        // 转换查询条件
        DeviceInfoQuery query = zsMapWebConvert.convert(req);
        query.setPageSize(99999); // 设置大页面大小以获取所有数据
        query.setPageNum(1);
        query.setGpuSort(true);

        // 查询虚拟设备数据
        List<DeviceVirtualInfoDTO> deviceList = deviceVirtualInfoManager.selectDeviceVirtualInfoList(query);

        // 处理实时指标数据
        List<DevicePageVirtualVO> exportList = new ArrayList<>();
        deviceList.forEach(device -> {
            DevicePageVirtualVO exportVO = zsMapWebConvert.convert(device);
            exportVO.setBusinessSystemName("智能视频");

            // 处理lastPeriod中的实时数据
            String lastPeriod = device.getLastPeriod();
            if (ObjNullUtils.isNotNull(lastPeriod)) {
                try {
                    DeviceCardMetricsDTO deviceCardMetricsDTO = JSONObject.parseObject(lastPeriod, DeviceCardMetricsDTO.class);
                    if (deviceCardMetricsDTO != null) {
                        exportVO.setGpuUtilPercent(ObjNullUtils.isNull(deviceCardMetricsDTO.getGpuUtilPercent()) ? null : BigDecimal.valueOf(deviceCardMetricsDTO.getGpuUtilPercent())
                                                                                                                                    .setScale(2, RoundingMode.HALF_UP));
                        exportVO.setMemUtilpercent(ObjNullUtils.isNull(deviceCardMetricsDTO.getGpuUtilPercent()) ? null : BigDecimal.valueOf(deviceCardMetricsDTO.getMemUtilpercent())
                                                                                                                                    .setScale(2, RoundingMode.HALF_UP));
                        exportVO.setTaskNum(deviceCardMetricsDTO.getAllocationCount());
                    }
                } catch (Exception e) {
                    // 解析失败时忽略实时数据
                }
            }
            exportList.add(exportVO);
        });

        // 生成文件路径
        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/export/" + UUID.randomUUID().toString().replaceAll("-", "") + ".xlsx";

        // 确保导出目录存在
        File exportDir = new File(projectPath + "/export/");
        if (!exportDir.exists()) {
            exportDir.mkdirs();
        }

        // 导出Excel
        FileUtils.doExport(exportList, DeviceVirtualGpuInfoExportVO.class, filePath);

        // 下载文件
        downloadFile(response, filePath, "虚拟GPU设备信息列表.xlsx");
    }

    /**
     * 下载文件
     */
    private void downloadFile(HttpServletResponse response, String exportPath, String fileName) {
        try {
            // 以流的形式下载文件
            InputStream fis = new BufferedInputStream(Files.newInputStream(Paths.get(exportPath)));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();

            // 清空response
            response.reset();
            // 设置response响应头
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

            // 添加Content-Length
            File file = new File(exportPath);
            response.setContentLength((int) file.length());

            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(buffer);
            outputStream.flush();
            outputStream.close();

            // 删除临时文件
            Files.deleteIfExists(Paths.get(exportPath));
        } catch (IOException e) {
            // 记录日志但不抛出异常
        }
    }

}
