package com.datatech.slgzt.model.req.ccae;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class CcaeTaskCreateReq {
    /**
     * 任务名称
     */
    @NotBlank(message = "taskName不能为空")
    private String taskName;
    /**
     * 模板ID
     */
    @NotBlank(message = "templateResId不能为空")
    private String templateResId;
    /**
     * 计算节点列表(SN序列号)
     */
    private List<String> computeList;
    /**
     * 网络节点列表(ip列表)
     */
    private List<String> networkIpList;
    private String desc;
}
