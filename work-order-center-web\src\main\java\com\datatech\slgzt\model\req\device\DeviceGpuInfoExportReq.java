package com.datatech.slgzt.model.req.device;

import lombok.Data;

import java.util.List;

/**
 * GPU设备信息导出请求
 * <AUTHOR>
 */
@Data
public class DeviceGpuInfoExportReq {

    /**
     * 区域代码
     */
    private String areaCode;

    /**
     * 显卡型号
     */
    private String modelName;


    //modelNames
    private List<String> modelNames;

    /**
     * 设备ID
     */
    private String deviceId;

    private List<String> deptNameList;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 资源池名称
     */
    private String regionName;

    /**
     * 是否启用GPU排序
     */
    private Boolean gpuSort;

    /**
     * 分配状态
     */
    private String inUsed;

    /**
     * 是否纳管
     */
    private String inManage;
} 