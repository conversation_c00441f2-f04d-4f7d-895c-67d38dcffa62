package com.datatech.slgzt.model.req.moa;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.datatech.slgzt.model.vo.moa.ItemVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class ApproveReq {
    // {
    //  "userid": "用户OA账号，如zhengzq",
    //  "appid": "应用id",
    //  "moduleid": "当前要查看的模块id （只能传一个）",
    //  "apprivalid ": "审批单据id",
    //  " typeid ": "选项ID ",
    //  " typevalue ": "选项名称 ",
    //  "approvaldesc": "审批意见",
    //  " transition": "流程流向",
    //  "nextuser": "下一处理人",
    //  "globalparam": "全局参数。条件路由字段的值会包含在此",
    //  "otherparam": "局部参数",
    //  //审批过程中所有修改过的字段及字段值。
    //  "validatelist": [
    //    {
    //      "itemid": "属性id",
    //      "itemvalue": "属性值"
    //    }
    //  ],
    //  //审批过程中所有修改过的动态表格数据1.12版本新增。
    //  "modifyDatagridList": [
    //    {
    //      "id": "taskinfo接口输出的，该datagrid的id",
    //      "oriData": "taskinfo接口输出的，该datagrid的全部原始数据，json数组，key为各属性，包含sys_moaTempRowId",
    //      "newData": "原始数据，经增、删、改之后的全部新数据，json数组，key为各属性，包含sys_moaTempRowId",
    //      "insertRows": "新增的行，用sys_moaTempRowId表示，多行用逗号分隔",
    //      "deleteRows": "删除的行，用sys_moaTempRowId表示，多行用逗号分隔",
    //      "modifyRows": "修改的行，用sys_moaTempRowId表示，多行用逗号分隔"
    //    }
    //  ]
    //}
    /**
     * 用户OA账号，如zhengzq
     */
    @JsonProperty("userid")
    @JSONField(name = "userid")
    private String userId;
    /**
     * 应用id
     */
    @JsonProperty("appid")
    @JSONField(name = "appid")
    private String appId;
    /**
     * 当前要查看的模块id （只能传一个）
     */
    @JsonProperty("moduleid")
    @JSONField(name = "moduleid")
    private String moduleId;
    /**
     * 审批单据id
     */
    @JsonProperty("apprivalid")
    @JSONField(name = "apprivalid")
    private String apprivalId;
    /**
     * 选项ID
     */
    @JsonProperty("typeid")
    @JSONField(name = "typeid")
    private String typeId;
    /**
     * 选项名称
     */
    @JsonProperty("typevalue")
    @JSONField(name = "typevalue")
    private String typeValue;
    /**
     * 审批意见
     */
    @JsonProperty("approvaldesc")
    @JSONField(name = "approvaldesc")
    private String approvalDesc;
    /**
     * 流程流向
     */
    @JsonProperty("transition")
    @JSONField(name = "transition")
    private String transition;
    /**
     * 下一处理人
     */
    @JsonProperty("nextuser")
    @JSONField(name = "nextuser")
    private String nextUser;
    /**
     * 全局参数。条件路由字段的值会包含在此
     */
    @JsonProperty("globalparam")
    @JSONField(name = "globalparam")
    private JSONObject globalParam;
    /**
     * 局部参数
     */
    @JsonProperty("otherparam")
    @JSONField(name = "otherparam")
    private JSONObject otherParam;
    /**
     * 审批过程中所有修改过的字段及字段值。
     */
    @JsonProperty("validatelist")
    @JSONField(name = "validatelist")
    private List<ItemVO> validateList;
}
