package com.datatech.slgzt.model.req.moa;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.datatech.slgzt.model.vo.moa.ItemVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class GetDecisionReq {
    /**
     * 用户OA账号，如zhengzq
     */
    @JsonProperty("userid")
    @JSONField(name = "userid")
    private String userId;
    /**
     * 应用id
     */
    @JsonProperty("appid")
    @JSONField(name = "appid")
    private String appId;
    /**
     * 当前要查看的模块id （只能传一个）
     */
    @JsonProperty("moduleid")
    @JSONField(name = "moduleid")
    private String moduleId;
    /**
     * 审批单据id
     */
    @JsonProperty("apprivalid")
    @JSONField(name = "apprivalid")
    private String apprivalId;
    /**
     * 全局参数，在列表返回数据中获取
     */
    @JsonProperty("globalparam")
    @JSONField(name = "globalparam")
    private JSONObject globalParam;
    /**
     * 单独参数，在详细内容返回数据中获取
     */
    @JsonProperty("otherparam")
    @JSONField(name = "otherparam")
    private JSONObject otherParam;
    /**
     * 修改过的属性列表
     */
    @JsonProperty("modifylist")
    @JSONField(name = "modifylist")
    private List<ItemVO> modifyList;
}
