package com.datatech.slgzt.model.req.moa;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class GetFlowRecordReq {
    // {
    //	"userid": "用户OA账号，如zhengzq",
    //	"appid": "应用id",
    //	"moduleid": "当前要查看的模块id （只能传一个）",
    //	"apprivalid": "审批单据id",
    //	"globalparam": "全局参数，在列表返回数据中获取",
    //	"otherparam": "单独参数，在列表返回数据中获取"
    //}
    /**
     * 用户OA账号，如zhengzq
     */
    @JsonProperty("userid")
    @JSONField(name = "userid")
    private String userId;
    /**
     * 应用id
     */
    @JsonProperty("appid")
    @JSONField(name = "appid")
    private String appId;
    /**
     * 当前要查看的模块id （只能传一个）
     */
    @JsonProperty("moduleid")
    @JSONField(name = "moduleid")
    private String moduleId;
    /**
     * 审批单据id
     */
    @JsonProperty("apprivalid")
    @JSONField(name = "apprivalid")
    private String apprivalId;
    /**
     * 全局参数。条件路由字段的值会包含在此
     */
    @JsonProperty("globalparam")
    @JSONField(name = "globalparam")
    private JSONObject globalParam;
    /**
     * 局部参数
     */
    @JsonProperty("otherparam")
    @JSONField(name = "otherparam")
    private JSONObject otherParam;
}
