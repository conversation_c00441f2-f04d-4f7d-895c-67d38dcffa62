package com.datatech.slgzt.model.req.moa;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class GetTaskInfoReq {
    /**
     * 用户OA账号，如zhengzq
     */
    @JsonProperty("userid")
    @JSONField(name = "userid")
    private String userId;
    /**
     * 应用id
     */
    @JsonProperty("appid")
    @JSONField(name = "appid")
    private String appId;
    /**
     * 查看的模块id(支持1个或者多个模块ID，模块之间用"|"分隔)
     */
    @JsonProperty("moduleid")
    @JSONField(name = "moduleid")
    private String moduleId;
    /**
     * 当前页码
     */
    private Integer page;
    /**
     * 搜索关键字。兼容公文的sheetTitle。(模糊查询必须参数)。
     */
    private String keyword;
    /**
     * 暂未启用参数。备注(非必须参数)
     */
    private String usePriority;
    /**
     * 暂未启用参数。备注(非必须参数)
     */
    private String useNewest;
    /**
     * 每页大小
     */
    @JsonProperty("pagesize")
    @JSONField(name = "pagesize")
    private Integer pageSize;
}
