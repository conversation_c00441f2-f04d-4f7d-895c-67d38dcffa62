package com.datatech.slgzt.model.req.zs2map;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 卡类型利用率查询请求
 * <AUTHOR>
 * @description 查询指定时间区间内卡类型的显存利用率和算力利用率
 * @date 2025年 01月27日
 */
@Data
public class CardUtilizationReq {

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 聚合类型：day-天，month-月
     */
    private String aggregationType;

    /**
     * 卡类型/显卡型号
     */
    private String modelName;

    /**
     * 区域编码（可选）
     */
    private String areaCode;

     /**
     * 部门名称列表（可选）
     */
    private java.util.List<String> deptNameList;
}
