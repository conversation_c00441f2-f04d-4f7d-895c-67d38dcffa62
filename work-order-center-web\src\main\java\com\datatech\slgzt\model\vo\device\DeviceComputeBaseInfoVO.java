package com.datatech.slgzt.model.vo.device;

import com.datatech.slgzt.enums.DeviceModelTFEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/17 10:13
 * @description TODO
 */
@Data
@Accessors(chain = true)
public class DeviceComputeBaseInfoVO  implements Serializable {
    //算力总数
    private BigDecimal totalCompute;


    private List<Model> modelList;


    @Data
    public static class Model{

        //显卡类型
        private String modelName;

        //总算力
        private BigDecimal totalCompute;

        //已分配算力
        private BigDecimal allocatedCompute;
        //可用算力
        private BigDecimal availableCompute;

        //总数
        private Integer totalCount;

        //已分配数量
        private Integer allocatedCount;

        //可用数量
        private Integer availableCount;

        //总算力=totalCompute*计算因子
        public BigDecimal getTotalCompute() {
            DeviceModelTFEnum modelTFEnum = DeviceModelTFEnum.getByModelName(modelName);
            //获取计算因子
            Double tflops = modelTFEnum.getTflops();
            return new BigDecimal(totalCount*tflops).setScale(0, RoundingMode.HALF_UP);
        }

        //已分配算力=allocatedCompute*计算因子
        public BigDecimal getAllocatedCompute() {
            DeviceModelTFEnum modelTFEnum = DeviceModelTFEnum.getByModelName(modelName);
            //获取计算因子
            Double tflops = modelTFEnum.getTflops();
            return new BigDecimal(allocatedCount*tflops).setScale(0, RoundingMode.HALF_UP);
        }

        //可用算力=availableCompute*计算因子
        public BigDecimal getAvailableCompute() {
            DeviceModelTFEnum modelTFEnum = DeviceModelTFEnum.getByModelName(modelName);
            //获取计算因子
            Double tflops = modelTFEnum.getTflops();
            return new BigDecimal(availableCount*tflops).setScale(0, RoundingMode.HALF_UP);
        }
    }

    /**
     * 获取总算力
     *
     * @return
     */
    public BigDecimal getTotalCompute() {
        return modelList.stream()
                        .map(Model::getTotalCompute)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}