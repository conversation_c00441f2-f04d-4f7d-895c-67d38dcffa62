package com.datatech.slgzt.model.vo.device;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

import java.math.BigDecimal;

/**
 * GPU设备信息导出VO
 * <AUTHOR>
 */
@Data
public class DeviceGpuInfoExportAllVO {


    @ExcelExportHeader(value = "所属平台")
    private String regionName;

    @ExcelExportHeader(value = "归属部门")
    private String deptName;

    @ExcelExportHeader(value = "所属业务")
    private String businessSystemName;

    //主机名称
    @ExcelExportHeader(value = "主机名称")
    private String vmName;

    @ExcelExportHeader(value = "型号")
    private String modelName;


    @ExcelExportHeader(value = "设备类型")
    private String vmType;

    //DCN地址
    @ExcelExportHeader(value = "DCN地址")
    private String dcnNetAddr;

    //cpu
    @ExcelExportHeader(value = "CPU")
    private String vmCpu;

    //内存
    @ExcelExportHeader(value = "内存")
    private String vmMemory;

    //硬盘
    @ExcelExportHeader(value = "硬盘")
    private String vmDisk;


    //分配状态
    @ExcelExportHeader(value = "分配状态")
    private String inUsed;

    //是否管理
    @ExcelExportHeader(value = "纳管状态")
    private String inManage;




} 