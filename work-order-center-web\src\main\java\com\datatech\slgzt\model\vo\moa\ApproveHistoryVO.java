package com.datatech.slgzt.model.vo.moa;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class ApproveHistoryVO {

    // {
    //    "responsecode": "1",
    //    "resultlist": [
    //        {
    //            "tabid": "approvehistory",
    //            "type": "block",
    //            "tabname": "审批历史",
    //            "secondlist": [
    //                {
    //                    "thirdlist": [
    //                        {
    //                            "itemvalue": "拟搞人",
    //                            "itemname": "环节名称"
    //                        },
    //                        {
    //                            "itemvalue": "2",
    //                            "itemname": "状态"
    //                        },
    //                        {
    //                            "itemvalue": "方正",
    //                            "itemname": "待处理人"
    //                        },
    //                        {
    //                            "itemvalue": "郭伟琴",
    //                            "itemname": "已处理人"
    //                        },
    //                        {
    //                            "itemvalue": "2024-11-15 00:29:24",
    //                            "itemname": "到达时间"
    //                        },
    //                        {
    //                            "itemvalue": "2024-11-15 00:29:59",
    //                            "itemname": "处理时间"
    //                        },
    //                        {
    //                            "itemvalue": "送部门经理审核(手机)",
    //                            "itemname": "提交决策"
    //                        },
    //                        {
    //                            "itemvalue": "送部门经理审核",
    //                            "itemname": "处理意见"
    //                        },
    //                        {
    //                            "itemvalue": "cjfz",
    //                            "itemname": "待处理人OA"
    //                        },
    //                        {
    //                            "itemvalue": "wb_guoweiqin",
    //                            "itemname": "已处理人OA"
    //                        }
    //                    ],
    //                    "title": "拟搞人"
    //                },
    //                {
    //                    "thirdlist": [
    //                        {
    //                            "itemvalue": "部门经理审核",
    //                            "itemname": "环节名称"
    //                        },
    //                        {
    //                            "itemvalue": "2",
    //                            "itemname": "状态"
    //                        },
    //                        {
    //                            "itemvalue": "郭伟琴",
    //                            "itemname": "待处理人"
    //                        },
    //                        {
    //                            "itemvalue": "方正",
    //                            "itemname": "已处理人"
    //                        },
    //                        {
    //                            "itemvalue": "2024-11-15 00:29:59",
    //                            "itemname": "到达时间"
    //                        },
    //                        {
    //                            "itemvalue": "2024-11-17 23:36:58",
    //                            "itemname": "处理时间"
    //                        },
    //                        {
    //                            "itemvalue": "不同意(手机)",
    //                            "itemname": "提交决策"
    //                        },
    //                        {
    //                            "itemvalue": "不同意",
    //                            "itemname": "处理意见"
    //                        },
    //                        {
    //                            "itemvalue": "wb_guoweiqin",
    //                            "itemname": "待处理人OA"
    //                        },
    //                        {
    //                            "itemvalue": "cjfz",
    //                            "itemname": "已处理人OA"
    //                        }
    //                    ],
    //                    "title": "部门经理审核"
    //                }
    //            ]
    //        }
    //    ]
    //}
    @JsonProperty("tabid")
    private String tabId;
    @JsonProperty("type")
    private String type;
    @JsonProperty("tabname")
    private String tabName;
    @JsonProperty("secondlist")
    private List<SecondList> secondList;

    public ApproveHistoryVO() {
        this.tabId = "approvehistory";
        this.type = "block";
        this.tabName = "审批历史";
    }

    @Data
    public static class SecondList {
        @JsonProperty("thirdlist")
        private List<ItemVO> thirdList;
        @JsonProperty("title")
        private String title;
    }
}
