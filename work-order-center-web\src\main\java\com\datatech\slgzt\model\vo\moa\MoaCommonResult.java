package com.datatech.slgzt.model.vo.moa;

import com.datatech.slgzt.utils.TaceIdContext;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 返回对象
 */
@Data
public class MoaCommonResult<T> {
    /**
     * 0失败；1成功
     */
    @JsonProperty("responsecode")
    private String responseCode = "1";

    /**
     * 返回信息
     */
    @JsonProperty("responsemessage")
    private String responseMessage = "";

    @JsonProperty("pagesize")
    private Long pageSize;
    @JsonProperty("pageno")
    private Long pageNo;
    @JsonProperty("totalcount")
    private Long totalCount;

    @JsonProperty("globalparam")
    private String globalParam;

    @JsonProperty("otherparam")
    private String otherParam;

    @JsonProperty("resultlist")
    private T resultList;

    @JsonProperty("typelist")
    private Object typeList;

    public MoaCommonResult(Integer responseCode, String responseMessage, T resultList) {
        this.responseCode = responseCode.toString();
        this.responseMessage = responseMessage;
        this.resultList = resultList;
    }

    public String getTraceId() {
        return TaceIdContext.generateTraceIdGet();
    }

    public static <U> MoaCommonResult build(Integer success, String msg, U data) {
        return new MoaCommonResult(success, msg, data);
    }

    public static <U> MoaCommonResult success() {
        return build(1, "success", "");
    }

    public static <U> MoaCommonResult success(String message, final U data) {
        return build(1, message, data);
    }

    public static <U> MoaCommonResult<U> success(final U data) {
        return new MoaCommonResult<U>(1, "", data);
    }

    /**
     * 不能再手机审批
     */
    public static MoaCommonResult noMobile() {
        return build(2, "当前节点不能在手机中审批", "");
    }

    public static MoaCommonResult failure(final String message) {
        return new MoaCommonResult(0, message, "");
    }

    public static MoaCommonResult failure(final Integer code, final String message) {
        return new MoaCommonResult(0, message, "");
    }
}
