package com.datatech.slgzt.model.vo.moa;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @program: workordercenterproject
 * @description: 查询入参
 * @author: LK
 * @create: 2025-12-11 11:31
 **/
@Data
public class MoaCountReq {

    /**
     * oa用户
     */
    @JsonProperty("userid")
    @JSONField(name = "userid")
    private String userId;

    /**
     * 是否只返回总条数， true 只返回总数，false 同时返回分模块数量
     */
    @JsonProperty("isallcount")
    @JSONField(name = "isallcount")
    private Boolean isAllCount;

    /**
     * 系统编号=模块名1|模块名2
     * eg：slgzt=standardWorkOrder|recoveryWorkOrder|changeWorkOrder|nonStandardWorkOrder
     */
    @JsonProperty("appid-moduleid")
    @JSONField(name = "appid-moduleid")
    private String appidModuleId;
}
