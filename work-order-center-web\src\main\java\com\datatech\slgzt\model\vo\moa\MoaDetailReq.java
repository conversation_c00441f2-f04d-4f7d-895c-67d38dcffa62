package com.datatech.slgzt.model.vo.moa;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @program: workordercenterproject
 * @description: 详情入参
 * @author: LK
 * @create: 2025-12-12 10:35
 **/
@Data
public class MoaDetailReq {

    /**
     * 用户OA账号
     */
    @JsonProperty("userid")
    @JSONField(name = "userid")
    private String userId;
    
    /**
     * 应用id
     */
    @JsonProperty("appid")
    @JSONField(name = "appid")
    private String appId;
    
    /**
     * 模块id
     */
    @JsonProperty("moduleid")
    @JSONField(name = "moduleid")
    private String moduleId;
    
    /**
     * 工单id
     */
    @JsonProperty("apprivalid")
    @JSONField(name = "apprivalid")
    private String workOrderId;
    
    /**
     * 列表的全局级参数，在列表接口数据中获取
     */
    @JsonProperty("globalparam")
    @JSONField(name = "globalparam")
    private JSONObject globalParam;
    
    /**
     * 列表的文档级参数，在列表返回数据中获取
     */
    @JsonProperty("otherparam")
    @JSONField(name = "otherparam")
    private JSONObject otherParam;
}
