package com.datatech.slgzt.model.vo.moa;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TaskInfoVO {
    /**
     * 标题
     */
    private String title;
    /**
     * 流程管理平台的工单编号（注意，与业务自定义的文号之类不同)
     */
    @JsonProperty("applyid")
    private String applyId;
    /**
     * 订单id，即wrdp中的piid
     */
    @JsonProperty("apprivalid")
    private String apprivalId;
    /**
     * 其他请求参数，如果没有为空即可.
     * 后续请求中平台所需要的参数，原值返回即可
     */
    @JsonProperty("otherparam")
    private JSONObject otherParam = new JSONObject();
    /**
     * 节点名称
     */
    @JsonProperty("nodename")
    private String nodeName;
    /**
     * 模块名字
     */
    @JsonProperty("modulename")
    private String moduleName;
    /**
     * 模块ID，流程模版的编号
     */
    @JsonProperty("moduleid")
    private String moduleId;
    /**
     * 发起人
     */
    private String creater;
    /**
     * 非必须参数。该属性只对待办有效，对已办、待阅、已阅都无效。
     * 0表示正常待办 1表示处理中待办 2 表示超时失败待办。
     * 主要为了解决类似经营分析系统的因部分待办处理缓慢，需要80秒左右，进行异步批量审批后立即刷新列表时的展现问题。其他应用可无视此属性
     */
    private String treatStatus;
    /**
     * 最后修改时间，时间格式[YYYY-MM-DD hh:mm:ss]
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime date;
    /**
     * 非必须参数。是否可以批量操作。1 是 0 否
     */
    private String canBatchAgree;
}
