{"responsemessage": "", "globalparam": "", "resultlist": [{"tabid": "baseinfo", "tabname": "基础信息", "type": "block", "secondlist": [{"thirdlist": [{"ismore": "0", "type": "show", "cannull": "true", "itemid": "createdUserName", "itemvalue": "${createdUserName}", "itemname": "申请人", "editflag": "no", "itemText": "${createdUserName}"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "departmentName", "itemvalue": "${departmentName}", "itemname": "所属部门", "editflag": "no", "itemText": "${departmentName}"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "orderType", "itemvalue": "${orderType}", "itemname": "工单类型", "editflag": "no", "itemText": "${orderType}"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "orderTitle", "itemvalue": "${orderTitle}", "itemname": "标题", "editflag": "no", "itemText": "${orderTitle}"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "businessSystemName", "itemvalue": "${businessSystemName}", "itemname": "业务系统", "editflag": "no", "itemText": "${businessSystemName}"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "moduleName", "itemvalue": "${moduleName}", "itemname": "所属业务模块", "editflag": "no", "itemText": "${moduleName}"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "bureauUserName", "itemvalue": "${bureauUserName}", "itemname": "局方负责人", "editflag": "no", "itemText": "${bureauUserName}"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "manufacturer", "itemvalue": "${manufacturer}", "itemname": "厂家", "editflag": "no", "itemText": "${manufacturer}"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "manufacturerContacts", "itemvalue": "${manufacturerContacts}", "itemname": "厂家负责人", "editflag": "no", "itemText": "${manufacturerContacts}"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "manufacturerMobile", "itemvalue": "${manufacturerMobile}", "itemname": "电话", "editflag": "no", "itemText": "${manufacturerMobile}"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "businessDepartLeaderName", "itemvalue": "${businessDepartLeaderName}", "itemname": "二级业务部门领导", "editflag": "no", "itemText": "${businessDepartLeaderName}"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "levelThreeLeaderName", "itemvalue": "${levelThreeLeaderName}", "itemname": "三级业务部门领导", "editflag": "no", "itemText": "${levelThreeLeaderName}"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "orderDesc", "itemvalue": "${orderDesc}", "itemname": "资源申请说明", "editflag": "no", "itemText": "${orderDesc}"}]}, {"thirdlist": [{"ismore": "0", "type": "cardTitle", "cannull": "true", "itemid": "$$formTitle1", "itemvalue": "资源开通建议", "itemname": "资源开通建议", "itemText": "资源开通建议"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "cloud_resources", "itemvalue": "${cloud_resources}", "itemname": "云资源是否满足要求", "editflag": "no", "itemText": "${cloud_resources}"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "cloud_architecture", "itemvalue": "${cloud_architecture}", "itemname": "云化架构是否满足要求", "editflag": "no", "itemText": "${cloud_architecture}"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "business_architecture", "itemvalue": "${business_architecture}", "itemname": "业务架构是否合理", "editflag": "no", "itemText": "${business_architecture}"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "business_planning", "itemvalue": "${business_planning}", "itemname": "业务是否有规划设计", "editflag": "no", "itemText": "${business_planning}"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "catalogueDomainName", "itemvalue": "${catalogueDomainName}", "itemname": "云类型", "editflag": "no", "itemText": "${catalogueDomainName}"}, {"ismore": "0", "type": "show", "cannull": "true", "itemid": "domainName", "itemvalue": "${domainName}", "itemname": "云平台", "itemText": "${domainName}"}, {"ismore": "0", "type": "cardTitle", "cannull": "true", "itemid": "$$formTitle2", "itemvalue": "资源申请概览", "itemname": "资源申请概览", "itemText": "资源申请概览"}]}]}, {"tabname": "审批历史", "tabid": "approvehistory", "secondlist": [], "type": "block"}, {"tabid": "attachment", "tabname": "附件", "type": "list", "r_object_id": null, "attDirControl": []}]}